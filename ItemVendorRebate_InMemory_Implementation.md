# ItemVendorRebate In-Memory Implementation

This document explains the in-memory implementation for ItemVendorRebate operations, designed to optimize performance for frequently accessed rebate data.

## Overview

The in-memory implementation provides optimized filtering and calculation capabilities by loading all rebate data into memory and performing operations using Java Streams instead of database queries.

## Key Components

### 1. ItemVendorRebateInMemoryService
Main service class that provides in-memory operations with caching capabilities.

### 2. ItemVendorRebateApplicationService (Enhanced)
Updated to include in-memory methods for better performance.

### 3. REST API Endpoints
New endpoints that leverage in-memory processing for optimal performance.

## Performance Benefits

### Traditional Database Approach
```java
// Multiple database queries
List<ItemVendorRebate> rebates1 = repository.findByVendorItemId(id1);
List<ItemVendorRebate> rebates2 = repository.findByVendorItemId(id2);
List<ItemVendorRebate> rebates3 = repository.findByVendorItemId(id3);
// Result: 3 database round trips
```

### In-Memory Approach
```java
// Single database query + in-memory filtering
List<ItemVendorRebate> allRebates = repository.findAll(); // 1 DB query
List<ItemVendorRebate> rebates1 = allRebates.stream()
    .filter(r -> id1.equals(r.getVendorItemId()))
    .collect(Collectors.toList());
// Result: 1 database round trip + fast in-memory operations
```

## Features

### 1. Caching with TTL
- **Cache Duration**: 5 minutes TTL (configurable)
- **Automatic Refresh**: Cache refreshes automatically when expired
- **Manual Control**: Clear cache via API endpoint

### 2. Batch Processing
```java
// Get rebates for multiple vendor items in one operation
Map<UUID, List<ItemVendorRebate>> rebatesMap = 
    inMemoryService.getRebatesForVendorItemsBatch(vendorItemIds);
```

### 3. Optimized Calculations
```java
// Calculate rebate without additional database queries
BigDecimal totalRebate = inMemoryService.calculateTotalRebateForVendorItem(
    vendorItemId, quantity, date);
```

### 4. Active Rebate Filtering
```java
// Get only currently active rebates
List<ItemVendorRebate> activeRebates = 
    inMemoryService.getActiveRebatesForVendorItem(vendorItemId);
```

## API Endpoints

### Basic Operations
```http
# Get all rebates for vendor item (in-memory)
GET /api/v1/item-vendor-rebates/vendor-item/{vendorItemId}/in-memory

# Get only active rebates
GET /api/v1/item-vendor-rebates/vendor-item/{vendorItemId}/in-memory?activeOnly=true
```

### Batch Operations
```http
# Get rebates for multiple vendor items
POST /api/v1/item-vendor-rebates/vendor-item/batch
Content-Type: application/json

["vendorItemId1", "vendorItemId2", "vendorItemId3"]
```

### Calculations
```http
# Calculate total rebate for vendor item
GET /api/v1/item-vendor-rebates/vendor-item/{vendorItemId}/calculate-rebate?quantity=100&date=2024-06-15

# Get highest rebate per unit
GET /api/v1/item-vendor-rebates/vendor-item/{vendorItemId}/highest-rebate?date=2024-06-15

# Check if has active rebates
GET /api/v1/item-vendor-rebates/vendor-item/{vendorItemId}/has-active
```

### Cache Management
```http
# Clear cache
POST /api/v1/item-vendor-rebates/cache/clear

# Get cache statistics
GET /api/v1/item-vendor-rebates/cache/stats
```

## Usage Examples

### 1. Single Vendor Item Query
```java
@Autowired
private ItemVendorRebateInMemoryService inMemoryService;

// Get all rebates for a vendor item
List<ItemVendorRebate> rebates = inMemoryService.getRebatesForVendorItem(vendorItemId);

// Get only active rebates
List<ItemVendorRebate> activeRebates = inMemoryService.getActiveRebatesForVendorItem(vendorItemId);
```

### 2. Batch Processing
```java
// Process multiple vendor items efficiently
List<UUID> vendorItemIds = Arrays.asList(id1, id2, id3);
Map<UUID, List<ItemVendorRebate>> rebatesMap = 
    inMemoryService.getRebatesForVendorItemsBatch(vendorItemIds);

// Process each vendor item's rebates
rebatesMap.forEach((vendorItemId, rebates) -> {
    // Process rebates for this vendor item
    processRebates(vendorItemId, rebates);
});
```

### 3. Rebate Calculations
```java
// Calculate total rebate amount
BigDecimal quantity = new BigDecimal("100");
LocalDate date = LocalDate.of(2024, 6, 15);
BigDecimal totalRebate = inMemoryService.calculateTotalRebateForVendorItem(
    vendorItemId, quantity, date);

// Get highest rebate per unit
BigDecimal highestRebate = inMemoryService.getHighestRebatePerUnitForVendorItem(
    vendorItemId, date);
```

### 4. Business Logic Checks
```java
// Check if vendor item has any active rebates
boolean hasActive = inMemoryService.hasActiveRebates(vendorItemId);

// Get rebates expiring soon
List<ItemVendorRebate> expiring = inMemoryService.getExpiringRebatesForVendorItem(
    vendorItemId, 30); // Next 30 days
```

## Performance Considerations

### When to Use In-Memory
✅ **Good for:**
- Frequent queries for the same data
- Batch processing multiple vendor items
- Complex calculations involving multiple rebates
- Real-time rebate validations
- Dashboard and reporting queries

❌ **Not ideal for:**
- Very large datasets (>10,000 rebates)
- Infrequent queries
- Write-heavy operations
- When data consistency is critical

### Memory Usage
- **Typical rebate record**: ~200 bytes
- **10,000 rebates**: ~2MB memory
- **Cache overhead**: ~20% additional memory

### Cache Strategy
- **TTL**: 5 minutes (configurable)
- **Refresh**: Automatic on expiry
- **Invalidation**: Manual via API or after write operations

## Configuration

### Cache Settings
```java
// In ItemVendorRebateInMemoryService
private static final long CACHE_TTL_MS = 5 * 60 * 1000; // 5 minutes
```

### Spring Cache Configuration
```java
@Cacheable(value = "vendorItemRebates", key = "#vendorItemId")
public List<ItemVendorRebate> getRebatesForVendorItem(UUID vendorItemId) {
    // Implementation
}
```

## Monitoring

### Cache Statistics
```json
{
  "cacheSize": 1,
  "lastCacheUpdate": 1640995200000,
  "cacheAge": 150000,
  "cacheTtl": 300000
}
```

### Performance Metrics
- **Cache Hit Rate**: Monitor via Spring Boot Actuator
- **Memory Usage**: JVM heap monitoring
- **Response Times**: Compare in-memory vs database queries

## Best Practices

### 1. Cache Warming
```java
// Warm up cache on application startup
@EventListener(ApplicationReadyEvent.class)
public void warmUpCache() {
    inMemoryService.warmUpCache();
}
```

### 2. Cache Invalidation
```java
// Clear cache after write operations
@Transactional
public ItemVendorRebateDto createRebate(CreateItemVendorRebateCommand command) {
    ItemVendorRebateDto result = // ... create rebate
    inMemoryService.clearCache(); // Invalidate cache
    return result;
}
```

### 3. Error Handling
```java
try {
    List<ItemVendorRebate> rebates = inMemoryService.getRebatesForVendorItem(vendorItemId);
} catch (Exception e) {
    // Fallback to database query
    log.warn("In-memory service failed, falling back to database", e);
    rebates = repository.findByVendorItemId(vendorItemId);
}
```

## Migration Strategy

### Phase 1: Parallel Implementation
- Keep existing database queries
- Add in-memory endpoints with `/in-memory` suffix
- Monitor performance and accuracy

### Phase 2: Gradual Migration
- Update high-frequency endpoints to use in-memory
- Keep database fallback for critical operations
- Monitor cache hit rates and performance

### Phase 3: Full Migration
- Replace database queries with in-memory operations
- Remove redundant endpoints
- Optimize cache configuration based on usage patterns

This in-memory implementation provides significant performance improvements for read-heavy rebate operations while maintaining data consistency and providing flexible caching strategies.
