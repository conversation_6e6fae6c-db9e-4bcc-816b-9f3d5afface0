# ItemVendorRebate Overlap Detection Logic

This document explains the corrected overlap detection logic for ItemVendorRebate entities, addressing the issue where the previous implementation only checked for exact start date matches instead of proper date range overlaps.

## Problem Statement

The original implementation used `existsByVendorIdAndItemIdAndStartDate` which only checked if a rebate with the same start date existed. This approach failed to detect overlapping date ranges, which is the actual business requirement.

**Example of the problem:**
- Existing rebate: 2024-01-01 to 2024-06-30
- New rebate: 2024-05-01 to 2024-08-31
- Original logic: ✅ Allowed (different start dates)
- Correct logic: ❌ Should be rejected (overlapping periods)

## Correct Overlap Detection Logic

Two date ranges overlap if:
```
inputStartDate < dbEndDate AND inputEndDate > dbStartDate
```

This formula handles all overlap scenarios:

### Scenario 1: New rebate starts before existing ends
```
Existing: |-------|     (2024-01-01 to 2024-06-30)
New:           |-------|  (2024-05-01 to 2024-08-31)
Result: OVERLAP ✓
```

### Scenario 2: New rebate ends after existing starts
```
Existing:      |-------|  (2024-05-01 to 2024-08-31)
New:      |-------|       (2024-01-01 to 2024-06-30)
Result: OVERLAP ✓
```

### Scenario 3: New rebate completely contains existing
```
Existing:   |---|         (2024-03-01 to 2024-06-30)
New:      |-------|       (2024-01-01 to 2024-08-31)
Result: OVERLAP ✓
```

### Scenario 4: New rebate completely contained by existing
```
Existing: |-------|       (2024-01-01 to 2024-08-31)
New:        |---|         (2024-03-01 to 2024-06-30)
Result: OVERLAP ✓
```

### Scenario 5: No overlap (adjacent periods)
```
Existing: |---|           (2024-01-01 to 2024-03-31)
New:           |---|      (2024-04-01 to 2024-06-30)
Result: NO OVERLAP ✓
```

## Special Handling for Continuous Rebates

Continuous rebates have `endDate = null`, meaning they are valid indefinitely. Special logic is needed:

### Case 1: Two continuous rebates
```
Existing: 2024-01-01 to null (continuous)
New:      2024-06-01 to null (continuous)
Result: OVERLAP ✓ (can't have two continuous rebates)
```

### Case 2: Continuous vs Fixed period
```
Existing: 2024-01-01 to null (continuous)
New:      2024-06-01 to 2024-12-31 (fixed)
Result: OVERLAP ✓ (continuous overlaps with any future period)
```

### Case 3: Fixed period vs Continuous
```
Existing: 2024-06-01 to 2024-12-31 (fixed)
New:      2024-01-01 to null (continuous)
Result: OVERLAP ✓ (continuous overlaps with existing period)
```

### Case 4: Continuous starts after fixed ends
```
Existing: 2024-01-01 to 2024-03-31 (fixed)
New:      2024-04-01 to null (continuous)
Result: NO OVERLAP ✓ (continuous starts after fixed ends)
```

## Implementation

### JPA Query
```sql
SELECT CASE WHEN COUNT(r) > 0 THEN true ELSE false END 
FROM ItemVendorRebateDo r 
WHERE r.vendorId = :vendorId AND r.itemId = :itemId 
AND (
    -- Case 1: Both are continuous (not allowed)
    (:endDate IS NULL AND r.endDate IS NULL) 
    OR 
    -- Case 2: New is continuous, existing is fixed
    (:endDate IS NULL AND r.endDate IS NOT NULL AND r.endDate > :startDate) 
    OR 
    -- Case 3: New is fixed, existing is continuous
    (:endDate IS NOT NULL AND r.endDate IS NULL AND r.startDate < :endDate) 
    OR 
    -- Case 4: Both are fixed periods
    (:endDate IS NOT NULL AND r.endDate IS NOT NULL AND r.startDate < :endDate AND r.endDate > :startDate)
)
```

### Repository Interface
```java
boolean existsOverlappingRebates(UUID vendorId, UUID itemId, LocalDate startDate, LocalDate endDate);
```

### Service Layer
```java
public boolean validateRebateSchedule(UUID vendorId, UUID itemId, LocalDate startDate, LocalDate endDate) {
    if (itemVendorRebateRepository.existsOverlappingRebates(vendorId, itemId, startDate, endDate)) {
        log.warn("Found overlapping rebates for vendorId: {}, itemId: {}, period: {} to {}", 
                 vendorId, itemId, startDate, endDate);
        return false;
    }
    return true;
}
```

## Test Cases

### Unit Tests
- No overlap scenarios
- All overlap scenarios
- Continuous rebate handling
- Different vendor/item combinations

### Integration Tests
- JPA query validation
- Database constraint testing
- Edge cases with null values

## Migration Notes

### Breaking Changes
- `validateRebateSchedule` method signature changed from `(UUID vendorItemId, LocalDate startDate, LocalDate endDate)` to `(UUID vendorId, UUID itemId, LocalDate startDate, LocalDate endDate)`
- New method `existsOverlappingRebates` added to repository interfaces

### Backward Compatibility
- Old `existsByVendorIdAndItemIdAndStartDate` method is kept for exact start date checks if needed
- Application service maintains backward compatibility through method overloading

## Business Impact

### Before Fix
- Multiple overlapping rebates could be created for the same vendor-item combination
- Potential for conflicting rebate calculations
- Data integrity issues

### After Fix
- Proper validation prevents overlapping rebate periods
- Clear business rules enforcement
- Consistent rebate calculations
- Support for continuous rebates (no end date)

## Examples

### Valid Rebate Creation
```json
// Existing rebate: 2024-01-01 to 2024-03-31
// New rebate: 2024-04-01 to 2024-06-30 ✅ Allowed
{
  "vendorId": "uuid",
  "itemId": "uuid",
  "startDate": "2024-04-01",
  "endDate": "2024-06-30",
  "rebatePerUnit": 5.00
}
```

### Invalid Rebate Creation (Overlap)
```json
// Existing rebate: 2024-01-01 to 2024-06-30
// New rebate: 2024-05-01 to 2024-08-31 ❌ Rejected
{
  "vendorId": "uuid",
  "itemId": "uuid", 
  "startDate": "2024-05-01",
  "endDate": "2024-08-31",
  "rebatePerUnit": 3.00
}
```

### Continuous Rebate
```json
// Valid continuous rebate (no existing rebates)
{
  "vendorId": "uuid",
  "itemId": "uuid",
  "startDate": "2024-01-01",
  "endDate": null,  // Continuous
  "rebatePerUnit": 2.50
}
```

This implementation ensures proper business rule enforcement while maintaining flexibility for continuous rebates and clear validation messages for users.
