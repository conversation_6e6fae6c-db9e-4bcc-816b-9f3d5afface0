package com.mercaso.data.master_catalog.controller.v1;

import static com.mercaso.data.master_catalog.constants.MasterCatalogBatchJobConstants.DEFAULT_BATCH_SIZE;

import com.mercaso.data.master_catalog.service.impl.MasterCatalogBatchJobServiceImpl;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/master-catalog/v1/batch-jobs")
@RequiredArgsConstructor
@Validated
public class MasterCatalogBatchJobController {

    private final MasterCatalogBatchJobServiceImpl batchJobService;

    @PreAuthorize("hasAnyAuthority('master-catalog:write:batch-jobs')")
    @PostMapping("/start")
    public ResponseEntity<Void> startProcessing(
        @RequestParam(value = "batchSize", defaultValue = DEFAULT_BATCH_SIZE) Integer batchSize) {
        batchJobService.startInitialProcessing(batchSize);
        return ResponseEntity.ok().build();
    }
}
