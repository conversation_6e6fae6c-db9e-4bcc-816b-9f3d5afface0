package com.mercaso.data.master_catalog.entity;

import com.mercaso.data.master_catalog.dto.MasterCatalogBatchJobDto;
import com.mercaso.data.master_catalog.enums.MasterCatalogBatchJobStatus;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.Table;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import java.time.Instant;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.SuperBuilder;

/**
 * Entity referring to {@link MasterCatalogBatchJobDto}
 */
@SuperBuilder
@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
@Entity
@Table(name = "mc_batch_job", schema = "public")
public class MasterCatalogBatchJob extends BaseEntity {
    @NotNull
    @Column(name = "status", nullable = false)
    @Enumerated(EnumType.STRING)
    private MasterCatalogBatchJobStatus status;

    @Column(name = "completed_at")
    private Instant completedAt;

}