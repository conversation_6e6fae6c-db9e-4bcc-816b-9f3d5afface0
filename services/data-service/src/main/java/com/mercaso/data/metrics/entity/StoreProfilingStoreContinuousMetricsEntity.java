package com.mercaso.data.metrics.entity;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Entity
@Data
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "store_profiling_store_continuous_metrics", schema = "public")
public class StoreProfilingStoreContinuousMetricsEntity {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "store_id", nullable = false)
    private String storeId;

    @Column(name = "metric_category", nullable = false)
    private String metricCategory;

    @Column(name = "metric_name", nullable = false)
    private String metricName;

    @Column(name = "metric_desc", nullable = false)
    private String metricDesc;

    @Column(name = "metric_value", nullable = false, precision = 10, scale = 2)
    private BigDecimal metricValue;

    @Column(name = "metric_date", nullable = false)
    private LocalDateTime metricDate;

    @Column(name = "metric_date_type", nullable = false)
    private String metricDateType;
}
