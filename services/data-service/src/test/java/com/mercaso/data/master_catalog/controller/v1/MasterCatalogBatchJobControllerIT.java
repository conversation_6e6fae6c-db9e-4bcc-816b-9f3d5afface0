package com.mercaso.data.master_catalog.controller.v1;

import static com.mercaso.data.master_catalog.utils.entity.MasterCatalogRawDataUtils.buildMasterCatalogRawData;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.springframework.http.HttpStatus.OK;

import com.mercaso.data.AbstractIT;
import com.mercaso.data.master_catalog.adaptor.ExternalApiAdapter;
import com.mercaso.data.master_catalog.entity.MasterCatalogRawData;
import com.mercaso.data.master_catalog.enums.RawDataStatus;
import com.mercaso.data.master_catalog.repository.MasterCatalogRawDataRepository;
import com.mercaso.data.master_catalog.utils.resource_utils.MasterCatalogBatchJobResourceApiUtils;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.ResponseEntity;

@Slf4j
class MasterCatalogBatchJobControllerIT extends AbstractIT {

    @Autowired
    private MasterCatalogBatchJobResourceApiUtils masterCatalogBatchJobResourceApiUtils;

    @Autowired
    private MasterCatalogRawDataRepository masterCatalogRawDataRepository;

    @MockBean
    private ExternalApiAdapter externalApiAdapter;

    private List<MasterCatalogRawData> testData;

    @BeforeEach
    void setUp() {
        masterCatalogRawDataRepository.deleteAllInBatch();

        testData = new ArrayList<>();
        for (int i = 0; i < 3; i++) {
            MasterCatalogRawData rawData = buildMasterCatalogRawData(UUID.randomUUID());
            rawData.setStatus(RawDataStatus.DRAFT.name());
            testData.add(rawData);
        }
        testData = masterCatalogRawDataRepository.saveAll(testData);
    }

    @Test
    void startProcessing() {
        ResponseEntity<Void> response = masterCatalogBatchJobResourceApiUtils.startProcessing();

        assertEquals(OK, response.getStatusCode());
    }
}