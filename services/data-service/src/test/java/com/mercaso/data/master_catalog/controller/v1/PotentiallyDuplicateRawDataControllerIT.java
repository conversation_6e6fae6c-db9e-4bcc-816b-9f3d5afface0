package com.mercaso.data.master_catalog.controller.v1;

import static com.mercaso.data.master_catalog.utils.entity.MasterCatalogPotentiallyDuplicateRawDataUtils.buildMasterCatalogPotentiallyDuplicateRawData;
import static com.mercaso.data.master_catalog.utils.entity.MasterCatalogRawDataUtils.buildMasterCatalogRawData;
import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.AssertionsForClassTypes.assertThatThrownBy;
import static org.mockito.Mockito.verify;

import com.mercaso.data.AbstractIT;
import com.mercaso.data.dto.CustomPage;
import com.mercaso.data.master_catalog.adaptor.impl.PineconeApiAdapterImpl;
import com.mercaso.data.master_catalog.dto.MasterCatalogPotentiallyDuplicateRawDataDto;
import com.mercaso.data.master_catalog.dto.MasterCatalogPotentiallyDuplicateRawDataUpdateRequest;
import com.mercaso.data.master_catalog.entity.MasterCatalogBatchJob;
import com.mercaso.data.master_catalog.entity.MasterCatalogPotentiallyDuplicateRawData;
import com.mercaso.data.master_catalog.entity.MasterCatalogRawData;
import com.mercaso.data.master_catalog.entity.MasterCatalogTask;
import com.mercaso.data.master_catalog.enums.MasterCatalogTaskStatus;
import com.mercaso.data.master_catalog.enums.MasterCatalogTaskType;
import com.mercaso.data.master_catalog.enums.PotentiallyDuplicateRawDataStatus;
import com.mercaso.data.master_catalog.enums.MasterCatalogBatchJobStatus;
import com.mercaso.data.master_catalog.event.applicationevent.publisher.ApplicationEventPublisherProvider;
import com.mercaso.data.master_catalog.repository.MasterCatalogPotentiallyDuplicateRawDataRepository;
import com.mercaso.data.master_catalog.repository.MasterCatalogBatchJobRepository;
import com.mercaso.data.master_catalog.repository.MasterCatalogRawDataRepository;
import com.mercaso.data.master_catalog.repository.MasterCatalogTaskRepository;
import com.mercaso.data.master_catalog.utils.resource_utils.PotentiallyDuplicateRawDataResourceApiUtils;
import java.util.UUID;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.boot.test.mock.mockito.SpyBean;

@Slf4j
class PotentiallyDuplicateRawDataControllerIT extends AbstractIT {

    @Autowired
    private PotentiallyDuplicateRawDataResourceApiUtils resourceApiUtils;

    @Autowired
    private MasterCatalogRawDataRepository rawDataRepository;

    @Autowired
    private MasterCatalogPotentiallyDuplicateRawDataRepository duplicateRawDataRepository;

    @Autowired
    private MasterCatalogTaskRepository masterCatalogTaskRepository;

    @SpyBean
    private MasterCatalogBatchJobRepository jobRepository;

    @MockBean
    private PineconeApiAdapterImpl pineconeApiAdapter;

    @MockBean
    private ApplicationEventPublisherProvider applicationEventPublisherProvider;

    @BeforeEach
    void setUp() {
        duplicateRawDataRepository.deleteAllInBatch();
        jobRepository.deleteAllInBatch();
        rawDataRepository.deleteAllInBatch();
    }

    @Test
    void list_WithPendingStatus_ShouldReturnPendingData() {
        // Arrange
        MasterCatalogRawData rawData1 = rawDataRepository.save(buildMasterCatalogRawData(UUID.randomUUID()));
        MasterCatalogRawData rawData2 = rawDataRepository.save(buildMasterCatalogRawData(UUID.randomUUID()));

        MasterCatalogBatchJob job = MasterCatalogBatchJob.builder()
            .status(MasterCatalogBatchJobStatus.REMOVE_DUPLICATION_IN_PROGRESS)
            .build();
        job = jobRepository.save(job);

        MasterCatalogPotentiallyDuplicateRawData duplicateData = buildMasterCatalogPotentiallyDuplicateRawData(
            rawData1.getId(),
            rawData2.getId(),
            job.getId(),
            UUID.randomUUID()
        );
        duplicateRawDataRepository.save(duplicateData);

        // Act
        CustomPage<MasterCatalogPotentiallyDuplicateRawDataDto> result =
            resourceApiUtils.list(PotentiallyDuplicateRawDataStatus.PENDING_REVIEW.name());

        // Assert
        assertThat(result.getData()).hasSize(1);
        assertThat(result.getTotalCount()).isEqualTo(1);

        MasterCatalogPotentiallyDuplicateRawDataDto dto = result.getData().get(0);
        assertThat(dto.getRawDataId()).isEqualTo(rawData1.getId());
        assertThat(dto.getPotentiallyDuplicateRawDataId()).isEqualTo(rawData2.getId());
        assertThat(dto.getStatus()).isEqualTo(PotentiallyDuplicateRawDataStatus.PENDING_REVIEW);
        assertThat(dto.getUpc()).isEqualTo(duplicateData.getUpc());
        assertThat(dto.getName()).isEqualTo(duplicateData.getName());
        assertThat(dto.getPrimaryVendor()).isEqualTo(duplicateData.getPrimaryVendor());
        assertThat(dto.getPotentiallyDuplicateUpc()).isEqualTo(duplicateData.getPotentiallyDuplicateUpc());
        assertThat(dto.getPotentiallyDuplicateName()).isEqualTo(duplicateData.getPotentiallyDuplicateName());
        assertThat(dto.getPotentiallyDuplicateVendor()).isEqualTo(duplicateData.getPotentiallyDuplicateVendor());
    }

//     @Test
//     void submit_ShouldUpdateStatusForSubmittedIds() {
//         // Arrange
//         MasterCatalogRawData rawData1 = rawDataRepository.save(buildMasterCatalogRawData(UUID.randomUUID()));
//         MasterCatalogRawData rawData2 = rawDataRepository.save(buildMasterCatalogRawData(UUID.randomUUID()));

//         MasterCatalogProductGenerationTask task = MasterCatalogProductGenerationTask.builder()
//             .name("Test Task")
//             .status(ProductGenerationTaskStatus.REMOVE_DUPLICATION_IN_PROGRESS)
//             .build();
//         task = jobRepository.save(task);

//         // Create duplicate data with IN_STAGE status
//         MasterCatalogPotentiallyDuplicateRawData duplicateData1 = buildMasterCatalogPotentiallyDuplicateRawData(
//             rawData1.getId(),
//             rawData2.getId(),
//             task.getId()
//         );
//         duplicateData1.setStatus(PotentiallyDuplicateRawDataStatus.IN_STAGE);
//         duplicateData1 = duplicateRawDataRepository.save(duplicateData1);

//         MasterCatalogRawData rawData3 = rawDataRepository.save(buildMasterCatalogRawData(UUID.randomUUID()));
//         MasterCatalogPotentiallyDuplicateRawData duplicateData2 = buildMasterCatalogPotentiallyDuplicateRawData(
//             rawData1.getId(),
//             rawData3.getId(),
//             task.getId()
//         );
//         duplicateData2.setStatus(PotentiallyDuplicateRawDataStatus.IN_STAGE);
//         duplicateData2 = duplicateRawDataRepository.save(duplicateData2);

//         // Create submit request with IDs of potentially duplicate raw data
//         PotentiallyDuplicateSubmitRequest submitRequest = new PotentiallyDuplicateSubmitRequest();
//         submitRequest.setPotentiallyDuplicateRawDataIds(List.of(duplicateData1.getId(), duplicateData2.getId()));

//         // Act
//         resourceApiUtils.submit(submitRequest);

//         // Assert
//         MasterCatalogPotentiallyDuplicateRawData updatedData1 = duplicateRawDataRepository.findById(duplicateData1.getId())
//             .orElseThrow();
//         assertThat(updatedData1.getStatus()).isEqualTo(PotentiallyDuplicateRawDataStatus.FIRST_ROUND_REVIEWED);

//         MasterCatalogPotentiallyDuplicateRawData updatedData2 = duplicateRawDataRepository.findById(duplicateData2.getId())
//             .orElseThrow();
//         assertThat(updatedData2.getStatus()).isEqualTo(PotentiallyDuplicateRawDataStatus.FIRST_ROUND_REVIEWED);

//         //Mock Pinecone
// //        when(pineconeApiAdapter.getDuplicationPredictionForInBatch(anyList(), anyList(), anyList(),
// //            anyList(), anyString(), anyFloat())).thenReturn(
// //            Collections.emptyList());
// //        when(pineconeApiAdapter.getDuplicationPredictionForProduct(anyList(), anyList(), anyList(),
// //            anyString(), anyFloat())).thenReturn(
// //            Collections.emptyList());
// //        when(pineconeApiAdapter.upsertToProductTable(anyList(), anyList(), anyList(),
// //            anyString())).thenReturn(0);

// //         The task status is updated to REMOVE_DUPLICATION_COMPLETED by the event listener
//         ArgumentCaptor<MasterCatalogProductGenerationTask> taskCaptor = ArgumentCaptor.forClass(
//             MasterCatalogProductGenerationTask.class);
//         verify(jobRepository, atLeast(1)).save(taskCaptor.capture());
//         List<MasterCatalogProductGenerationTask> savedTasks = taskCaptor.getAllValues();
//         assert savedTasks.getFirst().getStatus()
//             == ProductGenerationTaskStatus.REMOVE_DUPLICATION_IN_PROGRESS;

// //        MasterCatalogProductGenerationTask updatedTask = jobRepository.findById(task.getId())
// //            .orElseThrow();
// //        assertThat(updatedTask.getStatus()).isEqualTo(
// //            ProductGenerationTaskStatus.REMOVE_DUPLICATION_IN_PROGRESS);
//     }

    @Test
    void update_ShouldUpdateDuplicatedFlag() {
        // Arrange
        UUID taskId = UUID.randomUUID();
        MasterCatalogRawData rawData1 = rawDataRepository.save(buildMasterCatalogRawData(UUID.randomUUID()));
        MasterCatalogRawData rawData2 = rawDataRepository.save(buildMasterCatalogRawData(UUID.randomUUID()));

        MasterCatalogBatchJob job = MasterCatalogBatchJob.builder()
            .status(MasterCatalogBatchJobStatus.REMOVE_DUPLICATION_IN_PROGRESS)
            .build();
        job = jobRepository.save(job);

        MasterCatalogTask task = MasterCatalogTask.builder()
            .id(taskId)
            .jobId(job.getId())
            .type(MasterCatalogTaskType.DUPLICATION_IN_BATCH)
            .status(MasterCatalogTaskStatus.IN_PROGRESS).build();
        masterCatalogTaskRepository.save(task);


        // Create duplicate data with IN_STAGE status
        MasterCatalogPotentiallyDuplicateRawData duplicateData = buildMasterCatalogPotentiallyDuplicateRawData(
            rawData1.getId(),
            rawData2.getId(),
            job.getId(),
            taskId
        );
        duplicateData.setStatus(PotentiallyDuplicateRawDataStatus.IN_STAGE);
        duplicateData.setDuplicated(false);
        duplicateData = duplicateRawDataRepository.save(duplicateData);

        // Create update request
        MasterCatalogPotentiallyDuplicateRawDataUpdateRequest updateRequest = new MasterCatalogPotentiallyDuplicateRawDataUpdateRequest();
        updateRequest.setDuplicated(true);

        resourceApiUtils.update(duplicateData.getId(), updateRequest);

        var result = duplicateRawDataRepository.findById(duplicateData.getId()).orElseThrow();

        // Assert
        assertThat(result).isNotNull();
        assertThat(result.getId()).isEqualTo(duplicateData.getId());
        assertThat(result.getDuplicated()).isTrue();

        // Verify database update
        MasterCatalogPotentiallyDuplicateRawData updatedData = duplicateRawDataRepository.findById(duplicateData.getId())
            .orElseThrow();
        assertThat(updatedData.getDuplicated()).isTrue();
    }

    @Test
    void update_WithNonExistentId_ShouldReturnError() {
        // Arrange
        UUID nonExistentId = UUID.randomUUID();
        MasterCatalogPotentiallyDuplicateRawDataUpdateRequest updateRequest = new MasterCatalogPotentiallyDuplicateRawDataUpdateRequest();
        updateRequest.setDuplicated(true);

        // Act & Assert
        assertThatThrownBy(() -> resourceApiUtils.update(nonExistentId, updateRequest))
            .hasMessageContaining(nonExistentId.toString());
    }
}
