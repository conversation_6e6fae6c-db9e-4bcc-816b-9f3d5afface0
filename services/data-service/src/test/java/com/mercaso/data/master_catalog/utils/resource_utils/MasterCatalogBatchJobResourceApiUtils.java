package com.mercaso.data.master_catalog.utils.resource_utils;

import com.mercaso.data.utils.IntegrationTestRestUtil;
import org.springframework.core.env.Environment;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;

@Component
public class MasterCatalogBatchJobResourceApiUtils extends IntegrationTestRestUtil {

    private static final String V1_PRODUCT_GENERATION_START = "/master-catalog/v1/batch-jobs/start";

    public MasterCatalogBatchJobResourceApiUtils(Environment environment) {
        super(environment);
    }

    public ResponseEntity<Void> startProcessing() {
        return postEntity(V1_PRODUCT_GENERATION_START, null, Void.class);
    }
}
