package com.mercaso.ims.application.command;

import com.mercaso.ims.application.BaseCommand;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;
import java.util.UUID;

/**
 * Command for batch creating ItemVendorRebates
 */
@Data
@Builder
@EqualsAndHashCode(callSuper = true)
@AllArgsConstructor
public class BatchCreateItemVendorRebateCommand extends BaseCommand {

    private List<RebateItem> rebateItems;

    /**
     * Individual rebate item for batch creation
     */
    @Data
    @Builder
    @AllArgsConstructor
    public static class RebateItem {
        private UUID vendorItemId;
        private UUID vendorId;
        private UUID itemId;
        private LocalDate startDate;
        private LocalDate endDate;
        private BigDecimal rebatePerUnit;

        /**
         * Validate the rebate item
         * @return true if valid
         */
        public boolean isValid() {
            return vendorItemId != null &&
                   vendorId != null &&
                   itemId != null &&
                   startDate != null &&
                   rebatePerUnit != null &&
                   rebatePerUnit.compareTo(BigDecimal.ZERO) >= 0 &&
                   (endDate == null || !endDate.isBefore(startDate));
        }

        /**
         * Get validation error message
         * @return error message if validation fails
         */
        public String getValidationError() {
            if (vendorItemId == null) {
                return "Vendor item ID is required";
            }
            if (vendorId == null) {
                return "Vendor ID is required";
            }
            if (itemId == null) {
                return "Item ID is required";
            }
            if (startDate == null) {
                return "Start date is required";
            }
            if (rebatePerUnit == null) {
                return "Rebate per unit is required";
            }
            if (rebatePerUnit.compareTo(BigDecimal.ZERO) < 0) {
                return "Rebate per unit cannot be negative";
            }
            if (endDate != null && endDate.isBefore(startDate)) {
                return "End date cannot be before start date";
            }
            return null;
        }
    }

    /**
     * Validate the command data
     * @return true if the command is valid
     */
    public boolean isValid() {
        return rebateItems != null && 
               !rebateItems.isEmpty() && 
               rebateItems.stream().allMatch(RebateItem::isValid);
    }

    /**
     * Get validation error message
     * @return error message if validation fails
     */
    public String getValidationError() {
        if (rebateItems == null || rebateItems.isEmpty()) {
            return "Rebate items list cannot be empty";
        }
        
        for (int i = 0; i < rebateItems.size(); i++) {
            RebateItem item = rebateItems.get(i);
            if (!item.isValid()) {
                return String.format("Item %d: %s", i + 1, item.getValidationError());
            }
        }
        
        return null;
    }
}
