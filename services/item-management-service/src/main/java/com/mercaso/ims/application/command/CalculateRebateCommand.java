package com.mercaso.ims.application.command;

import com.mercaso.ims.application.BaseCommand;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.UUID;

/**
 * Command for calculating rebate amount
 */
@Data
@Builder
@EqualsAndHashCode(callSuper = true)
@AllArgsConstructor
public class CalculateRebateCommand extends BaseCommand {

    private UUID vendorId;

    private UUID itemId;

    private BigDecimal quantity;

    private LocalDate date;

    /**
     * Validate the command data
     * @return true if the command is valid
     */
    public boolean isValid() {
        return vendorId != null &&
               itemId != null &&
               quantity != null &&
               quantity.compareTo(BigDecimal.ZERO) > 0;
    }

    /**
     * Get validation error message
     * @return error message if validation fails
     */
    public String getValidationError() {
        if (vendorId == null) {
            return "Vendor ID is required";
        }
        if (itemId == null) {
            return "Item ID is required";
        }
        if (quantity == null) {
            return "Quantity is required";
        }
        if (quantity.compareTo(BigDecimal.ZERO) <= 0) {
            return "Quantity must be greater than zero";
        }
        return null;
    }

    /**
     * Get effective date for calculation
     * @return the date to use for calculation (defaults to today if null)
     */
    public LocalDate getEffectiveDate() {
        return date != null ? date : LocalDate.now();
    }
}
