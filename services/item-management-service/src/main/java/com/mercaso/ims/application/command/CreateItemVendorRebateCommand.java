package com.mercaso.ims.application.command;

import com.mercaso.ims.application.BaseCommand;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.UUID;

/**
 * Command for creating a new ItemVendorRebate
 *
 * Business Requirements:
 * - Start Date: Required - when rebate becomes effective
 * - End Date: Optional - if empty, rebate is continuous and valid indefinitely
 * - Supplier: Required - must be a direct supplier
 * - SKU: Required - item identification
 * - Rebate Amount per Unit Sold: Required - amount supplier refunds per sales unit
 */
@Data
@Builder
@EqualsAndHashCode(callSuper = true)
@AllArgsConstructor
public class CreateItemVendorRebateCommand extends BaseCommand {

    /** Vendor Item ID */
    private UUID vendorItemId;

    /** Direct Supplier ID - must be a direct supplier */
    private UUID vendorId;

    /** Item ID associated with the SKU */
    private UUID itemId;

    /** Start Date - when rebate becomes effective (REQUIRED) */
    private LocalDate startDate;

    /** End Date - when rebate terminates (OPTIONAL - null means continuous) */
    private LocalDate endDate;

    /** Rebate Amount per Unit Sold ($) - amount refunded per sales unit (REQUIRED) */
    private BigDecimal rebatePerUnit;

    /**
     * Validate the command data according to business requirements
     * @return true if the command is valid
     */
    public boolean isValid() {
        return vendorItemId != null &&
               startDate != null && // Start date required
               rebatePerUnit != null &&
               rebatePerUnit.compareTo(BigDecimal.ZERO) > 0 && // Must be positive amount
               (endDate == null || !endDate.isBefore(startDate)); // End date optional, but if set must be after start
    }

    /**
     * Get validation error message according to business requirements
     * @return error message if validation fails
     */
    public String getValidationError() {
        if (vendorItemId == null) {
            return "Vendor item ID is required";
        }
        if (startDate == null) {
            return "Start Date is required - when the rebate becomes effective";
        }
        if (rebatePerUnit == null) {
            return "Rebate Amount per Unit Sold ($) is required";
        }
        if (rebatePerUnit.compareTo(BigDecimal.ZERO) <= 0) {
            return "Rebate Amount per Unit Sold must be greater than zero";
        }
        if (endDate != null && endDate.isBefore(startDate)) {
            return "End Date cannot be before Start Date";
        }
        return null;
    }

    /**
     * Check if this creates a continuous rebate (no end date)
     * @return true if end date is null (continuous rebate)
     */
    public boolean isContinuousRebate() {
        return endDate == null;
    }
}
