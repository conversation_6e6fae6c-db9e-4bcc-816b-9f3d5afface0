package com.mercaso.ims.application.command;

import com.mercaso.ims.application.BaseCommand;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.UUID;

/**
 * Command for creating a new ItemVendorRebate
 */
@Data
@Builder
@EqualsAndHashCode(callSuper = true)
@AllArgsConstructor
public class CreateItemVendorRebateCommand extends BaseCommand {

    private UUID vendorItemId;

    private UUID vendorId;

    private UUID itemId;

    private LocalDate startDate;

    private LocalDate endDate;

    private BigDecimal rebatePerUnit;

    /**
     * Validate the command data
     * @return true if the command is valid
     */
    public boolean isValid() {
        return vendorItemId != null &&
               vendorId != null &&
               itemId != null &&
               startDate != null &&
               rebatePerUnit != null &&
               rebatePerUnit.compareTo(BigDecimal.ZERO) >= 0 &&
               (endDate == null || !endDate.isBefore(startDate));
    }

    /**
     * Get validation error message
     * @return error message if validation fails
     */
    public String getValidationError() {
        if (vendorItemId == null) {
            return "Vendor item ID is required";
        }
        if (vendorId == null) {
            return "Vendor ID is required";
        }
        if (itemId == null) {
            return "Item ID is required";
        }
        if (startDate == null) {
            return "Start date is required";
        }
        if (rebatePerUnit == null) {
            return "Rebate per unit is required";
        }
        if (rebatePerUnit.compareTo(BigDecimal.ZERO) < 0) {
            return "Rebate per unit cannot be negative";
        }
        if (endDate != null && endDate.isBefore(startDate)) {
            return "End date cannot be before start date";
        }
        return null;
    }
}
