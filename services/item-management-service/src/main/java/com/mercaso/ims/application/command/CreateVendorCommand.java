package com.mercaso.ims.application.command;

import com.mercaso.ims.application.BaseCommand;
import java.util.UUID;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@Builder
@EqualsAndHashCode(callSuper = true)
@AllArgsConstructor
public class CreateVendorCommand extends BaseCommand {

    private final UUID id;

    private String vendorName;
    private Boolean externalPicking;
    private Boolean shutdownWindowEnabled;

    public String getVendorName() {
        return vendorName != null ? vendorName.trim() : null;
    }

}
