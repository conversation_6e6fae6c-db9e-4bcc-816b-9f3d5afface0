package com.mercaso.ims.application.command;

import com.mercaso.ims.application.BaseCommand;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.UUID;

/**
 * Command for deleting an ItemVendorRebate
 */
@Data
@Builder
@EqualsAndHashCode(callSuper = true)
@AllArgsConstructor
public class DeleteItemVendorRebateCommand extends BaseCommand {

    private final UUID id;

    /**
     * Validate the command data
     * @return true if the command is valid
     */
    public boolean isValid() {
        return id != null;
    }

    /**
     * Get validation error message
     * @return error message if validation fails
     */
    public String getValidationError() {
        if (id == null) {
            return "Rebate ID is required";
        }
        return null;
    }
}
