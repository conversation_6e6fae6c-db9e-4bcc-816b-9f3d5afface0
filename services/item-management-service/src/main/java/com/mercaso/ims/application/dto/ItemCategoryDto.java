package com.mercaso.ims.application.dto;

import java.math.BigDecimal;
import java.util.List;
import java.util.UUID;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class ItemCategoryDto extends BaseDto {

    private UUID id;

    private String title;

    private String skuNumber;

    private Integer pack;

    private String brand;

    private String bottleSize;

    private String department;

    private String category;

    private String subCategory;

    private String clazz;

    private UUID primaryVendorId;

    private String primaryVendorName;

    private UUID backupVendorId;

    private String backupVendorName;

    private BigDecimal crv;

    private List<VendorItemDto> vendorItemDtos;

    private List<ItemAttributeDto> itemAttributes;

    private Integer versionNumber;

    private String photoUrl;

    private String photoName;
}
