package com.mercaso.ims.application.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.UUID;

/**
 * Data Transfer Object for ItemVendorRebate
 *
 * Business Fields:
 * - Start Date: Required - when rebate becomes effective
 * - End Date: Optional - if null, rebate is continuous and valid indefinitely
 * - Supplier: Required - must be a direct supplier
 * - SKU: Required - item identification
 * - Rebate Amount per Unit Sold: Required - amount supplier refunds per sales unit
 */
@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class ItemVendorRebateDto extends BaseDto {

    private UUID id;

    private UUID vendorItemId;

    /** Direct Supplier ID */
    private UUID vendorId;

    /** Item ID associated with the SKU */
    private UUID itemId;

    /** Start Date - when rebate becomes effective (Required) */
    private LocalDate startDate;

    /** End Date - when rebate terminates (Optional - null means continuous) */
    private LocalDate endDate;

    /** Rebate Amount per Unit Sold ($) - amount refunded per sales unit */
    private BigDecimal rebatePerUnit;

    // Additional fields for display purposes
    /** Direct Supplier Name */
    private String vendorName;

    /** Item Name */
    private String itemName;

    /** Item SKU Number */
    private String itemSkuNumber;

    /** Vendor Item Name */
    private String vendorItemName;

    /** Vendor SKU Number */
    private String vendorSkuNumber;

    /** Whether the rebate is currently active */
    private Boolean isActive;

    /** Whether the rebate has expired (only if end date is set and passed) */
    private Boolean isExpired;

    /** Whether the rebate has not started yet */
    private Boolean isNotStarted;

    /** Whether this is a continuous rebate (no end date) */
    private Boolean isContinuous;

    /** Formatted rebate description for display */
    private String rebateDescription;
}
