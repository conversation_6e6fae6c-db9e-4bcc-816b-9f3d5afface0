package com.mercaso.ims.application.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.UUID;

/**
 * Data Transfer Object for ItemVendorRebate
 */
@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class ItemVendorRebateDto extends BaseDto {

    private UUID id;

    private UUID vendorItemId;

    private UUID vendorId;

    private UUID itemId;

    private LocalDate startDate;

    private LocalDate endDate;

    private BigDecimal rebatePerUnit;

    // Additional fields for display purposes
    private String vendorName;

    private String itemName;

    private String itemSkuNumber;

    private String vendorItemName;

    private String vendorSkuNumber;

    private Boolean isActive;

    private Boolean isExpired;

    private Boolean isNotStarted;
}
