package com.mercaso.ims.application.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.List;
import java.util.UUID;

/**
 * Summary DTO for ItemVendorRebate information
 * Used for in-memory calculations and aggregations
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class ItemVendorRebateSummary {

    /** Vendor Item ID */
    private UUID vendorItemId;

    /** Total number of active rebates */
    private int totalActiveRebates;

    /** Sum of all active rebate amounts per unit */
    private BigDecimal totalRebateAmount;

    /** Highest rebate per unit among active rebates */
    private BigDecimal highestRebatePerUnit;

    /** Whether there is at least one continuous rebate (no end date) */
    private boolean hasContinuousRebate;

    /** List of active rebate details */
    private List<ItemVendorRebateDto> activeRebates;

    /** Additional summary information */
    private String summaryDescription;

    /**
     * Check if there are any active rebates
     * @return true if there are active rebates
     */
    public boolean hasActiveRebates() {
        return totalActiveRebates > 0;
    }

    /**
     * Get average rebate per unit
     * @return average rebate amount, or zero if no active rebates
     */
    public BigDecimal getAverageRebatePerUnit() {
        if (totalActiveRebates == 0) {
            return BigDecimal.ZERO;
        }
        return totalRebateAmount.divide(BigDecimal.valueOf(totalActiveRebates), 2, BigDecimal.ROUND_HALF_UP);
    }

    /**
     * Generate summary description
     * @return formatted summary description
     */
    public String generateSummaryDescription() {
        if (totalActiveRebates == 0) {
            return "No active rebates";
        }
        
        StringBuilder desc = new StringBuilder();
        desc.append(totalActiveRebates).append(" active rebate");
        if (totalActiveRebates > 1) {
            desc.append("s");
        }
        
        desc.append(", total: $").append(totalRebateAmount);
        desc.append(", highest: $").append(highestRebatePerUnit);
        
        if (hasContinuousRebate) {
            desc.append(" (includes continuous rebate)");
        }
        
        return desc.toString();
    }
}
