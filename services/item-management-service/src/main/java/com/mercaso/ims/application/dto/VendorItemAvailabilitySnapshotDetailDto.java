package com.mercaso.ims.application.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.util.UUID;

@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class VendorItemAvailabilitySnapshotDetailDto extends BaseDto {

    private UUID detailId;
    private UUID snapshotId;
    private UUID vendorItemId;
    private String vendorSkuNumber;
    private UUID itemId;
    private Boolean previousAvailability;
    private Boolean newAvailability;
} 