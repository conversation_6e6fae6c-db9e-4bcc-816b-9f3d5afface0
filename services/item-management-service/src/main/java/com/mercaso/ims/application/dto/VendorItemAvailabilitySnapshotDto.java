package com.mercaso.ims.application.dto;

import com.mercaso.ims.domain.vendoritem.enums.SnapshotType;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.time.Instant;
import java.util.List;
import java.util.UUID;

@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class VendorItemAvailabilitySnapshotDto extends BaseDto {

    private UUID snapshotId;
    private UUID vendorId;
    private Instant snapshotTime;
    private SnapshotType snapshotType;
    private List<VendorItemAvailabilitySnapshotDetailDto> details;
} 