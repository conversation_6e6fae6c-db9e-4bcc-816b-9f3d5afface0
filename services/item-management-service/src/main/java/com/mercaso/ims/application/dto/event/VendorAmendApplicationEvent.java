package com.mercaso.ims.application.dto.event;

import com.mercaso.ims.application.dto.payload.VendorAmendPayloadDto;
import com.mercaso.ims.infrastructure.event.applicationevent.BaseApplicationEvent;
import lombok.Getter;
import lombok.Setter;

@Setter
@Getter
public class VendorAmendApplicationEvent extends BaseApplicationEvent<VendorAmendPayloadDto> {

    public VendorAmendApplicationEvent(Object source, VendorAmendPayloadDto payload) {
        super(source, payload);
    }
}