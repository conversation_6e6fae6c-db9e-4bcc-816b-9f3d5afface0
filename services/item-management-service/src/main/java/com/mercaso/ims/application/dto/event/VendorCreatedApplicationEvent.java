package com.mercaso.ims.application.dto.event;

import com.mercaso.ims.application.dto.payload.VendorCreatedPayloadDto;
import com.mercaso.ims.infrastructure.event.applicationevent.BaseApplicationEvent;
import lombok.Getter;
import lombok.Setter;

@Setter
@Getter
public class VendorCreatedApplicationEvent extends BaseApplicationEvent<VendorCreatedPayloadDto> {

    public VendorCreatedApplicationEvent(Object source, VendorCreatedPayloadDto payload) {
        super(source, payload);
    }
}