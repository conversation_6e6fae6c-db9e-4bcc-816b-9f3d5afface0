package com.mercaso.ims.application.dto.event;

import com.mercaso.ims.application.dto.payload.VendorItemDeletedPayloadDto;
import com.mercaso.ims.infrastructure.event.applicationevent.BaseApplicationEvent;
import lombok.Getter;
import lombok.Setter;

@Setter
@Getter
public class VendorItemDeletedApplicationEvent extends BaseApplicationEvent<VendorItemDeletedPayloadDto> {

    public VendorItemDeletedApplicationEvent(Object source, VendorItemDeletedPayloadDto payload) {
        super(source, payload);
    }
}