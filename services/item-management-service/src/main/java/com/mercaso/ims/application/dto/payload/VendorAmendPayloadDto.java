package com.mercaso.ims.application.dto.payload;

import com.mercaso.ims.application.dto.AmendDto;
import com.mercaso.ims.application.dto.VendorDto;
import com.mercaso.ims.infrastructure.event.BusinessEventPayloadDto;
import java.util.Objects;
import java.util.UUID;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class VendorAmendPayloadDto extends BusinessEventPayloadDto<AmendDto<VendorDto>> {

    private UUID vendorId;

    private VendorDto previous;

    private VendorDto current;

    @Builder
    public VendorAmendPayloadDto(VendorDto previous,
        VendorDto current,
        UUID vendorId) {
        super(new AmendDto<>(previous, current));
        this.vendorId = vendorId;
        this.previous = previous;
        this.current = current;
    }

    public boolean isShutdownWindowEnabledChanged() {
        return !Objects.equals(previous.getShutdownWindowEnabled(), current.getShutdownWindowEnabled());
    }

}