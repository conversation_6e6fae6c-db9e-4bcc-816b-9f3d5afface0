package com.mercaso.ims.application.dto.payload;

import com.mercaso.ims.application.dto.VendorDto;
import com.mercaso.ims.infrastructure.event.BusinessEventPayloadDto;
import java.util.UUID;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class VendorCreatedPayloadDto extends BusinessEventPayloadDto<VendorDto> {

    private UUID vendorId;

    @Builder
    public VendorCreatedPayloadDto(VendorDto data, UUID vendorId) {
        super(data);
        this.vendorId = vendorId;
    }
}