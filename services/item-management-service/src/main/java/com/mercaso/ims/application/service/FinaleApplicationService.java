package com.mercaso.ims.application.service;

import com.mercaso.ims.application.dto.ItemDto;
import com.mercaso.ims.infrastructure.external.finale.dto.FinaleVendorDto;
import com.mercaso.ims.infrastructure.external.finale.dto.FinaleVendorInfoDto;


public interface FinaleApplicationService {

    void syncItemToFinale(ItemDto itemDto);

    FinaleVendorDto createVendor(String vendorName);

    FinaleVendorDto getVendor(String vendorName);

    FinaleVendorInfoDto getVendorById(String finaleId);

    void updateVendor(FinaleVendorInfoDto finaleVendorInfoDto);
}
