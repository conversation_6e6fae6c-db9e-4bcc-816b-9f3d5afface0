package com.mercaso.ims.application.service;

import com.mercaso.ims.application.command.BatchCreateItemVendorRebateCommand;
import com.mercaso.ims.application.command.CalculateRebateCommand;
import com.mercaso.ims.application.command.CreateItemVendorRebateCommand;
import com.mercaso.ims.application.command.DeleteItemVendorRebateCommand;
import com.mercaso.ims.application.command.UpdateItemVendorRebateCommand;
import com.mercaso.ims.application.dto.ItemVendorRebateDto;
import com.mercaso.ims.application.dto.ItemVendorRebateSummary;
import com.mercaso.ims.domain.itemvendorrebate.ItemVendorRebate;
import com.mercaso.ims.domain.itemvendorrebate.ItemVendorRebateFactory;
import com.mercaso.ims.domain.itemvendorrebate.ItemVendorRebateRepository;
import com.mercaso.ims.domain.itemvendorrebate.service.ItemVendorRebateService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.UUID;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * Application service for ItemVendorRebate operations
 */
@Slf4j
@Service
@RequiredArgsConstructor
@Transactional
public class ItemVendorRebateApplicationService {

    private final ItemVendorRebateRepository itemVendorRebateRepository;
    private final ItemVendorRebateService itemVendorRebateService;
    private final ItemVendorRebateFactory itemVendorRebateFactory;

    /**
     * Create a new item vendor rebate using command
     * @param command the create command
     * @return the created rebate DTO
     */
    public ItemVendorRebateDto createRebate(CreateItemVendorRebateCommand command) {
        UUID vendorItemId = command.getVendorItemId();
        
        log.info("Creating new ItemVendorRebate for vendorItemId: {}", vendorItemId);

        // Get rebates for vendor item - implemented in memory for better performance
        List<ItemVendorRebate> rebatesForVendorItem = getRebatesForVendorItemInMemory(vendorItemId);
        

        // Validate the rebate schedule
        if (!itemVendorRebateService.validateRebateSchedule(command.getVendorItemId(), command.getStartDate(),
                                                            command.getEndDate())) {
            throw new IllegalArgumentException("Rebate schedule conflicts with existing rebates");
        }

        ItemVendorRebate rebate = itemVendorRebateFactory.create(
                command.getVendorItemId(),
                command.getVendorId(),
                command.getItemId(),
                command.getStartDate(),
                command.getEndDate(),
                command.getRebatePerUnit()
        );

        ItemVendorRebate savedRebate = itemVendorRebateRepository.save(rebate);
        return convertToDto(savedRebate);
    }

    /**
     * Update an existing item vendor rebate using command
     * @param command the update command
     * @return the updated rebate DTO
     */
    public ItemVendorRebateDto updateRebate(UpdateItemVendorRebateCommand command) {
        log.info("Updating ItemVendorRebate with id: {}", command.getId());

        Assert.isTrue(command.isValid(), command.getValidationError());

        ItemVendorRebate existingRebate = itemVendorRebateRepository.findById(command.getId())
                .orElseThrow(() -> new IllegalArgumentException("Rebate not found with id: " + command.getId()));

        // Update fields
        existingRebate.setVendorItemId(command.getVendorItemId());
        existingRebate.setVendorId(command.getVendorId());
        existingRebate.setItemId(command.getItemId());
        existingRebate.setStartDate(command.getStartDate());
        existingRebate.setEndDate(command.getEndDate());
        existingRebate.setRebatePerUnit(command.getRebatePerUnit());

        ItemVendorRebate savedRebate = itemVendorRebateRepository.save(existingRebate);
        return convertToDto(savedRebate);
    }

    /**
     * Batch create item vendor rebates
     * @param command the batch create command
     * @return list of created rebate DTOs
     */
    public List<ItemVendorRebateDto> batchCreateRebates(BatchCreateItemVendorRebateCommand command) {
        log.info("Batch creating {} ItemVendorRebates", command.getRebateItems().size());

        Assert.isTrue(command.isValid(), command.getValidationError());

        List<ItemVendorRebateDto> createdRebates = new ArrayList<>();

        for (BatchCreateItemVendorRebateCommand.RebateItem item : command.getRebateItems()) {
            // Validate each rebate schedule
            if (!itemVendorRebateService.validateRebateSchedule(item.getVendorId(), item.getItemId(),
                                                                 item.getStartDate(), item.getEndDate())) {
                throw new IllegalArgumentException(
                    String.format("Rebate schedule conflicts with existing rebates for vendorId: %s, itemId: %s",
                                  item.getVendorId(), item.getItemId()));
            }

            ItemVendorRebate rebate = itemVendorRebateFactory.create(
                    item.getVendorItemId(),
                    item.getVendorId(),
                    item.getItemId(),
                    item.getStartDate(),
                    item.getEndDate(),
                    item.getRebatePerUnit()
            );

            ItemVendorRebate savedRebate = itemVendorRebateRepository.save(rebate);
            createdRebates.add(convertToDto(savedRebate));
        }

        return createdRebates;
    }

    /**
     * Update an existing item vendor rebate using DTO (for backward compatibility)
     * @param id the rebate ID
     * @param dto the updated rebate data
     * @return the updated rebate DTO
     */
    public ItemVendorRebateDto updateRebate(UUID id, ItemVendorRebateDto dto) {
        UpdateItemVendorRebateCommand command = UpdateItemVendorRebateCommand.builder()
                .id(id)
                .vendorItemId(dto.getVendorItemId())
                .vendorId(dto.getVendorId())
                .itemId(dto.getItemId())
                .startDate(dto.getStartDate())
                .endDate(dto.getEndDate())
                .rebatePerUnit(dto.getRebatePerUnit())
                .build();

        return updateRebate(command);
    }

    /**
     * Get rebate by ID
     * @param id the rebate ID
     * @return the rebate DTO
     */
    @Transactional(readOnly = true)
    public Optional<ItemVendorRebateDto> getRebateById(UUID id) {
        log.debug("Getting ItemVendorRebate by id: {}", id);
        return itemVendorRebateRepository.findById(id)
                .map(this::convertToDto);
    }

    /**
     * Get all rebates for a vendor
     * @param vendorId the vendor ID
     * @return list of rebate DTOs
     */
    @Transactional(readOnly = true)
    public List<ItemVendorRebateDto> getRebatesByVendor(UUID vendorId) {
        log.debug("Getting ItemVendorRebates for vendorId: {}", vendorId);
        List<ItemVendorRebate> rebates = itemVendorRebateRepository.findByVendorId(vendorId);
        return rebates.stream()
                .map(this::convertToDto)
                .collect(Collectors.toList());
    }

    /**
     * Get all rebates for an item
     * @param itemId the item ID
     * @return list of rebate DTOs
     */
    @Transactional(readOnly = true)
    public List<ItemVendorRebateDto> getRebatesByItem(UUID itemId) {
        log.debug("Getting ItemVendorRebates for itemId: {}", itemId);
        List<ItemVendorRebate> rebates = itemVendorRebateRepository.findByItemId(itemId);
        return rebates.stream()
                .map(this::convertToDto)
                .collect(Collectors.toList());
    }

    /**
     * Get all rebates for a vendor item
     * @param vendorItemId the vendor item ID
     * @return list of rebate DTOs
     */
    @Transactional(readOnly = true)
    public List<ItemVendorRebateDto> getRebatesByVendorItem(UUID vendorItemId) {
        log.debug("Getting ItemVendorRebates for vendorItemId: {}", vendorItemId);
        List<ItemVendorRebate> rebates = itemVendorRebateRepository.findByVendorItemId(vendorItemId);
        return rebates.stream()
                .map(this::convertToDto)
                .collect(Collectors.toList());
    }

    /**
     * Get currently active rebates for a vendor and item
     * @param vendorId the vendor ID
     * @param itemId the item ID
     * @return list of active rebate DTOs
     */
    @Transactional(readOnly = true)
    public List<ItemVendorRebateDto> getCurrentlyActiveRebates(UUID vendorId, UUID itemId) {
        log.debug("Getting currently active rebates for vendorId: {}, itemId: {}", vendorId, itemId);
        List<ItemVendorRebate> rebates = itemVendorRebateRepository.findCurrentlyActiveRebates(vendorId, itemId);
        return rebates.stream()
                .map(this::convertToDto)
                .collect(Collectors.toList());
    }

    /**
     * Calculate total rebate amount for a vendor and item
     * @param vendorId the vendor ID
     * @param itemId the item ID
     * @param quantity the quantity
     * @param date the date (optional, defaults to today)
     * @return the total rebate amount
     */
    @Transactional(readOnly = true)
    public BigDecimal calculateTotalRebate(UUID vendorId, UUID itemId, BigDecimal quantity, LocalDate date) {
        LocalDate effectiveDate = date != null ? date : LocalDate.now();
        return itemVendorRebateService.calculateTotalRebate(vendorId, itemId, quantity, effectiveDate);
    }

    /**
     * Delete a rebate
     * @param id the rebate ID
     */
    public void deleteRebate(UUID id) {
        log.info("Deleting ItemVendorRebate with id: {}", id);
        if (!itemVendorRebateRepository.existsById(id)) {
            throw new IllegalArgumentException("Rebate not found with id: " + id);
        }
        itemVendorRebateRepository.deleteById(id);
    }

    /**
     * Get rebates expiring within the specified number of days
     * @param days the number of days to look ahead
     * @return list of expiring rebate DTOs
     */
    @Transactional(readOnly = true)
    public List<ItemVendorRebateDto> getExpiringRebates(int days) {
        log.debug("Getting rebates expiring within {} days", days);
        List<ItemVendorRebate> rebates = itemVendorRebateService.getExpiringRebates(days);
        return rebates.stream()
                .map(this::convertToDto)
                .collect(Collectors.toList());
    }

    /**
     * Get rebates for vendor item using in-memory filtering for better performance
     * This method loads all rebates and filters in memory instead of database query
     * @param vendorItemId the vendor item ID
     * @return list of rebates for the vendor item
     */
    public List<ItemVendorRebate> getRebatesForVendorItemInMemory(UUID vendorItemId) {
        log.debug("Getting rebates for vendorItemId: {} using in-memory filtering", vendorItemId);

        // Load all rebates into memory
        List<ItemVendorRebate> allRebates = itemVendorRebateRepository.findByVendorItemId(vendorItemId);

        // Filter in memory by vendorItemId
        return allRebates.stream()
                .filter(rebate -> vendorItemId.equals(rebate.getVendorItemId()))
                .collect(Collectors.toList());
    }

    /**
     * Get rebates for vendor item using in-memory filtering with additional criteria
     * @param vendorItemId the vendor item ID
     * @param activeOnly if true, only return currently active rebates
     * @param sortByStartDate if true, sort results by start date
     * @return filtered and optionally sorted list of rebates
     */
    @Transactional(readOnly = true)
    public List<ItemVendorRebate> getRebatesForVendorItemInMemory(UUID vendorItemId, boolean activeOnly, boolean sortByStartDate) {
        log.debug("Getting rebates for vendorItemId: {} with activeOnly: {}, sortByStartDate: {}",
                  vendorItemId, activeOnly, sortByStartDate);

        // Load all rebates into memory
        List<ItemVendorRebate> allRebates = itemVendorRebateRepository.findAll();

        // Filter and process in memory
        Stream<ItemVendorRebate> rebateStream = allRebates.stream()
                .filter(rebate -> vendorItemId.equals(rebate.getVendorItemId()));

        // Apply active filter if requested
        if (activeOnly) {
            rebateStream = rebateStream.filter(ItemVendorRebate::isCurrentlyActive);
        }

        // Apply sorting if requested
        if (sortByStartDate) {
            rebateStream = rebateStream.sorted((r1, r2) -> r1.getStartDate().compareTo(r2.getStartDate()));
        }

        return rebateStream.collect(Collectors.toList());
    }

    /**
     * Get rebates for multiple vendor items using in-memory batch processing
     * @param vendorItemIds list of vendor item IDs
     * @return map of vendor item ID to list of rebates
     */
    @Transactional(readOnly = true)
    public Map<UUID, List<ItemVendorRebate>> getRebatesForVendorItemsBatchInMemory(List<UUID> vendorItemIds) {
        log.debug("Getting rebates for {} vendorItemIds using in-memory batch processing", vendorItemIds.size());

        if (vendorItemIds == null || vendorItemIds.isEmpty()) {
            return new HashMap<>();
        }

        // Load all rebates into memory once
        List<ItemVendorRebate> allRebates = itemVendorRebateRepository.findAll();

        // Group by vendorItemId in memory
        return allRebates.stream()
                .filter(rebate -> vendorItemIds.contains(rebate.getVendorItemId()))
                .collect(Collectors.groupingBy(ItemVendorRebate::getVendorItemId));
    }

    /**
     * Get active rebates summary for vendor item using in-memory calculation
     * @param vendorItemId the vendor item ID
     * @return summary information about active rebates
     */
    @Transactional(readOnly = true)
    public ItemVendorRebateSummary getActiveRebateSummaryInMemory(UUID vendorItemId) {
        log.debug("Getting active rebate summary for vendorItemId: {} using in-memory calculation", vendorItemId);

        List<ItemVendorRebate> activeRebates = getRebatesForVendorItemInMemory(vendorItemId, true, false);

        if (activeRebates.isEmpty()) {
            return ItemVendorRebateSummary.builder()
                    .vendorItemId(vendorItemId)
                    .totalActiveRebates(0)
                    .totalRebateAmount(BigDecimal.ZERO)
                    .highestRebatePerUnit(BigDecimal.ZERO)
                    .hasContinuousRebate(false)
                    .build();
        }

        // Calculate summary in memory
        BigDecimal totalRebateAmount = activeRebates.stream()
                .map(ItemVendorRebate::getRebatePerUnit)
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        BigDecimal highestRebatePerUnit = activeRebates.stream()
                .map(ItemVendorRebate::getRebatePerUnit)
                .max(BigDecimal::compareTo)
                .orElse(BigDecimal.ZERO);

        boolean hasContinuousRebate = activeRebates.stream()
                .anyMatch(ItemVendorRebate::isContinuous);

        return ItemVendorRebateSummary.builder()
                .vendorItemId(vendorItemId)
                .totalActiveRebates(activeRebates.size())
                .totalRebateAmount(totalRebateAmount)
                .highestRebatePerUnit(highestRebatePerUnit)
                .hasContinuousRebate(hasContinuousRebate)
                .activeRebates(activeRebates.stream().map(this::convertToDto).collect(Collectors.toList()))
                .build();
    }

    /**
     * Convert domain entity to DTO
     * @param rebate the domain entity
     * @return the DTO
     */
    private ItemVendorRebateDto convertToDto(ItemVendorRebate rebate) {
        return ItemVendorRebateDto.builder()
                .id(rebate.getId())
                .vendorItemId(rebate.getVendorItemId())
                .vendorId(rebate.getVendorId())
                .itemId(rebate.getItemId())
                .startDate(rebate.getStartDate())
                .endDate(rebate.getEndDate())
                .rebatePerUnit(rebate.getRebatePerUnit())
                .isActive(rebate.isCurrentlyActive())
                .isExpired(rebate.isExpired())
                .isNotStarted(rebate.isNotStarted())
                .isContinuous(rebate.isContinuous())
                .rebateDescription(rebate.getRebateDescription())
                .build();
    }
}
