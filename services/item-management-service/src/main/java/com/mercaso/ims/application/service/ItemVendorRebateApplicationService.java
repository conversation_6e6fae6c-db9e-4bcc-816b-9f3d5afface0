package com.mercaso.ims.application.service;

import com.mercaso.ims.application.dto.ItemVendorRebateDto;
import com.mercaso.ims.domain.itemvendorrebate.ItemVendorRebate;
import com.mercaso.ims.domain.itemvendorrebate.ItemVendorRebateFactory;
import com.mercaso.ims.domain.itemvendorrebate.ItemVendorRebateRepository;
import com.mercaso.ims.domain.itemvendorrebate.service.ItemVendorRebateService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;
import java.util.Optional;
import java.util.UUID;
import java.util.stream.Collectors;

/**
 * Application service for ItemVendorRebate operations
 */
@Slf4j
@Service
@RequiredArgsConstructor
@Transactional
public class ItemVendorRebateApplicationService {

    private final ItemVendorRebateRepository itemVendorRebateRepository;
    private final ItemVendorRebateService itemVendorRebateService;
    private final ItemVendorRebateFactory itemVendorRebateFactory;

    /**
     * Create a new item vendor rebate
     * @param dto the rebate data
     * @return the created rebate DTO
     */
    public ItemVendorRebateDto createRebate(ItemVendorRebateDto dto) {
        log.info("Creating new ItemVendorRebate for vendorId: {}, itemId: {}", dto.getVendorId(), dto.getItemId());
        
        // Validate the rebate schedule
        if (!itemVendorRebateService.validateRebateSchedule(dto.getVendorId(), dto.getItemId(), 
                                                             dto.getStartDate(), dto.getEndDate())) {
            throw new IllegalArgumentException("Rebate schedule conflicts with existing rebates");
        }
        
        ItemVendorRebate rebate = itemVendorRebateFactory.create(
                dto.getVendorItemId(),
                dto.getVendorId(),
                dto.getItemId(),
                dto.getStartDate(),
                dto.getEndDate(),
                dto.getRebatePerUnit()
        );
        
        ItemVendorRebate savedRebate = itemVendorRebateRepository.save(rebate);
        return convertToDto(savedRebate);
    }

    /**
     * Update an existing item vendor rebate
     * @param id the rebate ID
     * @param dto the updated rebate data
     * @return the updated rebate DTO
     */
    public ItemVendorRebateDto updateRebate(UUID id, ItemVendorRebateDto dto) {
        log.info("Updating ItemVendorRebate with id: {}", id);
        
        ItemVendorRebate existingRebate = itemVendorRebateRepository.findById(id)
                .orElseThrow(() -> new IllegalArgumentException("Rebate not found with id: " + id));
        
        // Update fields
        existingRebate.setVendorItemId(dto.getVendorItemId());
        existingRebate.setVendorId(dto.getVendorId());
        existingRebate.setItemId(dto.getItemId());
        existingRebate.setStartDate(dto.getStartDate());
        existingRebate.setEndDate(dto.getEndDate());
        existingRebate.setRebatePerUnit(dto.getRebatePerUnit());
        
        ItemVendorRebate savedRebate = itemVendorRebateRepository.save(existingRebate);
        return convertToDto(savedRebate);
    }

    /**
     * Get rebate by ID
     * @param id the rebate ID
     * @return the rebate DTO
     */
    @Transactional(readOnly = true)
    public Optional<ItemVendorRebateDto> getRebateById(UUID id) {
        log.debug("Getting ItemVendorRebate by id: {}", id);
        return itemVendorRebateRepository.findById(id)
                .map(this::convertToDto);
    }

    /**
     * Get all rebates for a vendor
     * @param vendorId the vendor ID
     * @return list of rebate DTOs
     */
    @Transactional(readOnly = true)
    public List<ItemVendorRebateDto> getRebatesByVendor(UUID vendorId) {
        log.debug("Getting ItemVendorRebates for vendorId: {}", vendorId);
        List<ItemVendorRebate> rebates = itemVendorRebateRepository.findByVendorId(vendorId);
        return rebates.stream()
                .map(this::convertToDto)
                .collect(Collectors.toList());
    }

    /**
     * Get all rebates for an item
     * @param itemId the item ID
     * @return list of rebate DTOs
     */
    @Transactional(readOnly = true)
    public List<ItemVendorRebateDto> getRebatesByItem(UUID itemId) {
        log.debug("Getting ItemVendorRebates for itemId: {}", itemId);
        List<ItemVendorRebate> rebates = itemVendorRebateRepository.findByItemId(itemId);
        return rebates.stream()
                .map(this::convertToDto)
                .collect(Collectors.toList());
    }

    /**
     * Get all rebates for a vendor item
     * @param vendorItemId the vendor item ID
     * @return list of rebate DTOs
     */
    @Transactional(readOnly = true)
    public List<ItemVendorRebateDto> getRebatesByVendorItem(UUID vendorItemId) {
        log.debug("Getting ItemVendorRebates for vendorItemId: {}", vendorItemId);
        List<ItemVendorRebate> rebates = itemVendorRebateRepository.findByVendorItemId(vendorItemId);
        return rebates.stream()
                .map(this::convertToDto)
                .collect(Collectors.toList());
    }

    /**
     * Get currently active rebates for a vendor and item
     * @param vendorId the vendor ID
     * @param itemId the item ID
     * @return list of active rebate DTOs
     */
    @Transactional(readOnly = true)
    public List<ItemVendorRebateDto> getCurrentlyActiveRebates(UUID vendorId, UUID itemId) {
        log.debug("Getting currently active rebates for vendorId: {}, itemId: {}", vendorId, itemId);
        List<ItemVendorRebate> rebates = itemVendorRebateRepository.findCurrentlyActiveRebates(vendorId, itemId);
        return rebates.stream()
                .map(this::convertToDto)
                .collect(Collectors.toList());
    }

    /**
     * Calculate total rebate amount for a vendor and item
     * @param vendorId the vendor ID
     * @param itemId the item ID
     * @param quantity the quantity
     * @param date the date (optional, defaults to today)
     * @return the total rebate amount
     */
    @Transactional(readOnly = true)
    public BigDecimal calculateTotalRebate(UUID vendorId, UUID itemId, BigDecimal quantity, LocalDate date) {
        LocalDate effectiveDate = date != null ? date : LocalDate.now();
        return itemVendorRebateService.calculateTotalRebate(vendorId, itemId, quantity, effectiveDate);
    }

    /**
     * Delete a rebate
     * @param id the rebate ID
     */
    public void deleteRebate(UUID id) {
        log.info("Deleting ItemVendorRebate with id: {}", id);
        if (!itemVendorRebateRepository.existsById(id)) {
            throw new IllegalArgumentException("Rebate not found with id: " + id);
        }
        itemVendorRebateRepository.deleteById(id);
    }

    /**
     * Get rebates expiring within the specified number of days
     * @param days the number of days to look ahead
     * @return list of expiring rebate DTOs
     */
    @Transactional(readOnly = true)
    public List<ItemVendorRebateDto> getExpiringRebates(int days) {
        log.debug("Getting rebates expiring within {} days", days);
        List<ItemVendorRebate> rebates = itemVendorRebateService.getExpiringRebates(days);
        return rebates.stream()
                .map(this::convertToDto)
                .collect(Collectors.toList());
    }

    /**
     * Convert domain entity to DTO
     * @param rebate the domain entity
     * @return the DTO
     */
    private ItemVendorRebateDto convertToDto(ItemVendorRebate rebate) {
        return ItemVendorRebateDto.builder()
                .id(rebate.getId())
                .vendorItemId(rebate.getVendorItemId())
                .vendorId(rebate.getVendorId())
                .itemId(rebate.getItemId())
                .startDate(rebate.getStartDate())
                .endDate(rebate.getEndDate())
                .rebatePerUnit(rebate.getRebatePerUnit())
                .isActive(rebate.isCurrentlyActive())
                .isExpired(rebate.isExpired())
                .isNotStarted(rebate.isNotStarted())
                .build();
    }
}
