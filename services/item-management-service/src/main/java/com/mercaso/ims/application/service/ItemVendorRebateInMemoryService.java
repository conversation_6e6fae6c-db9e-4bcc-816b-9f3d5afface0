package com.mercaso.ims.application.service;

import com.mercaso.ims.domain.itemvendorrebate.ItemVendorRebate;
import com.mercaso.ims.domain.itemvendorrebate.ItemVendorRebateRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

/**
 * In-memory service for ItemVendorRebate operations
 * Provides optimized in-memory filtering and caching for better performance
 */
@Slf4j
@Service
@RequiredArgsConstructor
@Transactional(readOnly = true)
public class ItemVendorRebateInMemoryService {

    private final ItemVendorRebateRepository itemVendorRebateRepository;
    
    // In-memory cache for frequently accessed data
    private final Map<String, List<ItemVendorRebate>> rebateCache = new ConcurrentHashMap<>();
    private volatile long lastCacheUpdate = 0;
    private static final long CACHE_TTL_MS = 5 * 60 * 1000; // 5 minutes

    /**
     * Get rebates for vendor item using in-memory filtering with caching
     * @param vendorItemId the vendor item ID
     * @return list of rebates for the vendor item
     */
    @Cacheable(value = "vendorItemRebates", key = "#vendorItemId")
    public List<ItemVendorRebate> getRebatesForVendorItem(UUID vendorItemId) {
        log.debug("Getting rebates for vendorItemId: {} using cached in-memory filtering", vendorItemId);
        
        List<ItemVendorRebate> allRebates = getAllRebatesFromCacheOrDatabase();
        
        return allRebates.stream()
                .filter(rebate -> vendorItemId.equals(rebate.getVendorItemId()))
                .collect(Collectors.toList());
    }

    /**
     * Get active rebates for vendor item using in-memory filtering
     * @param vendorItemId the vendor item ID
     * @return list of currently active rebates
     */
    @Cacheable(value = "activeVendorItemRebates", key = "#vendorItemId")
    public List<ItemVendorRebate> getActiveRebatesForVendorItem(UUID vendorItemId) {
        log.debug("Getting active rebates for vendorItemId: {} using in-memory filtering", vendorItemId);
        
        List<ItemVendorRebate> allRebates = getAllRebatesFromCacheOrDatabase();
        LocalDate today = LocalDate.now();
        
        return allRebates.stream()
                .filter(rebate -> vendorItemId.equals(rebate.getVendorItemId()))
                .filter(rebate -> rebate.isActiveOn(today))
                .collect(Collectors.toList());
    }

    /**
     * Get rebates for multiple vendor items using batch in-memory processing
     * @param vendorItemIds list of vendor item IDs
     * @return map of vendor item ID to list of rebates
     */
    public Map<UUID, List<ItemVendorRebate>> getRebatesForVendorItemsBatch(List<UUID> vendorItemIds) {
        log.debug("Getting rebates for {} vendorItemIds using batch in-memory processing", vendorItemIds.size());
        
        if (vendorItemIds == null || vendorItemIds.isEmpty()) {
            return new HashMap<>();
        }
        
        List<ItemVendorRebate> allRebates = getAllRebatesFromCacheOrDatabase();
        
        return allRebates.stream()
                .filter(rebate -> vendorItemIds.contains(rebate.getVendorItemId()))
                .collect(Collectors.groupingBy(ItemVendorRebate::getVendorItemId));
    }

    /**
     * Calculate total rebate amount for vendor item and quantity using in-memory calculation
     * @param vendorItemId the vendor item ID
     * @param quantity the quantity
     * @param date the date to calculate for (optional, defaults to today)
     * @return total rebate amount
     */
    public BigDecimal calculateTotalRebateForVendorItem(UUID vendorItemId, BigDecimal quantity, LocalDate date) {
        log.debug("Calculating total rebate for vendorItemId: {}, quantity: {}, date: {}", 
                  vendorItemId, quantity, date);
        
        if (quantity == null || quantity.compareTo(BigDecimal.ZERO) <= 0) {
            return BigDecimal.ZERO;
        }
        
        LocalDate effectiveDate = date != null ? date : LocalDate.now();
        List<ItemVendorRebate> allRebates = getAllRebatesFromCacheOrDatabase();
        
        return allRebates.stream()
                .filter(rebate -> vendorItemId.equals(rebate.getVendorItemId()))
                .filter(rebate -> rebate.isActiveOn(effectiveDate))
                .map(rebate -> rebate.calculateRebateAmount(quantity))
                .reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    /**
     * Get highest rebate per unit for vendor item using in-memory calculation
     * @param vendorItemId the vendor item ID
     * @param date the date to check (optional, defaults to today)
     * @return highest rebate per unit
     */
    public BigDecimal getHighestRebatePerUnitForVendorItem(UUID vendorItemId, LocalDate date) {
        log.debug("Getting highest rebate per unit for vendorItemId: {}, date: {}", vendorItemId, date);
        
        LocalDate effectiveDate = date != null ? date : LocalDate.now();
        List<ItemVendorRebate> allRebates = getAllRebatesFromCacheOrDatabase();
        
        return allRebates.stream()
                .filter(rebate -> vendorItemId.equals(rebate.getVendorItemId()))
                .filter(rebate -> rebate.isActiveOn(effectiveDate))
                .map(ItemVendorRebate::getRebatePerUnit)
                .max(BigDecimal::compareTo)
                .orElse(BigDecimal.ZERO);
    }

    /**
     * Check if vendor item has any active rebates using in-memory check
     * @param vendorItemId the vendor item ID
     * @return true if there are active rebates
     */
    public boolean hasActiveRebates(UUID vendorItemId) {
        log.debug("Checking if vendorItemId: {} has active rebates", vendorItemId);
        
        List<ItemVendorRebate> allRebates = getAllRebatesFromCacheOrDatabase();
        LocalDate today = LocalDate.now();
        
        return allRebates.stream()
                .filter(rebate -> vendorItemId.equals(rebate.getVendorItemId()))
                .anyMatch(rebate -> rebate.isActiveOn(today));
    }

    /**
     * Get rebates expiring soon for vendor item using in-memory filtering
     * @param vendorItemId the vendor item ID
     * @param days number of days to look ahead
     * @return list of rebates expiring within the specified days
     */
    public List<ItemVendorRebate> getExpiringRebatesForVendorItem(UUID vendorItemId, int days) {
        log.debug("Getting expiring rebates for vendorItemId: {} within {} days", vendorItemId, days);
        
        List<ItemVendorRebate> allRebates = getAllRebatesFromCacheOrDatabase();
        LocalDate today = LocalDate.now();
        LocalDate futureDate = today.plusDays(days);
        
        return allRebates.stream()
                .filter(rebate -> vendorItemId.equals(rebate.getVendorItemId()))
                .filter(rebate -> rebate.getEndDate() != null)
                .filter(rebate -> !rebate.getEndDate().isBefore(today))
                .filter(rebate -> !rebate.getEndDate().isAfter(futureDate))
                .collect(Collectors.toList());
    }

    /**
     * Clear cache when rebates are modified
     */
    @CacheEvict(value = {"vendorItemRebates", "activeVendorItemRebates"}, allEntries = true)
    public void clearCache() {
        log.debug("Clearing ItemVendorRebate cache");
        rebateCache.clear();
        lastCacheUpdate = 0;
    }

    /**
     * Get all rebates from cache or database
     * @return list of all rebates
     */
    private List<ItemVendorRebate> getAllRebatesFromCacheOrDatabase() {
        long currentTime = System.currentTimeMillis();
        
        // Check if cache is still valid
        if (currentTime - lastCacheUpdate < CACHE_TTL_MS && !rebateCache.isEmpty()) {
            return rebateCache.get("all");
        }
        
        // Refresh cache from database
        log.debug("Refreshing rebate cache from database");
        List<ItemVendorRebate> allRebates = itemVendorRebateRepository.findAll();
        rebateCache.put("all", allRebates);
        lastCacheUpdate = currentTime;
        
        return allRebates;
    }

    /**
     * Warm up the cache by loading all rebates
     */
    public void warmUpCache() {
        log.info("Warming up ItemVendorRebate cache");
        getAllRebatesFromCacheOrDatabase();
    }

    /**
     * Get cache statistics
     * @return cache statistics
     */
    public Map<String, Object> getCacheStatistics() {
        Map<String, Object> stats = new HashMap<>();
        stats.put("cacheSize", rebateCache.size());
        stats.put("lastCacheUpdate", lastCacheUpdate);
        stats.put("cacheAge", System.currentTimeMillis() - lastCacheUpdate);
        stats.put("cacheTtl", CACHE_TTL_MS);
        return stats;
    }
}
