package com.mercaso.ims.application.service;

import com.mercaso.ims.application.dto.VendorItemAvailabilitySnapshotDetailDto;
import com.mercaso.ims.application.dto.VendorItemAvailabilitySnapshotDto;
import com.mercaso.ims.domain.vendoritem.enums.SnapshotType;

import java.time.Instant;
import java.util.List;
import java.util.UUID;

public interface VendorItemAvailabilitySnapshotApplicationService {

    VendorItemAvailabilitySnapshotDto findById(UUID id);

    List<VendorItemAvailabilitySnapshotDto> findByVendorId(UUID vendorId);

    List<VendorItemAvailabilitySnapshotDto> findByVendorIdAndSnapshotType(UUID vendorId, SnapshotType snapshotType);

    VendorItemAvailabilitySnapshotDto findLatestByVendorIdAndSnapshotType(UUID vendorId, SnapshotType snapshotType);

    List<VendorItemAvailabilitySnapshotDto> findByVendorIdAndSnapshotTimeAfter(UUID vendorId, Instant startTime);

    List<VendorItemAvailabilitySnapshotDetailDto> findDetailsBySnapshotId(UUID snapshotId);
} 