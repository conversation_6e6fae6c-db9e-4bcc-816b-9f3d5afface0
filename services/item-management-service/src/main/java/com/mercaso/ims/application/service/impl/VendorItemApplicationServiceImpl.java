package com.mercaso.ims.application.service.impl;

import com.mercaso.ims.application.command.CreateVendorItemCommand;
import com.mercaso.ims.application.command.UpdateVendorItemCommand;
import com.mercaso.ims.application.dto.VendorItemAuditHistoryInfoDto;
import com.mercaso.ims.application.dto.VendorItemDto;
import com.mercaso.ims.application.dto.payload.VendorItemAmendPayloadDto;
import com.mercaso.ims.application.dto.payload.VendorItemCreatedPayloadDto;
import com.mercaso.ims.application.dto.payload.VendorItemDeletedPayloadDto;
import com.mercaso.ims.application.mapper.vendoritem.VendorItemDtoApplicationMapper;
import com.mercaso.ims.application.queryservice.VendorItemQueryApplicationService;
import com.mercaso.ims.application.service.VendorItemApplicationService;
import com.mercaso.ims.domain.businessevent.BusinessEvent;
import com.mercaso.ims.domain.businessevent.enums.EntityEnums;
import com.mercaso.ims.domain.businessevent.service.BusinessEventService;
import com.mercaso.ims.domain.item.Item;
import com.mercaso.ims.domain.item.service.ItemService;
import com.mercaso.ims.domain.itemcostchangerequest.ItemCostChangeRequest;
import com.mercaso.ims.domain.itemcostchangerequest.service.ItemCostChangeRequestService;
import com.mercaso.ims.domain.vendor.Vendor;
import com.mercaso.ims.domain.vendor.service.VendorService;
import com.mercaso.ims.domain.vendoritem.VendorItem;
import com.mercaso.ims.domain.vendoritem.VendorItemFactory;
import com.mercaso.ims.domain.vendoritem.service.VendorItemService;
import com.mercaso.ims.infrastructure.exception.ImsBusinessException;
import com.mercaso.ims.infrastructure.util.SerializationUtils;
import java.util.List;
import java.util.UUID;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import static com.mercaso.ims.infrastructure.exception.ErrorCodeEnums.*;

@RequiredArgsConstructor
@Service
@Slf4j
@Transactional
public class VendorItemApplicationServiceImpl implements VendorItemApplicationService {

    private final VendorItemService vendorItemService;
    private final VendorItemDtoApplicationMapper vendorItemDtoApplicationMapper;
    private final ItemService itemService;
    private final BusinessEventService businessEventService;
    private final VendorService vendorService;
    private final VendorItemQueryApplicationService vendorItemQueryApplicationService;
    private final ItemCostChangeRequestService itemCostChangeRequestService;

    @Override
    @Transactional
    public VendorItemDto create(CreateVendorItemCommand command) {
        Assert.notNull(command.getVendorId(), "VendorId is required");
        Assert.notNull(command.getItemId(), "ItemId is required");

        Item item = itemService.findById(command.getItemId());
        if (item == null) {
            log.warn("Item not found as createVendorItem itemId: {}", command.getItemId());
            throw new ImsBusinessException(ITEM_NOT_FOUND);
        }

        Vendor vendor = vendorService.findById(command.getVendorId());
        if (vendor == null) {
            log.warn("Vendor not found as createVendorItem vendor: {}", command.getVendorId());
            throw new ImsBusinessException(VENDOR_NOT_FOUND);
        }

        VendorItem vendorItem = vendorItemService.findByVendorIDAndItemId(command.getVendorId(), command.getItemId());
        if (vendorItem != null) {
            log.warn("VendorItem already exist  as createVendorItem vendorId: {}  ItemId :{}",
                command.getVendorId(),
                command.getItemId());
            throw new ImsBusinessException(VENDOR_ITEM_ALREADY_EXISTS);
        }
        vendorItem = VendorItemFactory.createVendorItem(command, vendor);
        vendorItem = vendorItemService.save(vendorItem);

        VendorItemDto current = vendorItemQueryApplicationService.findById(vendorItem.getId());

        businessEventService.dispatch(VendorItemCreatedPayloadDto.builder()
            .vendorItemId(vendorItem.getId())
            .data(current)
            .build());
        return current;
    }

    @Override
    public VendorItemDto update(UpdateVendorItemCommand command) {
        VendorItem vendorItem = vendorItemService.findById(command.getVendorItemId());
        if (vendorItem == null) {
            throw new ImsBusinessException(VENDOR_ITEM_NOT_FOUND);
        }

        Item item = itemService.findById(vendorItem.getItemId());
        if (item == null) {
            log.warn("Item not found as updateVendorItem itemId: {}", vendorItem.getItemId());
            throw new ImsBusinessException(ITEM_NOT_FOUND);
        }

        Vendor vendor = vendorService.findById(vendorItem.getVendorId());
        if (vendor == null) {
            log.warn("Vendor not found as updateVendorItem vendor: {}", vendorItem.getVendorId());
            throw new ImsBusinessException(VENDOR_NOT_FOUND);
        }

        VendorItemDto previous = vendorItemQueryApplicationService.findById(vendorItem.getId());

        vendorItem.updateVendorSkuNumber(command.getVendorSkuNumber());
        vendorItem.updateDetails(command.getVendorItemName(), command.getNote(), command.getAisle());
        vendorItem.updateCost(command.getCost(), command.getIsCostRefreshed());
        vendorItem.updateAvailability(command.getAvailability());
        vendorItem.updateBackupCost(command.getBackupCost(), command.getIsBackupCostRefreshed());
        vendorItem.updateVendorItemType(command.getVendorItemType());
        vendorItem = vendorItemService.save(vendorItem);

        VendorItemDto current = vendorItemQueryApplicationService.findById(vendorItem.getId());

        businessEventService.dispatch(VendorItemAmendPayloadDto.builder()
            .vendorItemId(vendorItem.getId())
            .itemCostChangeRequestId(command.getItemCostChangeRequestId())
            .previous(previous)
            .current(current)
            .build());
        return current;
    }

    @Override
    public VendorItemDto delete(UUID id) {
        // 1. Directly retrieve VendorItem through the domain service to reduce one query.
        VendorItem vendorItem = vendorItemService.findById(id);
        if (vendorItem == null) {
            throw new ImsBusinessException(VENDOR_ITEM_NOT_FOUND);
        }

        // 2. Verify if the associated Item exists
        Item item = itemService.findById(vendorItem.getItemId());
        if (item == null) {
            log.warn("Item not found when deleting VendorItem, itemId: {}", vendorItem.getItemId());
            throw new ImsBusinessException(ITEM_NOT_FOUND);
        }

        // 3. Query all VendorItems under the same Item and verify the deletion rules in one go.
        List<VendorItem> allVendorItems = vendorItemService.findByItemID(item.getId());
        if (allVendorItems.size() <= 1) {
            throw new ImsBusinessException(CANNOT_DELETE_LAST_VENDOR_ITEM_WITHOUT_ALTERNATIVE);
        }

        // 4. Execute delete operation
        VendorItem deletedVendorItem = vendorItemService.delete(id);
        
        // 5. Only convert to DTO and return after the deletion is successful.
        VendorItemDto vendorItemDto = vendorItemDtoApplicationMapper.domainToDto(deletedVendorItem);

        businessEventService.dispatch(VendorItemDeletedPayloadDto.builder()
                .vendorItemId(vendorItem.getId())
                .data(vendorItemDto)
                .build());

        return vendorItemDto;
    }

    @Override
    public List<VendorItemAuditHistoryInfoDto> getVendorItemAuditHistories(UUID id) {
        List<BusinessEvent> businessEvents = businessEventService.findByEntityIdAndType(id, EntityEnums.VENDOR_ITEM.getValue());
        if (CollectionUtils.isEmpty(businessEvents)) {
            return List.of();
        }
        return businessEvents.stream()
            .map(this::buildVendorItemAuditHistoryInfoDto)
            .toList();
    }


    private VendorItemAuditHistoryInfoDto buildVendorItemAuditHistoryInfoDto(
        BusinessEvent businessEvent) {

        return switch (businessEvent.getType()) {
            case VENDOR_ITEM_AMEND -> {
                VendorItemAmendPayloadDto payload = SerializationUtils.deserialize(businessEvent.getPayload(),
                    VendorItemAmendPayloadDto.class);
                yield populateVendorItemAuditHistoryInfoDto(businessEvent,
                    payload.getData().getPrevious(),
                    payload.getData().getCurrent(),
                    payload.getItemCostChangeRequestId(),
                    payload.getItemAdjustmentRequestDetailId());
            }
            case VENDOR_ITEM_CREATED -> {
                VendorItemCreatedPayloadDto createdPayloadDto = SerializationUtils.deserialize(businessEvent.getPayload(),
                    VendorItemCreatedPayloadDto.class);
                yield populateVendorItemAuditHistoryInfoDto(businessEvent, null, createdPayloadDto.getData(), null,
                    createdPayloadDto.getItemAdjustmentRequestDetailId());
            }
            default -> null;
        };
    }

    private VendorItemAuditHistoryInfoDto populateVendorItemAuditHistoryInfoDto(BusinessEvent businessEvent,
        VendorItemDto previous, VendorItemDto current, UUID itemCostChangeRequestId,
        UUID itemAdjustmentRequestDetailId) {
        UUID itemCostCollectionId = null;
        if (itemCostChangeRequestId != null) {
            ItemCostChangeRequest itemCostChangeRequest = itemCostChangeRequestService.findById(
                itemCostChangeRequestId);
            if (itemCostChangeRequest != null) {
                itemCostCollectionId = itemCostChangeRequest.getItemCostCollectionId();
            }
        }
        return VendorItemAuditHistoryInfoDto.builder()
            .type(businessEvent.getType())
            .updatedBy(businessEvent.getCreatedBy())
            .updatedAt(businessEvent.getCreatedAt())
            .updatedUserName(businessEvent.getCreatedUserName())
            .previous(previous)
            .current(current)
            .itemCostChangeRequestId(itemCostChangeRequestId)
            .itemAdjustmentRequestDetailId(itemAdjustmentRequestDetailId)
            .itemCostCollectionId(itemCostCollectionId)
            .build();
    }

}
