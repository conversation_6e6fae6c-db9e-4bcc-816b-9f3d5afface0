package com.mercaso.ims.domain.item;

import com.mercaso.ims.application.command.UpdateItemCommand;
import com.mercaso.ims.domain.Specification;
import com.mercaso.ims.domain.item.enums.AvailabilityStatus;
import com.mercaso.ims.domain.itemregprice.ItemRegPrice;
import com.mercaso.ims.domain.itemregprice.ItemRegPriceRepository;
import com.mercaso.ims.domain.vendoritem.VendorItemRepository;
import java.math.BigDecimal;
import java.util.UUID;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;


@Component
@RequiredArgsConstructor
@Slf4j
public class ItemSpecification implements Specification<Item> {

    private final VendorItemRepository vendorItemRepository;
    private final ItemRepository itemRepository;
    private final ItemRegPriceRepository regPriceRepository;

    public boolean isSatisfiedNewMargin(UpdateItemCommand updateItemCommand) {
        Item item = itemRepository.findById(updateItemCommand.getId());

        // Check if item exists
        if (item == null) {
            log.error("Item not found with id: {}", updateItemCommand.getId());
            return false;
        }

        BigDecimal primaryCost;
        AvailabilityStatus availabilityStatus = updateItemCommand.getAvailabilityStatus() == null ? item.getAvailabilityStatus()
            : updateItemCommand.getAvailabilityStatus();
        if (availabilityStatus != AvailabilityStatus.ACTIVE) {
            return true;
        }

        if (updateItemCommand.getPrimaryVendorId() == null && item.getPrimaryVendorId() == null) {
            // Use backup vendor logic
            if (item.getBackupVendorId() == null) {
                log.warn("Backup vendor ID is null for item : {}", item.getSkuNumber());
                return true;
            }

            var backupVendorItem = vendorItemRepository.findByVendorIDAndItemId(item.getBackupVendorId(), item.getId());
            if (backupVendorItem == null) {
                log.warn("Backup vendor item not found for vendor ID: {} and item ID: {}",
                    item.getBackupVendorId(),
                    item.getId());
                return true;
            }

            primaryCost = backupVendorItem.getBackupPackPlusCrvCost();
        } else {
            // Use primary vendor logic
            UUID primaryVendorId = updateItemCommand.getPrimaryVendorId() == null ? item.getPrimaryVendorId()
                : updateItemCommand.getPrimaryVendorId();

            if (primaryVendorId == null) {
                log.warn("Primary vendor ID is null for item : {}", item.getSkuNumber());
                return true;
            }

            var primaryVendorItem = vendorItemRepository.findByVendorIDAndItemId(primaryVendorId, item.getId());
            if (primaryVendorItem == null) {
                log.warn("Primary vendor item not found for vendor ID: {} and item ID: {}", primaryVendorId, item.getId());
                return true;
            }

            primaryCost = primaryVendorItem.getPackPlusCrvCost();
        }
        if (primaryCost == null) {
            log.warn("Primary cost is null for item : {}", item.getSkuNumber());
            return true;
        }
        ItemRegPrice itemRegPrice = regPriceRepository.findByItemId(item.getId());

        // Check if ItemRegPrice exists
        if (itemRegPrice == null) {
            log.warn("ItemRegPrice not found for item id: {}", item.getId());
            return true;
        }

        BigDecimal newPrice =
            updateItemCommand.getRegPrice() == null ? itemRegPrice.getRegPrice() : updateItemCommand.getRegPrice();

        // Check if newPrice is null
        if (newPrice == null) {
            log.warn("New price is null for item : {}", item.getSkuNumber());
            return true;
        }

        if (newPrice.compareTo(primaryCost) < 0) {
            log.warn("Price for SKU :{} is too low: {}", item.getSkuNumber(), newPrice);
            return false;
        }
        return true;

    }


}
