package com.mercaso.ims.domain.itemvendorrebate;

import com.mercaso.ims.domain.BaseDomain;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.SuperBuilder;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.UUID;

/**
 * ItemVendorRebate domain entity
 * Represents rebate information for direct supplier items
 *
 * Business Rules:
 * - Start Date is required (when rebate becomes effective)
 * - End Date is optional (if null, rebate is continuous and valid indefinitely)
 * - Supplier must be a direct supplier
 * - SKU is required (item identification)
 * - Rebate Amount per Unit Sold is required (amount supplier refunds per sales unit)
 */
@Getter
@Setter
@ToString
@SuperBuilder
public class ItemVendorRebate extends BaseDomain {

    private final UUID id;

    private UUID vendorItemId;

    /** Direct supplier ID - must be a direct supplier */
    private UUID vendorId;

    /** Item ID associated with the SKU */
    private UUID itemId;

    /** Start Date - when the rebate becomes effective (required) */
    private LocalDate startDate;

    /** End Date - when rebate terminates (optional, null means continuous) */
    private LocalDate endDate;

    /** Rebate Amount per Unit Sold ($) - amount refunded per sales unit */
    private BigDecimal rebatePerUnit;

    /**
     * Check if the rebate is active for a given date
     * @param date the date to check
     * @return true if the rebate is active on the given date
     */
    public boolean isActiveOn(LocalDate date) {
        if (date == null || startDate == null) {
            return false;
        }

        // Must be on or after start date
        boolean afterStart = !date.isBefore(startDate);
        // If end date is null, rebate is continuous; otherwise must be on or before end date
        boolean beforeEnd = endDate == null || !date.isAfter(endDate);

        return afterStart && beforeEnd;
    }

    /**
     * Check if the rebate is currently active
     * @return true if the rebate is active today
     */
    public boolean isCurrentlyActive() {
        return isActiveOn(LocalDate.now());
    }

    /**
     * Check if the rebate has expired
     * @return true if the rebate has expired (only if end date is set and passed)
     */
    public boolean isExpired() {
        return endDate != null && LocalDate.now().isAfter(endDate);
    }

    /**
     * Check if the rebate has not started yet
     * @return true if the rebate has not started yet
     */
    public boolean isNotStarted() {
        return startDate != null && LocalDate.now().isBefore(startDate);
    }

    /**
     * Check if this is a continuous rebate (no end date)
     * @return true if the rebate is continuous (end date is null)
     */
    public boolean isContinuous() {
        return endDate == null;
    }

    /**
     * Calculate rebate amount for a given sales quantity
     * @param salesQuantity the sales quantity to calculate rebate for
     * @return the total rebate amount (amount supplier refunds)
     */
    public BigDecimal calculateRebateAmount(BigDecimal salesQuantity) {
        if (salesQuantity == null || rebatePerUnit == null) {
            return BigDecimal.ZERO;
        }
        if (salesQuantity.compareTo(BigDecimal.ZERO) <= 0) {
            return BigDecimal.ZERO;
        }
        return rebatePerUnit.multiply(salesQuantity);
    }

    /**
     * Get rebate description for display
     * @return formatted description of the rebate
     */
    public String getRebateDescription() {
        StringBuilder desc = new StringBuilder();
        desc.append("$").append(rebatePerUnit).append(" per unit sold");

        if (isContinuous()) {
            desc.append(" (continuous from ").append(startDate).append(")");
        } else {
            desc.append(" (").append(startDate).append(" to ").append(endDate).append(")");
        }

        return desc.toString();
    }
}
