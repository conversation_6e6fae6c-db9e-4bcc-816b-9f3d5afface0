package com.mercaso.ims.domain.itemvendorrebate;

import com.mercaso.ims.domain.BaseDomain;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.SuperBuilder;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.UUID;

/**
 * ItemVendorRebate domain entity
 * Represents rebate information for vendor items
 */
@Getter
@Setter
@ToString
@SuperBuilder
public class ItemVendorRebate extends BaseDomain {

    private final UUID id;

    private UUID vendorItemId;

    private UUID vendorId;

    private UUID itemId;

    private LocalDate startDate;

    private LocalDate endDate;

    private BigDecimal rebatePerUnit;

    /**
     * Check if the rebate is active for a given date
     * @param date the date to check
     * @return true if the rebate is active on the given date
     */
    public boolean isActiveOn(LocalDate date) {
        if (date == null) {
            return false;
        }
        
        boolean afterStart = startDate == null || !date.isBefore(startDate);
        boolean beforeEnd = endDate == null || !date.isAfter(endDate);
        
        return afterStart && beforeEnd;
    }

    /**
     * Check if the rebate is currently active
     * @return true if the rebate is active today
     */
    public boolean isCurrentlyActive() {
        return isActiveOn(LocalDate.now());
    }

    /**
     * Check if the rebate has expired
     * @return true if the rebate has expired
     */
    public boolean isExpired() {
        return endDate != null && LocalDate.now().isAfter(endDate);
    }

    /**
     * Check if the rebate has not started yet
     * @return true if the rebate has not started yet
     */
    public boolean isNotStarted() {
        return startDate != null && LocalDate.now().isBefore(startDate);
    }

    /**
     * Calculate rebate amount for a given quantity
     * @param quantity the quantity to calculate rebate for
     * @return the total rebate amount
     */
    public BigDecimal calculateRebateAmount(BigDecimal quantity) {
        if (quantity == null || rebatePerUnit == null) {
            return BigDecimal.ZERO;
        }
        return rebatePerUnit.multiply(quantity);
    }
}
