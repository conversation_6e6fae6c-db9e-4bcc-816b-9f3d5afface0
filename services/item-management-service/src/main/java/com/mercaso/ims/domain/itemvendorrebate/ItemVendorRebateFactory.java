package com.mercaso.ims.domain.itemvendorrebate;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.UUID;

/**
 * Factory for creating ItemVendorRebate domain entities
 */
@Slf4j
@Component
public class ItemVendorRebateFactory {

    /**
     * Create a new ItemVendorRebate entity
     * @param vendorItemId the vendor item ID
     * @param vendorId the vendor ID
     * @param itemId the item ID
     * @param startDate the start date of the rebate
     * @param endDate the end date of the rebate (can be null for indefinite)
     * @param rebatePerUnit the rebate amount per unit
     * @return the created ItemVendorRebate entity
     */
    public ItemVendorRebate create(UUID vendorItemId, UUID vendorId, UUID itemId, 
                                   LocalDate startDate, LocalDate endDate, BigDecimal rebatePerUnit) {
        log.debug("Creating ItemVendorRebate for vendorItemId: {}, vendorId: {}, itemId: {}", 
                  vendorItemId, vendorId, itemId);
        
        validateInputs(vendorItemId, vendorId, itemId, startDate, rebatePerUnit);
        
        return ItemVendorRebate.builder()
                .id(UUID.randomUUID())
                .vendorItemId(vendorItemId)
                .vendorId(vendorId)
                .itemId(itemId)
                .startDate(startDate)
                .endDate(endDate)
                .rebatePerUnit(rebatePerUnit)
                .build();
    }

    /**
     * Create a new ItemVendorRebate entity with indefinite end date
     * @param vendorItemId the vendor item ID
     * @param vendorId the vendor ID
     * @param itemId the item ID
     * @param startDate the start date of the rebate
     * @param rebatePerUnit the rebate amount per unit
     * @return the created ItemVendorRebate entity
     */
    public ItemVendorRebate createIndefinite(UUID vendorItemId, UUID vendorId, UUID itemId, 
                                             LocalDate startDate, BigDecimal rebatePerUnit) {
        return create(vendorItemId, vendorId, itemId, startDate, null, rebatePerUnit);
    }

    /**
     * Create a new ItemVendorRebate entity starting immediately
     * @param vendorItemId the vendor item ID
     * @param vendorId the vendor ID
     * @param itemId the item ID
     * @param endDate the end date of the rebate (can be null for indefinite)
     * @param rebatePerUnit the rebate amount per unit
     * @return the created ItemVendorRebate entity
     */
    public ItemVendorRebate createImmediate(UUID vendorItemId, UUID vendorId, UUID itemId, 
                                            LocalDate endDate, BigDecimal rebatePerUnit) {
        return create(vendorItemId, vendorId, itemId, LocalDate.now(), endDate, rebatePerUnit);
    }

    /**
     * Validate the input parameters for creating an ItemVendorRebate
     * @param vendorItemId the vendor item ID
     * @param vendorId the vendor ID
     * @param itemId the item ID
     * @param startDate the start date
     * @param rebatePerUnit the rebate per unit
     */
    private void validateInputs(UUID vendorItemId, UUID vendorId, UUID itemId, 
                                LocalDate startDate, BigDecimal rebatePerUnit) {
        if (vendorItemId == null) {
            throw new IllegalArgumentException("Vendor item ID cannot be null");
        }
        if (vendorId == null) {
            throw new IllegalArgumentException("Vendor ID cannot be null");
        }
        if (itemId == null) {
            throw new IllegalArgumentException("Item ID cannot be null");
        }
        if (startDate == null) {
            throw new IllegalArgumentException("Start date cannot be null");
        }
        if (rebatePerUnit == null) {
            throw new IllegalArgumentException("Rebate per unit cannot be null");
        }
        if (rebatePerUnit.compareTo(BigDecimal.ZERO) < 0) {
            throw new IllegalArgumentException("Rebate per unit cannot be negative");
        }
    }
}
