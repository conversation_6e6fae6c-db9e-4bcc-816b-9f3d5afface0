package com.mercaso.ims.domain.itemvendorrebate;

import com.mercaso.ims.domain.BaseDomainRepository;

import java.time.LocalDate;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

/**
 * Repository interface for ItemVendorRebate domain entity
 */
public interface ItemVendorRebateRepository extends BaseDomainRepository<ItemVendorRebate, UUID> {

    /**
     * Find rebates by vendor ID
     * @param vendorId the vendor ID
     * @return list of rebates for the vendor
     */
    List<ItemVendorRebate> findByVendorId(UUID vendorId);

    /**
     * Find rebates by item ID
     * @param itemId the item ID
     * @return list of rebates for the item
     */
    List<ItemVendorRebate> findByItemId(UUID itemId);

    /**
     * Find rebates by vendor item ID
     * @param vendorItemId the vendor item ID
     * @return list of rebates for the vendor item
     */
    List<ItemVendorRebate> findByVendorItemId(UUID vendorItemId);

    /**
     * Find rebates by vendor ID and item ID
     * @param vendorId the vendor ID
     * @param itemId the item ID
     * @return list of rebates for the vendor and item combination
     */
    List<ItemVendorRebate> findByVendorIdAndItemId(UUID vendorId, UUID itemId);

    /**
     * Find active rebates for a vendor and item on a specific date
     * @param vendorId the vendor ID
     * @param itemId the item ID
     * @param date the date to check
     * @return list of active rebates
     */
    List<ItemVendorRebate> findActiveRebates(UUID vendorId, UUID itemId, LocalDate date);

    /**
     * Find currently active rebates for a vendor and item
     * @param vendorId the vendor ID
     * @param itemId the item ID
     * @return list of currently active rebates
     */
    List<ItemVendorRebate> findCurrentlyActiveRebates(UUID vendorId, UUID itemId);

    /**
     * Find rebates that overlap with a given date range
     * @param vendorId the vendor ID
     * @param itemId the item ID
     * @param startDate the start date of the range
     * @param endDate the end date of the range
     * @return list of overlapping rebates
     */
    List<ItemVendorRebate> findOverlappingRebates(UUID vendorId, UUID itemId, LocalDate startDate, LocalDate endDate);

    /**
     * Check if a rebate exists for the given vendor, item, and start date combination
     * @param vendorItemId the vendor ID
     * @param startDate the start date
     * @return true if a rebate exists
     */
    boolean existsByVendorIdAndItemIdAndStartDate(UUID vendorItemId, LocalDate startDate);

    /**
     * Find rebates expiring within a date range
     * @param fromDate the start date of the range
     * @param toDate the end date of the range
     * @return list of rebates expiring within the range
     */
    List<ItemVendorRebate> findExpiringRebates(LocalDate fromDate, LocalDate toDate);
}
