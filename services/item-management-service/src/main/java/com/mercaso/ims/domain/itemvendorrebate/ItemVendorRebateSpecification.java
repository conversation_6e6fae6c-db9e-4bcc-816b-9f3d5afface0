package com.mercaso.ims.domain.itemvendorrebate;

import com.mercaso.ims.domain.Specification;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.time.LocalDate;
import java.util.List;

/**
 * Specification class for ItemVendorRebate business rules and validations
 */
@Slf4j
@Component
public class ItemVendorRebateSpecification implements Specification<ItemVendorRebate> {

    /**
     * Check if the rebate satisfies all business rules
     * @param rebate the rebate to validate
     * @return true if the rebate is valid
     */
    @Override
    public boolean isSatisfiedBy(ItemVendorRebate rebate) {
        return isValidDateRange(rebate) && 
               isValidRebateAmount(rebate) && 
               hasRequiredFields(rebate);
    }

    /**
     * Validate that the rebate has a valid date range
     * @param rebate the rebate to validate
     * @return true if the date range is valid
     */
    public boolean isValidDateRange(ItemVendorRebate rebate) {
        if (rebate.getStartDate() == null) {
            log.warn("Rebate start date cannot be null");
            return false;
        }
        
        if (rebate.getEndDate() != null && rebate.getEndDate().isBefore(rebate.getStartDate())) {
            log.warn("Rebate end date {} cannot be before start date {}", 
                     rebate.getEndDate(), rebate.getStartDate());
            return false;
        }
        
        return true;
    }

    /**
     * Validate that the rebate amount is valid
     * @param rebate the rebate to validate
     * @return true if the rebate amount is valid
     */
    public boolean isValidRebateAmount(ItemVendorRebate rebate) {
        if (rebate.getRebatePerUnit() == null) {
            log.warn("Rebate per unit cannot be null");
            return false;
        }
        
        if (rebate.getRebatePerUnit().signum() < 0) {
            log.warn("Rebate per unit cannot be negative: {}", rebate.getRebatePerUnit());
            return false;
        }
        
        return true;
    }

    /**
     * Validate that the rebate has all required fields
     * @param rebate the rebate to validate
     * @return true if all required fields are present
     */
    public boolean hasRequiredFields(ItemVendorRebate rebate) {
        if (rebate.getVendorId() == null) {
            log.warn("Vendor ID cannot be null");
            return false;
        }
        
        if (rebate.getItemId() == null) {
            log.warn("Item ID cannot be null");
            return false;
        }
        
        if (rebate.getVendorItemId() == null) {
            log.warn("Vendor Item ID cannot be null");
            return false;
        }
        
        return true;
    }

    /**
     * Check if rebates have overlapping date ranges
     * @param rebates list of rebates to check
     * @return true if any rebates overlap
     */
    public boolean hasOverlappingRebates(List<ItemVendorRebate> rebates) {
        if (rebates == null || rebates.size() < 2) {
            return false;
        }
        
        for (int i = 0; i < rebates.size(); i++) {
            for (int j = i + 1; j < rebates.size(); j++) {
                if (doRebatesOverlap(rebates.get(i), rebates.get(j))) {
                    log.warn("Found overlapping rebates: {} and {}", rebates.get(i).getId(), rebates.get(j).getId());
                    return true;
                }
            }
        }
        
        return false;
    }

    /**
     * Check if two rebates have overlapping date ranges
     * @param rebate1 the first rebate
     * @param rebate2 the second rebate
     * @return true if the rebates overlap
     */
    public boolean doRebatesOverlap(ItemVendorRebate rebate1, ItemVendorRebate rebate2) {
        LocalDate start1 = rebate1.getStartDate();
        LocalDate end1 = rebate1.getEndDate();
        LocalDate start2 = rebate2.getStartDate();
        LocalDate end2 = rebate2.getEndDate();
        
        // Handle null end dates (indefinite rebates)
        LocalDate effectiveEnd1 = end1 != null ? end1 : LocalDate.of(9999, 12, 31);
        LocalDate effectiveEnd2 = end2 != null ? end2 : LocalDate.of(9999, 12, 31);
        
        // Check for overlap: start1 <= effectiveEnd2 && start2 <= effectiveEnd1
        return !start1.isAfter(effectiveEnd2) && !start2.isAfter(effectiveEnd1);
    }

    /**
     * Check if a rebate is active on a specific date
     * @param rebate the rebate to check
     * @param date the date to check
     * @return true if the rebate is active on the date
     */
    public boolean isActiveOnDate(ItemVendorRebate rebate, LocalDate date) {
        return rebate.isActiveOn(date);
    }

    /**
     * Check if a rebate is currently active
     * @param rebate the rebate to check
     * @return true if the rebate is currently active
     */
    public boolean isCurrentlyActive(ItemVendorRebate rebate) {
        return rebate.isCurrentlyActive();
    }

    /**
     * Check if a rebate will expire within a specified number of days
     * @param rebate the rebate to check
     * @param days the number of days to look ahead
     * @return true if the rebate will expire within the specified days
     */
    public boolean willExpireWithinDays(ItemVendorRebate rebate, int days) {
        if (rebate.getEndDate() == null) {
            return false; // Indefinite rebates don't expire
        }
        
        LocalDate futureDate = LocalDate.now().plusDays(days);
        return rebate.getEndDate().isBefore(futureDate) || rebate.getEndDate().isEqual(futureDate);
    }
}
