# ItemVendorRebate Domain

This package contains the domain model and related components for managing vendor item rebates in the Item Management Service.

## Overview

The ItemVendorRebate entity represents rebate information for vendor items, allowing the system to track and calculate rebates based on vendor-item relationships and date ranges.

## Domain Model

### ItemVendorRebate Entity

The main domain entity that represents a rebate for a specific vendor-item combination.

**Key Fields:**
- `id`: Unique identifier (UUID)
- `vendorItemId`: Reference to the vendor item (UUID)
- `vendorId`: Reference to the vendor (UUID)
- `itemId`: Reference to the item (UUID)
- `startDate`: When the rebate becomes effective (LocalDate)
- `endDate`: When the rebate expires (LocalDate, nullable for indefinite rebates)
- `rebatePerUnit`: The rebate amount per unit (BigDecimal)

**Key Methods:**
- `isActiveOn(LocalDate date)`: Check if rebate is active on a specific date
- `isCurrentlyActive()`: Check if rebate is currently active
- `isExpired()`: Check if rebate has expired
- `isNotStarted()`: Check if rebate has not started yet
- `calculateRebateAmount(BigDecimal quantity)`: Calculate total rebate for a quantity

## Components

### 1. Domain Layer

#### ItemVendorRebate
The main domain entity with business logic for rebate calculations and status checks.

#### ItemVendorRebateRepository
Repository interface defining data access methods for rebates.

#### ItemVendorRebateFactory
Factory class for creating new ItemVendorRebate instances with proper validation.

#### ItemVendorRebateService
Domain service containing complex business logic for rebate operations.

#### ItemVendorRebateSpecification
Specification class for business rules and validations.

### 2. Infrastructure Layer

#### ItemVendorRebateDo
JPA entity (Data Object) for database persistence.

#### ItemVendorRebateJpaRepository
Spring Data JPA repository with custom query methods.

#### ItemVendorRebateRepositoryImpl
Implementation of the domain repository interface.

#### ItemVendorRebateDoMapper
MapStruct mapper for converting between domain entities and data objects.

### 3. Application Layer

#### ItemVendorRebateApplicationService
Application service orchestrating domain operations and transactions.

#### ItemVendorRebateDto
Data Transfer Object for API communication.

### 4. Interface Layer

#### ItemVendorRebateController
REST controller providing HTTP endpoints for rebate operations.

## Database Schema

The `item_vendor_rebate` table has the following structure:

```sql
CREATE TABLE item_vendor_rebate (
    id UUID PRIMARY KEY,
    vendor_item_id UUID NOT NULL,
    vendor_id UUID NOT NULL,
    item_id UUID NOT NULL,
    start_date DATE NOT NULL,
    end_date DATE,
    rebate_per_unit DECIMAL(10, 2) NOT NULL,
    CONSTRAINT uq_item_vendor_rebate UNIQUE (vendor_id, item_id, start_date)
);
```

## Usage Examples

### Creating a Rebate

```java
@Autowired
private ItemVendorRebateFactory factory;

@Autowired
private ItemVendorRebateRepository repository;

// Create a new rebate
ItemVendorRebate rebate = factory.create(
    vendorItemId,
    vendorId,
    itemId,
    LocalDate.of(2024, 1, 1),
    LocalDate.of(2024, 12, 31),
    new BigDecimal("5.00")
);

// Save the rebate
repository.save(rebate);
```

### Calculating Rebates

```java
@Autowired
private ItemVendorRebateService rebateService;

// Calculate total rebate for a quantity
BigDecimal totalRebate = rebateService.calculateTotalRebate(
    vendorId,
    itemId,
    new BigDecimal("100"), // quantity
    LocalDate.now()
);
```

### Querying Active Rebates

```java
@Autowired
private ItemVendorRebateRepository repository;

// Find currently active rebates
List<ItemVendorRebate> activeRebates = repository.findCurrentlyActiveRebates(vendorId, itemId);

// Find rebates active on a specific date
List<ItemVendorRebate> rebatesOnDate = repository.findActiveRebates(vendorId, itemId, specificDate);
```

## API Endpoints

The REST controller provides the following endpoints:

- `POST /api/v1/item-vendor-rebates` - Create a new rebate
- `GET /api/v1/item-vendor-rebates/{id}` - Get rebate by ID
- `PUT /api/v1/item-vendor-rebates/{id}` - Update an existing rebate
- `DELETE /api/v1/item-vendor-rebates/{id}` - Delete a rebate
- `GET /api/v1/item-vendor-rebates/vendor/{vendorId}` - Get rebates by vendor
- `GET /api/v1/item-vendor-rebates/item/{itemId}` - Get rebates by item
- `GET /api/v1/item-vendor-rebates/vendor-item/{vendorItemId}` - Get rebates by vendor item
- `GET /api/v1/item-vendor-rebates/active` - Get currently active rebates
- `GET /api/v1/item-vendor-rebates/calculate-rebate` - Calculate total rebate amount
- `GET /api/v1/item-vendor-rebates/expiring` - Get expiring rebates

## Business Rules

1. **Unique Constraint**: Only one rebate can exist per vendor-item-start_date combination
2. **Date Validation**: End date must be after or equal to start date
3. **Amount Validation**: Rebate per unit must be non-negative
4. **Overlap Prevention**: The system validates against overlapping rebate periods
5. **Required Fields**: Vendor ID, Item ID, Vendor Item ID, Start Date, and Rebate Per Unit are mandatory

## Testing

Unit tests are provided for the domain entity to verify business logic:
- Date range validations
- Rebate calculations
- Status checks (active, expired, not started)

Run tests with:
```bash
./gradlew test --tests "*ItemVendorRebateTest"
```
