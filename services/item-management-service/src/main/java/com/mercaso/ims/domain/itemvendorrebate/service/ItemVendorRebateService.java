package com.mercaso.ims.domain.itemvendorrebate.service;

import com.mercaso.ims.domain.itemvendorrebate.ItemVendorRebate;
import com.mercaso.ims.domain.itemvendorrebate.ItemVendorRebateRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;
import java.util.UUID;

/**
 * Domain service for ItemVendorRebate business logic
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ItemVendorRebateService {

    private final ItemVendorRebateRepository itemVendorRebateRepository;

    /**
     * Calculate total rebate amount for a vendor and item on a specific date
     * @param vendorId the vendor ID
     * @param itemId the item ID
     * @param quantity the quantity
     * @param date the date to calculate rebate for
     * @return the total rebate amount
     */
    public BigDecimal calculateTotalRebate(UUID vendorId, UUID itemId, BigDecimal quantity, LocalDate date) {
        log.debug("Calculating total rebate for vendorId: {}, itemId: {}, quantity: {}, date: {}", 
                  vendorId, itemId, quantity, date);
        
        if (quantity == null || quantity.compareTo(BigDecimal.ZERO) <= 0) {
            return BigDecimal.ZERO;
        }

        List<ItemVendorRebate> activeRebates = itemVendorRebateRepository.findActiveRebates(vendorId, itemId, date);
        
        return activeRebates.stream()
                .map(rebate -> rebate.calculateRebateAmount(quantity))
                .reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    /**
     * Calculate current total rebate amount for a vendor and item
     * @param vendorId the vendor ID
     * @param itemId the item ID
     * @param quantity the quantity
     * @return the current total rebate amount
     */
    public BigDecimal calculateCurrentTotalRebate(UUID vendorId, UUID itemId, BigDecimal quantity) {
        return calculateTotalRebate(vendorId, itemId, quantity, LocalDate.now());
    }

    /**
     * Get the highest rebate per unit for a vendor and item on a specific date
     * @param vendorId the vendor ID
     * @param itemId the item ID
     * @param date the date to check
     * @return the highest rebate per unit, or BigDecimal.ZERO if no rebates found
     */
    public BigDecimal getHighestRebatePerUnit(UUID vendorId, UUID itemId, LocalDate date) {
        log.debug("Getting highest rebate per unit for vendorId: {}, itemId: {}, date: {}", 
                  vendorId, itemId, date);
        
        List<ItemVendorRebate> activeRebates = itemVendorRebateRepository.findActiveRebates(vendorId, itemId, date);
        
        return activeRebates.stream()
                .map(ItemVendorRebate::getRebatePerUnit)
                .max(BigDecimal::compareTo)
                .orElse(BigDecimal.ZERO);
    }

    /**
     * Check if there are any active rebates for a vendor and item
     * @param vendorId the vendor ID
     * @param itemId the item ID
     * @return true if there are active rebates
     */
    public boolean hasActiveRebates(UUID vendorId, UUID itemId) {
        log.debug("Checking if there are active rebates for vendorId: {}, itemId: {}", vendorId, itemId);
        
        List<ItemVendorRebate> activeRebates = itemVendorRebateRepository.findCurrentlyActiveRebates(vendorId, itemId);
        return !activeRebates.isEmpty();
    }

    /**
     * Validate that a new rebate doesn't conflict with existing ones
     * @param startDate the start date of the new rebate
     * @param endDate the end date of the new rebate (can be null)
     * @return true if the rebate is valid (no conflicts)
     */
    public boolean validateRebateSchedule(UUID vendorItemId, LocalDate startDate, LocalDate endDate) {
        log.debug("Validating rebate schedule for vendorItemId: {}, startDate: {}, endDate: {}",
                vendorItemId, startDate, endDate);
        
        // Check for exact duplicate start dates
        if (itemVendorRebateRepository.existsByVendorIdAndItemIdAndStartDate(vendorItemId, startDate)) {
            log.warn("Rebate with same start date already exists for vendorItemId: {}, startDate: {}",
                    vendorItemId, startDate);
            return false;
        }

        // Check for overlapping rebates
        LocalDate effectiveEndDate = endDate != null ? endDate : LocalDate.of(9999, 12, 31); // Far future date for indefinite rebates
        List<ItemVendorRebate> overlappingRebates = itemVendorRebateRepository.findOverlappingRebates(
                vendorId, itemId, startDate, effectiveEndDate);
        
        if (!overlappingRebates.isEmpty()) {
            log.warn("Found {} overlapping rebates for vendorId: {}, itemId: {}, period: {} to {}", 
                     overlappingRebates.size(), vendorId, itemId, startDate, endDate);
            return false;
        }

        return true;
    }

    /**
     * Get rebates that are expiring soon (within the next specified days)
     * @param days the number of days to look ahead
     * @return list of rebates expiring within the specified period
     */
    public List<ItemVendorRebate> getExpiringRebates(int days) {
        log.debug("Getting rebates expiring within {} days", days);
        
        LocalDate today = LocalDate.now();
        LocalDate futureDate = today.plusDays(days);
        
        return itemVendorRebateRepository.findExpiringRebates(today, futureDate);
    }

    /**
     * Get all rebates for a specific vendor item
     * @param vendorItemId the vendor item ID
     * @return list of rebates for the vendor item
     */
    public List<ItemVendorRebate> getRebatesForVendorItem(UUID vendorItemId) {
        log.debug("Getting rebates for vendorItemId: {}", vendorItemId);
        return itemVendorRebateRepository.findByVendorItemId(vendorItemId);
    }
}
