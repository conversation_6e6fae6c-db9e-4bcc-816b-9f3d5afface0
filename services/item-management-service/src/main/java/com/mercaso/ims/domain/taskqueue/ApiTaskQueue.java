package com.mercaso.ims.domain.taskqueue;

import com.mercaso.ims.domain.BaseDomain;
import com.mercaso.ims.domain.taskqueue.enums.TaskStatus;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.time.Instant;
import java.util.UUID;

/**
 * API Task Queue domain object
 * Represents a queued API task with rate limiting and retry capabilities
 */
@Data
@SuperBuilder
@EqualsAndHashCode(callSuper = true)
public class ApiTaskQueue extends BaseDomain {

    /**
     * Unique identifier for the task
     */
    private UUID id;

    /**
     * Type of the task (e.g., FINALE_GET_PRODUCT, FINALE_UPDATE_STOCK)
     */
    private String taskType;

    /**
     * API endpoint URL or identifier
     */
    private String apiEndpoint;

    /**
     * HTTP method (GET, POST, PUT, DELETE, PATCH)
     */
    private String httpMethod;

    /**
     * Request payload in JSON format
     */
    private String requestPayload;

    /**
     * Response payload in JSON format
     */
    private String responsePayload;

    /**
     * Current status of the task
     */
    private TaskStatus status;

    /**
     * Task priority (higher number = higher priority)
     */
    private Integer priority;

    /**
     * Maximum number of retry attempts
     */
    private Integer maxRetryCount;

    /**
     * Current retry attempt count
     */
    private Integer currentRetryCount;

    /**
     * Scheduled execution time (for delayed execution or retry)
     */
    private Instant scheduledAt;

    /**
     * Actual start time of task execution
     */
    private Instant startedAt;

    /**
     * Task completion time
     */
    private Instant completedAt;

    /**
     * Error message if task failed
     */
    private String errorMessage;

    /**
     * Check if task can be executed
     */
    public boolean canExecute() {
        return status != null && status.canExecute() && 
               (scheduledAt == null || scheduledAt.isBefore(Instant.now()));
    }

    /**
     * Check if task has reached maximum retry attempts
     */
    public boolean hasReachedMaxRetries() {
        return currentRetryCount != null && maxRetryCount != null && 
               currentRetryCount >= maxRetryCount;
    }

    /**
     * Increment retry count
     */
    public void incrementRetryCount() {
        if (currentRetryCount == null) {
            currentRetryCount = 1;
        } else {
            currentRetryCount++;
        }
    }

    /**
     * Mark task as started
     */
    public void markAsStarted() {
        this.status = TaskStatus.PROCESSING;
        this.startedAt = Instant.now();
    }

    /**
     * Mark task as completed successfully
     */
    public void markAsCompleted(String responsePayload) {
        this.status = TaskStatus.COMPLETED;
        this.completedAt = Instant.now();
        this.responsePayload = responsePayload;
        this.errorMessage = null;
    }

    /**
     * Mark task as failed
     */
    public void markAsFailed(String errorMessage) {
        this.status = TaskStatus.FAILED;
        this.completedAt = Instant.now();
        this.errorMessage = errorMessage;
    }

    /**
     * Mark task for retry with exponential backoff
     */
    public void markForRetry(String errorMessage, long backoffSeconds) {
        incrementRetryCount();
        this.status = TaskStatus.RETRY;
        this.errorMessage = errorMessage;
        this.scheduledAt = Instant.now().plusSeconds(backoffSeconds);
    }
}
