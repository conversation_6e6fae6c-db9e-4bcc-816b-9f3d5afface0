package com.mercaso.ims.domain.taskqueue.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum TaskStatus {


    PENDING("PENDING", "Waiting for execution"),


    PROCESSING("PROCESSING", "Executing"),


    COMPLETED("COMPLETED", "Execution successful"),


    FAILED("FAILED", "Execution failed"),


    RETRY("RETRY", "Wait to retry");

    private final String code;
    private final String description;

    public static TaskStatus fromCode(String code) {
        for (TaskStatus status : values()) {
            if (status.code.equals(code)) {
                return status;
            }
        }
        throw new IllegalArgumentException("Unknown task status code: " + code);
    }


    public boolean isTerminal() {
        return this == COMPLETED || this == FAILED;
    }


    public boolean canExecute() {
        return this == PENDING || this == RETRY;
    }
}
