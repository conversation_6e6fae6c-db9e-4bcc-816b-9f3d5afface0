package com.mercaso.ims.domain.taskqueue.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * API task type enumeration
 * Maps to different rate limiter configurations based on external API limits.
 * Currently supports Finale API with the following limits:
 * - 120 updates (POST) per minute
 * - 120 single item fetches (GET specific entity) per minute
 * - 300 collection requests or reports per hour
 *
 * This enum can be extended to support other external APIs in the future.
 */
@Getter
@AllArgsConstructor
public enum TaskType {

    FINALE_GET_PRODUCT("FINALE_GET_PRODUCT", TaskType.FINALE_SINGLE_ENTITY_GET, "Get single product"),
    FINALE_GET_PURCHASE_ORDER("FINALE_GET_PURCHASE_ORDER", TaskType.FINALE_SINGLE_ENTITY_GET, "Query purchase orders"),
    FINALE_CHECK_ITEM_UNIQUE("FINALE_CHECK_ITEM_UNIQUE", TaskType.FINALE_SINGLE_ENTITY_GET, "Check item unique"),



    FINALE_GET_VENDORS("FINALE_GET_VENDORS", TaskType.FINALE_COLLECTION_REQUESTS, "Query vendors"),
    FINALE_GET_PURCHASE_ORDERS("FINALE_GET_PURCHASE_ORDERS", TaskType.FINALE_COLLECTION_REQUESTS, "Query purchase orders"),



    FINALE_UPDATE_VENDOR_ITEM("FINALE_UPDATE_VENDOR_ITEM", TaskType.FINALE_UPDATE_OPERATIONS, "Update vendor item"),
    FINALE_CREATE_VENDOR("FINALE_CREATE_VENDOR", TaskType.FINALE_UPDATE_OPERATIONS, "Create vendor"),
    FINALE_UPDATE_VENDOR("FINALE_UPDATE_VENDOR", TaskType.FINALE_UPDATE_OPERATIONS, "Update vendor"),
    FINALE_CREATE_ITEM("FINALE_CREATE_ITEM", TaskType.FINALE_UPDATE_OPERATIONS, "Create item"),
    FINALE_UPDATE_ITEM("FINALE_UPDATE_ITEM", TaskType.FINALE_UPDATE_OPERATIONS, "Update item"),

    ;


    private final String type;
    private final String rateLimiterName;
    private final String description;

    private static final String FINALE_SINGLE_ENTITY_GET = "finaleSingleEntityGet";
    private static final String FINALE_COLLECTION_REQUESTS = "finaleCollectionRequests";
    private static final String FINALE_UPDATE_OPERATIONS = "finaleUpdateOperations";

    /**
     * Get task type by name
     */
    public static TaskType fromTaskType(String taskType) {
        for (TaskType type : values()) {
            if (type.type.equals(taskType)) {
                return type;
            }
        }
        throw new IllegalArgumentException("Unknown task type: " + taskType);
    }
}
