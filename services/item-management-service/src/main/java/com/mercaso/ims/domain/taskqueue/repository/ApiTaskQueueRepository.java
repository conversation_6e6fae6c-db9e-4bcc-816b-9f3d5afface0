package com.mercaso.ims.domain.taskqueue.repository;

import com.mercaso.ims.domain.taskqueue.ApiTaskQueue;
import com.mercaso.ims.domain.taskqueue.enums.TaskStatus;

import java.time.Instant;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

/**
 * Repository interface for API Task Queue domain objects
 */
public interface ApiTaskQueueRepository {

    /**
     * Save a task
     *
     * @param task Task to save
     * @return Saved task
     */
    ApiTaskQueue save(ApiTaskQueue task);

    /**
     * Find task by ID
     *
     * @param id Task ID
     * @return Task if found
     */
    Optional<ApiTaskQueue> findById(UUID id);

    /**
     * Find executable tasks
     *
     * @param statuses Allowed statuses for execution
     * @param limit Maximum number of tasks to return
     * @return List of executable tasks
     */
    List<ApiTaskQueue> findExecutableTasks(List<TaskStatus> statuses, int limit);

    /**
     * Find distinct task types by status
     *
     * @param statuses Task statuses to filter by
     * @return List of distinct task types
     */
    List<String> findDistinctTaskTypesByStatus(List<TaskStatus> statuses);

    /**
     * Find tasks by status
     *
     * @param status Task status
     * @return List of tasks with the specified status
     */
    List<ApiTaskQueue> findByStatus(TaskStatus status);

    /**
     * Find tasks by task type and status
     *
     * @param taskType Task type
     * @param status Task status
     * @return List of tasks with the specified type and status
     */
    List<ApiTaskQueue> findByTaskTypeAndStatus(String taskType, TaskStatus status);

    /**
     * Count tasks by status
     *
     * @param status Task status
     * @return Number of tasks with the specified status
     */
    long countByStatus(TaskStatus status);

    /**
     * Count tasks by task type and status
     *
     * @param taskType Task type
     * @param status Task status
     * @return Number of tasks with the specified type and status
     */
    long countByTaskTypeAndStatus(String taskType, TaskStatus status);

    /**
     * Find tasks that are stuck in processing state
     *
     * @param cutoffTime Tasks started before this time are considered stuck
     * @return List of stuck tasks
     */
    List<ApiTaskQueue> findStuckProcessingTasks(Instant cutoffTime);

    /**
     * Clean up completed tasks older than specified time
     *
     * @param cutoffTime Tasks completed before this time will be deleted
     * @return Number of tasks cleaned up
     */
    int cleanupCompletedTasks(Instant cutoffTime);

    /**
     * Delete a task
     *
     * @param task Task to delete
     */
    void delete(ApiTaskQueue task);

    /**
     * Delete task by ID
     *
     * @param id Task ID
     */
    void deleteById(UUID id);
}
