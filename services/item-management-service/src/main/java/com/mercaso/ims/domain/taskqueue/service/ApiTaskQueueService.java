package com.mercaso.ims.domain.taskqueue.service;

import com.mercaso.ims.domain.taskqueue.ApiTaskQueue;
import com.mercaso.ims.domain.taskqueue.enums.TaskStatus;

import java.time.Duration;
import java.time.Instant;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

/**
 * Domain service for API Task Queue operations
 */
public interface ApiTaskQueueService {

    /**
     * Create a new API task
     *
     * @param taskType Task type identifier
     * @param apiEndpoint API endpoint or identifier
     * @param httpMethod HTTP method
     * @param requestPayload Request payload in JSON format
     * @param priority Task priority (higher number = higher priority)
     * @param maxRetryCount Maximum retry attempts
     * @return Created task
     */
    ApiTaskQueue createTask(String taskType, String apiEndpoint, String httpMethod, 
                           String requestPayload, Integer priority, Integer maxRetryCount);

    /**
     * Create a scheduled API task
     *
     * @param taskType Task type identifier
     * @param apiEndpoint API endpoint or identifier
     * @param httpMethod HTTP method
     * @param requestPayload Request payload in JSON format
     * @param scheduledAt When to execute the task
     * @return Created task
     */
    ApiTaskQueue createScheduledTask(String taskType, String apiEndpoint, String httpMethod, 
                                   String requestPayload, Instant scheduledAt);

    /**
     * Get executable tasks for processing
     *
     * @param limit Maximum number of tasks to return
     * @return List of executable tasks
     */
    List<ApiTaskQueue> getExecutableTasks(int limit);

    /**
     * Get all distinct task types that have pending tasks
     *
     * @return List of task types with pending tasks
     */
    List<String> getPendingTaskTypes();

    /**
     * Get task by ID
     *
     * @param taskId Task ID
     * @return Task if found
     */
    Optional<ApiTaskQueue> getTaskById(UUID taskId);

    /**
     * Update task status
     *
     * @param taskId Task ID
     * @param status New status
     * @return Updated task
     */
    ApiTaskQueue updateTaskStatus(UUID taskId, TaskStatus status);

    /**
     * Mark task as started
     *
     * @param taskId Task ID
     * @return Updated task
     */
    ApiTaskQueue markTaskAsStarted(UUID taskId);

    /**
     * Mark task as completed with response
     *
     * @param taskId Task ID
     * @param responsePayload Response payload in JSON format
     * @return Updated task
     */
    ApiTaskQueue markTaskAsCompleted(UUID taskId, String responsePayload);

    /**
     * Mark task as failed
     *
     * @param taskId Task ID
     * @param errorMessage Error message
     * @return Updated task
     */
    ApiTaskQueue markTaskAsFailed(UUID taskId, String errorMessage);

    /**
     * Mark task for retry
     *
     * @param taskId Task ID
     * @param errorMessage Error message
     * @param retryDelay Delay before retry
     * @return Updated task
     */
    ApiTaskQueue markTaskForRetry(UUID taskId, String errorMessage, Duration retryDelay);

    /**
     * Get tasks by status
     *
     * @param status Task status
     * @return List of tasks with the specified status
     */
    List<ApiTaskQueue> getTasksByStatus(TaskStatus status);

    /**
     * Get task count by status
     *
     * @param status Task status
     * @return Number of tasks with the specified status
     */
    long getTaskCountByStatus(TaskStatus status);

    /**
     * Get task count by task type and status
     *
     * @param taskType Task type
     * @param status Task status
     * @return Number of tasks with the specified type and status
     */
    long getTaskCountByTypeAndStatus(String taskType, TaskStatus status);

    /**
     * Find tasks that are stuck in processing state
     *
     * @param timeout How long a task can be in processing state before considered stuck
     * @return List of stuck tasks
     */
    List<ApiTaskQueue> findStuckTasks(Duration timeout);

    /**
     * Reset stuck tasks back to pending state
     *
     * @param timeout How long a task can be in processing state before considered stuck
     * @return Number of tasks reset
     */
    int resetStuckTasks(Duration timeout);

    /**
     * Clean up completed tasks older than specified duration
     *
     * @param olderThan Tasks completed before this duration will be deleted
     * @return Number of tasks cleaned up
     */
    int cleanupCompletedTasks(Duration olderThan);

    /**
     * Wait for task completion (for synchronous operations)
     *
     * @param taskId Task ID to wait for
     * @param timeout Maximum time to wait
     * @return Completed task if finished within timeout
     */
    Optional<ApiTaskQueue> waitForTaskCompletion(UUID taskId, Duration timeout);
}
