package com.mercaso.ims.domain.taskqueue.service.impl;

import com.mercaso.ims.domain.taskqueue.ApiTaskQueue;
import com.mercaso.ims.domain.taskqueue.enums.TaskStatus;
import com.mercaso.ims.domain.taskqueue.repository.ApiTaskQueueRepository;
import com.mercaso.ims.domain.taskqueue.service.ApiTaskQueueService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.Duration;
import java.time.Instant;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

/**
 * Implementation of API Task Queue service
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ApiTaskQueueServiceImpl implements ApiTaskQueueService {

    private static final int DEFAULT_PRIORITY = 0;
    private static final int DEFAULT_MAX_RETRY_COUNT = 3;
    public static final String TASK_NOT_FOUND_MESSAGE = "Task not found: ";

    private final ApiTaskQueueRepository apiTaskQueueRepository;

    @Override
    @Transactional
    public ApiTaskQueue createTask(String taskType, String apiEndpoint, String httpMethod, 
                                  String requestPayload, Integer priority, Integer maxRetryCount) {
        ApiTaskQueue task = ApiTaskQueue.builder()
                .taskType(taskType)
                .apiEndpoint(apiEndpoint)
                .httpMethod(httpMethod)
                .requestPayload(requestPayload)
                .status(TaskStatus.PENDING)
                .priority(priority != null ? priority : DEFAULT_PRIORITY)
                .maxRetryCount(maxRetryCount != null ? maxRetryCount : DEFAULT_MAX_RETRY_COUNT)
                .currentRetryCount(0)
                .build();

        return apiTaskQueueRepository.save(task);
    }

    @Override
    @Transactional
    public ApiTaskQueue createScheduledTask(String taskType, String apiEndpoint, String httpMethod, 
                                          String requestPayload, Instant scheduledAt) {
        ApiTaskQueue task = ApiTaskQueue.builder()
                .taskType(taskType)
                .apiEndpoint(apiEndpoint)
                .httpMethod(httpMethod)
                .requestPayload(requestPayload)
                .status(TaskStatus.PENDING)
                .priority(DEFAULT_PRIORITY)
                .maxRetryCount(DEFAULT_MAX_RETRY_COUNT)
                .currentRetryCount(0)
                .scheduledAt(scheduledAt)
                .build();

        return apiTaskQueueRepository.save(task);
    }

    @Override
    public List<ApiTaskQueue> getExecutableTasks(int limit) {
        List<TaskStatus> executableStatuses = List.of(TaskStatus.PENDING, TaskStatus.RETRY);
        List<ApiTaskQueue> executableTasks = apiTaskQueueRepository.findExecutableTasks(executableStatuses, limit);
        return executableTasks.stream()
                .filter(taskDo -> taskDo.getScheduledAt() == null || taskDo.getScheduledAt().isBefore(Instant.now()))
                // Re-sort to maintain execution order: priority DESC, then created_at ASC
                .sorted((t1, t2) -> {
                    // First compare by priority (higher priority first)
                    int priorityCompare = Integer.compare(t2.getPriority(), t1.getPriority());
                    if (priorityCompare != 0) {
                        return priorityCompare;
                    }
                    // Then compare by created_at (earlier created first) to maintain execution order
                    return t1.getCreatedAt().compareTo(t2.getCreatedAt());
                })
                .limit(limit)
                .toList();
    }

    @Override
    public List<String> getPendingTaskTypes() {
        List<TaskStatus> executableStatuses = List.of(TaskStatus.PENDING, TaskStatus.RETRY);
        return apiTaskQueueRepository.findDistinctTaskTypesByStatus(executableStatuses);
    }

    @Override
    public Optional<ApiTaskQueue> getTaskById(UUID taskId) {
        return apiTaskQueueRepository.findById(taskId);
    }

    @Override
    @Transactional
    public ApiTaskQueue updateTaskStatus(UUID taskId, TaskStatus status) {
        ApiTaskQueue task = apiTaskQueueRepository.findById(taskId)
                .orElseThrow(() -> new IllegalArgumentException(TASK_NOT_FOUND_MESSAGE + taskId));
        
        task.setStatus(status);
        return apiTaskQueueRepository.save(task);
    }

    @Override
    @Transactional
    public ApiTaskQueue markTaskAsStarted(UUID taskId) {
        ApiTaskQueue task = apiTaskQueueRepository.findById(taskId)
                .orElseThrow(() -> new IllegalArgumentException(TASK_NOT_FOUND_MESSAGE + taskId));
        
        task.markAsStarted();
        return apiTaskQueueRepository.save(task);
    }

    @Override
    @Transactional
    public ApiTaskQueue markTaskAsCompleted(UUID taskId, String responsePayload) {
        ApiTaskQueue task = apiTaskQueueRepository.findById(taskId)
                .orElseThrow(() -> new IllegalArgumentException(TASK_NOT_FOUND_MESSAGE + taskId));
        
        task.markAsCompleted(responsePayload);
        return apiTaskQueueRepository.save(task);
    }

    @Override
    @Transactional
    public ApiTaskQueue markTaskAsFailed(UUID taskId, String errorMessage) {
        ApiTaskQueue task = apiTaskQueueRepository.findById(taskId)
                .orElseThrow(() -> new IllegalArgumentException(TASK_NOT_FOUND_MESSAGE + taskId));
        
        task.markAsFailed(errorMessage);
        return apiTaskQueueRepository.save(task);
    }

    @Override
    @Transactional
    public ApiTaskQueue markTaskForRetry(UUID taskId, String errorMessage, Duration retryDelay) {
        ApiTaskQueue task = apiTaskQueueRepository.findById(taskId)
                .orElseThrow(() -> new IllegalArgumentException(TASK_NOT_FOUND_MESSAGE + taskId));
        
        if (task.hasReachedMaxRetries()) {
            log.warn("Task {} has reached maximum retry count, marking as failed", taskId);
            task.markAsFailed(errorMessage + " (Max retries exceeded)");
        } else {
            task.markForRetry(errorMessage, retryDelay.getSeconds());
        }
        
        return apiTaskQueueRepository.save(task);
    }

    @Override
    public List<ApiTaskQueue> getTasksByStatus(TaskStatus status) {
        return apiTaskQueueRepository.findByStatus(status);
    }

    @Override
    public long getTaskCountByStatus(TaskStatus status) {
        return apiTaskQueueRepository.countByStatus(status);
    }

    @Override
    public long getTaskCountByTypeAndStatus(String taskType, TaskStatus status) {
        return apiTaskQueueRepository.countByTaskTypeAndStatus(taskType, status);
    }

    @Override
    public List<ApiTaskQueue> findStuckTasks(Duration timeout) {
        Instant cutoffTime = Instant.now().minus(timeout);
        return apiTaskQueueRepository.findStuckProcessingTasks(cutoffTime);
    }

    @Override
    @Transactional
    public int resetStuckTasks(Duration timeout) {
        List<ApiTaskQueue> stuckTasks = findStuckTasks(timeout);
        int resetCount = 0;
        
        for (ApiTaskQueue task : stuckTasks) {
            log.warn("Resetting stuck task {} that has been processing since {}", 
                    task.getId(), task.getStartedAt());
            task.setStatus(TaskStatus.PENDING);
            task.setStartedAt(null);
            task.setErrorMessage("Reset due to timeout");
            apiTaskQueueRepository.save(task);
            resetCount++;
        }
        
        return resetCount;
    }

    @Override
    @Transactional
    public int cleanupCompletedTasks(Duration olderThan) {
        Instant cutoffTime = Instant.now().minus(olderThan);
        return apiTaskQueueRepository.cleanupCompletedTasks(cutoffTime);
    }

    @Override
    public Optional<ApiTaskQueue> waitForTaskCompletion(UUID taskId, Duration timeout) {
        Instant deadline = Instant.now().plus(timeout);
        
        while (Instant.now().isBefore(deadline)) {
            Optional<ApiTaskQueue> taskOpt = getTaskById(taskId);
            if (taskOpt.isPresent() && taskOpt.get().getStatus().isTerminal()) {
                return taskOpt;
            }
            
            try {
                Thread.sleep(1000); // Poll every second
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                break;
            }
        }
        
        return Optional.empty();
    }
}
