package com.mercaso.ims.domain.vendor;

import com.mercaso.ims.application.command.CreateVendorCommand;

public class VendorFactory {

    private VendorFactory() {
    }

    public static Vendor createVendor(CreateVendorCommand command) {
        Vendor vendor = Vendor.builder()
            .vendorName(command.getVendorName())
            .externalPicking(command.getExternalPicking())
            .shutdownWindowEnabled(command.getShutdownWindowEnabled())
            .build();
        vendor.setShutdownWindow(command.getShutdownWindowEnabled());
        return vendor;
    }
}
