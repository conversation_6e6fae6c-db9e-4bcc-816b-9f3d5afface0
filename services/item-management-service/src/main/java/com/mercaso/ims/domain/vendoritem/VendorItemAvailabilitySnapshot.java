package com.mercaso.ims.domain.vendoritem;

import com.mercaso.ims.domain.BaseDomain;
import com.mercaso.ims.domain.vendoritem.enums.SnapshotType;
import java.time.Instant;
import java.util.List;
import java.util.UUID;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.SuperBuilder;

@Getter
@Setter
@ToString
@SuperBuilder
public class VendorItemAvailabilitySnapshot extends BaseDomain {

    private final UUID id;
    private UUID vendorId;
    private Instant snapshotTime;
    private SnapshotType snapshotType;
    private List<VendorItemAvailabilitySnapshotDetail> details;
} 