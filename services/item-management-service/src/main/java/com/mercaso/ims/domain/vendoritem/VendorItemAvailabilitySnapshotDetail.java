package com.mercaso.ims.domain.vendoritem;

import com.mercaso.ims.domain.BaseDomain;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.SuperBuilder;

import java.util.UUID;

@Getter
@Setter
@ToString
@SuperBuilder
public class VendorItemAvailabilitySnapshotDetail extends BaseDomain {

    private final UUID id;
    private UUID snapshotId;
    private UUID vendorItemId;
    private String vendorSkuNumber;
    private UUID itemId;
    private Boolean previousAvailability;
    private Boolean newAvailability;
} 