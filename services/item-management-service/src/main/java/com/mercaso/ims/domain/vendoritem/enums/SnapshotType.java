package com.mercaso.ims.domain.vendoritem.enums;

public enum SnapshotType {
    SHUTDOWN("SHUTDOWN", "Shutdown_Snapshot"),
    RESTORE("RESTORE", "Restore_Snapshot");

    private final String code;
    private final String description;

    SnapshotType(String code, String description) {
        this.code = code;
        this.description = description;
    }

    public String getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }

    public static SnapshotType fromCode(String code) {
        for (SnapshotType type : values()) {
            if (type.getCode().equals(code)) {
                return type;
            }
        }
        throw new IllegalArgumentException("Unknown snapshot type: " + code);
    }
}