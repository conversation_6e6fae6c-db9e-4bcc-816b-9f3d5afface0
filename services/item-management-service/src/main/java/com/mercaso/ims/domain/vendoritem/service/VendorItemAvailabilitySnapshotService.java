package com.mercaso.ims.domain.vendoritem.service;

import com.mercaso.ims.domain.vendoritem.VendorItemAvailabilitySnapshot;
import com.mercaso.ims.domain.vendoritem.VendorItemAvailabilitySnapshotDetail;
import com.mercaso.ims.domain.vendoritem.enums.SnapshotType;

import java.time.Instant;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

public interface VendorItemAvailabilitySnapshotService {

    VendorItemAvailabilitySnapshot save(VendorItemAvailabilitySnapshot snapshot);

    VendorItemAvailabilitySnapshot findById(UUID id);

    List<VendorItemAvailabilitySnapshot> findByVendorIdAndSnapshotTypeOrderBySnapshotTimeDesc(UUID vendorId, SnapshotType snapshotType);

    Optional<VendorItemAvailabilitySnapshot> findLatestByVendorIdAndSnapshotType(UUID vendorId, SnapshotType snapshotType);

    void createShutdownSnapshot(UUID vendorId, List<VendorItemAvailabilitySnapshotDetail> details);

    void createRestoreSnapshot(UUID vendorId, List<VendorItemAvailabilitySnapshotDetail> details);

    List<VendorItemAvailabilitySnapshotDetail> getLatestShutdownSnapshotDetails(UUID vendorId);
} 