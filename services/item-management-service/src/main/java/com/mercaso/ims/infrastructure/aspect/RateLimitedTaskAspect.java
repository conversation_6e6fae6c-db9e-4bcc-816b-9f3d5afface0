package com.mercaso.ims.infrastructure.aspect;

import com.mercaso.featureflags.service.FeatureFlagsManager;
import com.mercaso.ims.infrastructure.annotation.RateLimitedTask;
import com.mercaso.ims.infrastructure.apitaskprocess.TaskRouter;
import com.mercaso.ims.infrastructure.apitaskprocess.TaskExecutionContext;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import static com.mercaso.ims.infrastructure.contant.FeatureFlagKeys.IMS_ADD_RATE_LIMITS_FOR_FINALE_API;

/**
 * Simplified AOP Aspect for handling @RateLimitedTask annotation
 * Routes between synchronous (direct rate limiting) and asynchronous (task queue) execution
 */
@Aspect
@Component
@Order(1)
@Slf4j
@RequiredArgsConstructor
public class RateLimitedTaskAspect {

    private final TaskRouter taskRouter;

    private final FeatureFlagsManager featureFlagsManager;

    /**
     * Around advice for methods annotated with @RateLimitedTask
     * Routes to appropriate execution strategy
     * If currently executing within a task processor, bypasses rate limiting to avoid infinite loops
     */
    @Around("@annotation(rateLimitedTask)")
    public Object handleRateLimitedTask(ProceedingJoinPoint joinPoint, RateLimitedTask rateLimitedTask) throws Throwable {
        // If we're already in a task execution context, bypass rate limiting to avoid infinite loops
        if (TaskExecutionContext.isInTaskExecution()) {
            log.info("Bypassing rate limiting for method {} as it's executing within task processor context",
                     joinPoint.getSignature().getName());
            return joinPoint.proceed();
        }

        boolean featureOn = featureFlagsManager.isFeatureOn(IMS_ADD_RATE_LIMITS_FOR_FINALE_API);
        return featureOn ? taskRouter.routeTask(joinPoint, rateLimitedTask) : joinPoint.proceed();
    }
}
