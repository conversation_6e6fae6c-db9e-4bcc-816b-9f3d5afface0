package com.mercaso.ims.infrastructure.config;

import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.EnableAspectJAutoProxy;

/**
 * Configuration for enabling AOP support for @RateLimitedTask annotation
 */
@Configuration
@EnableAspectJAutoProxy(proxyTargetClass = true)
public class RateLimitedTaskConfig {

    // AOP configuration is handled by the annotation
    // This class ensures that AspectJ auto proxy is enabled
}
