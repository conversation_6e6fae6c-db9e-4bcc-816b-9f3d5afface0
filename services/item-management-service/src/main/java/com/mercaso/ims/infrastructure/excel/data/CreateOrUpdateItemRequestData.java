package com.mercaso.ims.infrastructure.excel.data;


import com.alibaba.excel.annotation.ExcelProperty;
import java.math.BigDecimal;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;

@Data
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class CreateOrUpdateItemRequestData extends ItemAdjustmentRequestData {

    @ExcelProperty("Status")
    private String status;
    @ExcelProperty("Company ID")
    private String companyId;
    @ExcelProperty("Location ID")
    private String locationId;
    @ExcelProperty("Primary Direct Vendor")
    private String primaryPoVendor;
    @ExcelProperty("Primary JIT Vendor")
    private String primaryJitVendor;
    @ExcelProperty("SKU")
    private String sku;
    @ExcelProperty("Primary Vendor Item Number")
    private String primaryVendorItemNumber;
    @ExcelProperty("Notes")
    private String notes;
    @ExcelProperty("Item Description")
    private String itemDescription;
    @ExcelProperty("New Description")
    private String newDescription;
    @ExcelProperty("Pack Size")
    private Integer packSize;
    @ExcelProperty("Item Size")
    private String itemSize;
    @ExcelProperty("Item Unit Measure")
    private String itemUnitMeasure;
    @ExcelProperty("CRV flag")
    private Boolean crvFlag;
    @ExcelProperty("Department")
    private String department;
    @ExcelProperty("Category")
    private String category;
    @ExcelProperty("Sub-Category")
    private String subCategory;
    @ExcelProperty("Class")
    private String classType;
    @ExcelProperty("Brand")
    private String brand;
    @ExcelProperty("Reg. Price (Pack)")
    private BigDecimal regPricePack;
    @ExcelProperty("Case UPC")
    private String caseUpc;
    @ExcelProperty("Each UPC")
    private String eachUpc;
    @ExcelProperty("Missing Each UPC Reason")
    private String missingEachUPCReason;
    @ExcelProperty("Missing Case UPC Reason")
    private String missingCaseUPCReason;
    @ExcelProperty("Length")
    private Double length;
    @ExcelProperty("Height")
    private Double height;
    @ExcelProperty("Width")
    private Double width;
    @ExcelProperty("Each Weight")
    private Double eachWeight;
    @ExcelProperty("Each Weight unit")
    private String eachWeightUnit;
    @ExcelProperty("Case Weight")
    private Double caseWeight;
    @ExcelProperty("Case Weight unit")
    private String caseWeightUnit;
    @ExcelProperty("Cooler")
    private Boolean cooler;
    @ExcelProperty("High Value")
    private Boolean highValue;
    @ExcelProperty("Primary Direct Vendor Item Cost")
    private BigDecimal primaryPoVendorItemCost;
    @ExcelProperty("Primary JIT Vendor Item Cost")
    private BigDecimal primaryJitVendorItemCost;
    @ExcelProperty("Primary Vendor Item Aisle")
    private String primaryVendorItemAisle;
    @ExcelProperty("Promo Price (Pack)")
    private BigDecimal promoPrice;
    @ExcelProperty("Promo flag")
    private Boolean promoFlag;
    @ExcelProperty("Image url")
    private String imageUrl;

    @ExcelProperty("Tags")
    private String tags;

    @ExcelProperty("Append Tags")
    private String appendTags;

    @ExcelProperty("Archived Reason")
    private String archivedReason;


    public String getDepartment() {
        return StringUtils.isBlank(department) ? "" : department.trim();
    }

    public String getCategory() {
        return StringUtils.isBlank(category) ? "" : category.trim();
    }

    public String getSubCategory() {
        return StringUtils.isBlank(subCategory) ? "" : subCategory.trim();
    }

    public String getClassType() {
        return StringUtils.isBlank(classType) ? "" : classType.trim();
    }

    public boolean isCategoryChanged() {
        return StringUtils.isNotBlank(department) || StringUtils.isNotBlank(category) || StringUtils.isNotBlank(subCategory)
            || StringUtils.isNotBlank(classType);
    }

    public boolean isPriceChanged() {
        return regPricePack != null || promoPrice != null || promoFlag != null || crvFlag != null
            || StringUtils.isNotBlank(itemUnitMeasure) || itemSize != null;
    }

}
