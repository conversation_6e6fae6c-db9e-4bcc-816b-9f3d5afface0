package com.mercaso.ims.infrastructure.external.finale.dto;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

import lombok.*;

import java.util.stream.IntStream;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class FinaleOrderCollectionDto {

    // Arrays from the API response
    private List<String> orderId;
    private List<String> orderUrl;
    private List<String> orderTypeId;
    private List<LocalDateTime> lastUpdatedDate;
    private List<LocalDateTime> createdDate;
    private List<String> settlementTermId;
    private List<String> fulfillmentId;
    private List<LocalDateTime> orderDate;
    private List<LocalDateTime> receiveDate;
    private List<BigDecimal> orderItemListTotal;
    private List<String> statusId;
    private List<String> destinationFacilityUrl;

    // Complex nested objects
    private List<List<ContactMech>> contactMechList;
    private List<List<OrderItem>> orderItemList;
    private List<List<Object>> orderAdjustmentList;
    private List<List<OrderRole>> orderRoleList;
    private List<List<Object>> contentList;
    private List<List<UserFieldData>> userFieldDataList;
    private List<List<String>> shipmentUrlList;
    private List<List<String>> invoiceUrlList;
    private List<List<String>> connectionRelationUrlList;
    private List<List<StatusHistory>> statusIdHistoryList;

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    public static class ContactMech {
        private String contactMechId;
        private String contactMechTypeId;
        private String contactMechPurposeTypeId;
        private String toName;
        private String address1;
        private String address2;
        private String city;
        private String stateProvinceGeoId;
        private String postalCode;
        private String statusId;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    public static class OrderItem {
        private String orderItemUrl;
        private String reserveUrl;
        private String productId;
        private String productUrl;
        private BigDecimal unitPrice;
        private Integer quantity;
        private String normalizedPackingString;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    public static class OrderRole {
        private String roleTypeId;
        private String partyId;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    public static class UserFieldData {
        private String attrName;
        private String attrValue;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    public static class StatusHistory {
        private String statusId;
        private Long txStamp;
        private String userLoginUrl;
    }

    // Helper methods to work with the data

    /**
     * Get the total number of orders in the collection
     */
    public int getOrderCount() {
        return orderId != null ? orderId.size() : 0;
    }

    /**
     * Get order at specific index as a structured object
     */
    public OrderSummary getOrderAt(int index) {
        if (index < 0 || index >= getOrderCount()) {
            return null;
        }

        return OrderSummary.builder()
                .orderId(getValueAt(orderId, index))
                .orderUrl(getValueAt(orderUrl, index))
                .orderTypeId(getValueAt(orderTypeId, index))
                .lastUpdatedDate(getValueAt(lastUpdatedDate, index))
                .createdDate(getValueAt(createdDate, index))
                .settlementTermId(getValueAt(settlementTermId, index))
                .fulfillmentId(getValueAt(fulfillmentId, index))
                .orderDate(getValueAt(orderDate, index))
                .receiveDate(getValueAt(receiveDate, index))
                .orderItemListTotal(getValueAt(orderItemListTotal, index))
                .statusId(getValueAt(statusId, index))
                .destinationFacilityUrl(getValueAt(destinationFacilityUrl, index))
                .contactMechList(getValueAt(contactMechList, index))
                .orderItemList(getValueAt(orderItemList, index))
                .orderRoleList(getValueAt(orderRoleList, index))
                .userFieldDataList(getValueAt(userFieldDataList, index))
                .shipmentUrlList(getValueAt(shipmentUrlList, index))
                .invoiceUrlList(getValueAt(invoiceUrlList, index))
                .statusIdHistoryList(getValueAt(statusIdHistoryList, index))
                .build();
    }

    /**
     * Get all orders as a list of structured objects
     */
    public List<OrderSummary> getAllOrders() {
        return IntStream.range(0, getOrderCount())
                .mapToObj(this::getOrderAt)
                .toList();
    }

    /**
     * Filter orders by order type
     */
    public List<OrderSummary> getOrdersByType(String orderType) {
        return getAllOrders().stream()
                .filter(order -> orderType.equals(order.getOrderTypeId()))
                .toList();
    }

    /**
     * Get purchase orders only
     */
    public List<OrderSummary> getPurchaseOrders() {
        return getOrdersByType("PURCHASE_ORDER");
    }

    /**
     * Filter orders by status
     */
    public List<OrderSummary> getOrdersByStatus(String status) {
        return getAllOrders().stream()
                .filter(order -> status.equals(order.getStatusId()))
                .toList();
    }

    private <T> T getValueAt(List<T> list, int index) {
        return (list != null && index < list.size()) ? list.get(index) : null;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    @ToString
    public static class OrderSummary {
        private String orderId;
        private String orderUrl;
        private String orderTypeId;
        private LocalDateTime lastUpdatedDate;
        private LocalDateTime createdDate;
        private String settlementTermId;
        private String fulfillmentId;
        private LocalDateTime orderDate;
        private LocalDateTime receiveDate;
        private BigDecimal orderItemListTotal;
        private String statusId;
        private String destinationFacilityUrl;
        private List<ContactMech> contactMechList;
        private List<OrderItem> orderItemList;
        private List<OrderRole> orderRoleList;
        private List<UserFieldData> userFieldDataList;
        private List<String> shipmentUrlList;
        private List<String> invoiceUrlList;
        private List<StatusHistory> statusIdHistoryList;

        /**
         * Get supplier information from order roles
         */
        public String getSupplierId() {
            if (orderRoleList == null) return null;
            return orderRoleList.stream()
                    .filter(role -> "SUPPLIER".equals(role.getRoleTypeId()))
                    .findFirst()
                    .map(OrderRole::getPartyId)
                    .orElse(null);
        }

        /**
         * Check if this is a purchase order
         */
        public boolean isPurchaseOrder() {
            return "PURCHASE_ORDER".equals(orderTypeId);
        }

        /**
         * Check if the order is completed
         */
        public boolean isCompleted() {
            return "ORDER_COMPLETED".equals(statusId);
        }

        /**
         * Get the total number of items in this order
         */
        public int getItemCount() {
            return orderItemList != null ? orderItemList.size() : 0;
        }
    }
}