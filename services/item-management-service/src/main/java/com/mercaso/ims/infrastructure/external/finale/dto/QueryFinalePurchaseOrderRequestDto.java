package com.mercaso.ims.infrastructure.external.finale.dto;

import java.nio.charset.StandardCharsets;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.mercaso.ims.infrastructure.exception.ImsBusinessException;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import java.util.Base64;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class QueryFinalePurchaseOrderRequestDto {

    private List<String> lastUpdatedDate;

    private List<String> statusId;

    private List<String> orderTypeId;

    /**
     * Generates a Base64 encoded string of the JSON representation of this object.
     * This is used as a filter parameter in Finale Inventory API calls.
     * @return Base64 encoded string of the JSON filter
     */
    @JsonIgnore
    public String getBase64EncodedFilter() {
        try {
            ObjectMapper mapper = new ObjectMapper();
            String json = mapper.writeValueAsString(this);
            return Base64.getEncoder().encodeToString(json.getBytes(StandardCharsets.UTF_8));
        } catch (Exception e) {
            log.error("Failed to encode filter to Base64", e);
            throw new ImsBusinessException("Failed to encode filter to Base64", e);
        }
    }
}
