package com.mercaso.ims.infrastructure.repository.itemvendorrebate;

import com.mercaso.ims.domain.itemvendorrebate.ItemVendorRebate;
import com.mercaso.ims.domain.itemvendorrebate.ItemVendorRebateRepository;
import com.mercaso.ims.infrastructure.repository.itemvendorrebate.jpa.ItemVendorRebateJpaRepository;
import com.mercaso.ims.infrastructure.repository.itemvendorrebate.jpa.dataobject.ItemVendorRebateDo;
import com.mercaso.ims.infrastructure.repository.itemvendorrebate.mapper.ItemVendorRebateDoMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;

import java.time.LocalDate;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

/**
 * Implementation of ItemVendorRebateRepository
 */
@Slf4j
@Repository
@RequiredArgsConstructor
public class ItemVendorRebateRepositoryImpl implements ItemVendorRebateRepository {

    private final ItemVendorRebateJpaRepository jpaRepository;
    private final ItemVendorRebateDoMapper mapper = ItemVendorRebateDoMapper.INSTANCE;

    @Override
    public ItemVendorRebate save(ItemVendorRebate itemVendorRebate) {
        log.debug("Saving ItemVendorRebate: {}", itemVendorRebate);
        ItemVendorRebateDo dataObject = mapper.toDataObject(itemVendorRebate);
        ItemVendorRebateDo savedDataObject = jpaRepository.save(dataObject);
        return mapper.toDomainEntity(savedDataObject);
    }

    @Override
    public ItemVendorRebate findById(UUID id) {
        log.debug("Finding ItemVendorRebate by id: {}", id);
        return jpaRepository.findById(id)
                .map(mapper::toDomainEntity);
    }

    @Override
    public List<ItemVendorRebate> findAll() {
        log.debug("Finding all ItemVendorRebates");
        List<ItemVendorRebateDo> dataObjects = jpaRepository.findAll();
        return mapper.toDomainEntities(dataObjects);
    }

    @Override
    public void deleteById(UUID id) {
        log.debug("Deleting ItemVendorRebate by id: {}", id);
        jpaRepository.deleteById(id);
    }

    @Override
    public boolean existsById(UUID id) {
        log.debug("Checking if ItemVendorRebate exists by id: {}", id);
        return jpaRepository.existsById(id);
    }

    @Override
    public List<ItemVendorRebate> findByVendorId(UUID vendorId) {
        log.debug("Finding ItemVendorRebates by vendorId: {}", vendorId);
        List<ItemVendorRebateDo> dataObjects = jpaRepository.findByVendorId(vendorId);
        return mapper.toDomainEntities(dataObjects);
    }

    @Override
    public List<ItemVendorRebate> findByItemId(UUID itemId) {
        log.debug("Finding ItemVendorRebates by itemId: {}", itemId);
        List<ItemVendorRebateDo> dataObjects = jpaRepository.findByItemId(itemId);
        return mapper.toDomainEntities(dataObjects);
    }

    @Override
    public List<ItemVendorRebate> findByVendorItemId(UUID vendorItemId) {
        log.debug("Finding ItemVendorRebates by vendorItemId: {}", vendorItemId);
        List<ItemVendorRebateDo> dataObjects = jpaRepository.findByVendorItemId(vendorItemId);
        return mapper.toDomainEntities(dataObjects);
    }

    @Override
    public List<ItemVendorRebate> findByVendorIdAndItemId(UUID vendorId, UUID itemId) {
        log.debug("Finding ItemVendorRebates by vendorId: {} and itemId: {}", vendorId, itemId);
        List<ItemVendorRebateDo> dataObjects = jpaRepository.findByVendorIdAndItemId(vendorId, itemId);
        return mapper.toDomainEntities(dataObjects);
    }

    @Override
    public List<ItemVendorRebate> findActiveRebates(UUID vendorId, UUID itemId, LocalDate date) {
        log.debug("Finding active ItemVendorRebates for vendorId: {}, itemId: {}, date: {}", vendorId, itemId, date);
        List<ItemVendorRebateDo> dataObjects = jpaRepository.findActiveRebates(vendorId, itemId, date);
        return mapper.toDomainEntities(dataObjects);
    }

    @Override
    public List<ItemVendorRebate> findCurrentlyActiveRebates(UUID vendorId, UUID itemId) {
        log.debug("Finding currently active ItemVendorRebates for vendorId: {}, itemId: {}", vendorId, itemId);
        List<ItemVendorRebateDo> dataObjects = jpaRepository.findCurrentlyActiveRebates(vendorId, itemId);
        return mapper.toDomainEntities(dataObjects);
    }

    @Override
    public List<ItemVendorRebate> findOverlappingRebates(UUID vendorId, UUID itemId, LocalDate startDate, LocalDate endDate) {
        log.debug("Finding overlapping ItemVendorRebates for vendorId: {}, itemId: {}, startDate: {}, endDate: {}", 
                  vendorId, itemId, startDate, endDate);
        List<ItemVendorRebateDo> dataObjects = jpaRepository.findOverlappingRebates(vendorId, itemId, startDate, endDate);
        return mapper.toDomainEntities(dataObjects);
    }

    @Override
    public boolean existsByVendorIdAndItemIdAndStartDate(UUID vendorItemId, LocalDate startDate) {
        log.debug("Checking if ItemVendorRebate exists for vendorItemId: {}, startDate: {}", vendorItemId, startDate);
        return jpaRepository.existsByVendorItemIdAndStartDate(vendorItemId, startDate);
    }

    @Override
    public List<ItemVendorRebate> findExpiringRebates(LocalDate fromDate, LocalDate toDate) {
        log.debug("Finding expiring ItemVendorRebates from: {} to: {}", fromDate, toDate);
        List<ItemVendorRebateDo> dataObjects = jpaRepository.findByEndDateBetween(fromDate, toDate);
        return mapper.toDomainEntities(dataObjects);
    }
}
