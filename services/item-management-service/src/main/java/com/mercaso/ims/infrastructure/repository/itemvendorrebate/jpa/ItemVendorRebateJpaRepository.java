package com.mercaso.ims.infrastructure.repository.itemvendorrebate.jpa;

import com.mercaso.ims.infrastructure.repository.itemvendorrebate.jpa.dataobject.ItemVendorRebateDo;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDate;
import java.util.List;
import java.util.UUID;

/**
 * JPA Repository for ItemVendorRebate
 */
@Repository
public interface ItemVendorRebateJpaRepository extends JpaRepository<ItemVendorRebateDo, UUID> {

    /**
     * Find rebates by vendor ID
     */
    List<ItemVendorRebateDo> findByVendorId(UUID vendorId);

    /**
     * Find rebates by item ID
     */
    List<ItemVendorRebateDo> findByItemId(UUID itemId);

    /**
     * Find rebates by vendor item ID
     */
    List<ItemVendorRebateDo> findByVendorItemId(UUID vendorItemId);

    /**
     * Find rebates by vendor ID and item ID
     */
    List<ItemVendorRebateDo> findByVendorIdAndItemId(UUID vendorId, UUID itemId);

    /**
     * Find active rebates for a vendor and item on a specific date
     */
    @Query("SELECT r FROM ItemVendorRebateDo r WHERE r.vendorId = :vendorId AND r.itemId = :itemId " +
           "AND r.startDate <= :date AND (r.endDate IS NULL OR r.endDate >= :date)")
    List<ItemVendorRebateDo> findActiveRebates(@Param("vendorId") UUID vendorId, 
                                               @Param("itemId") UUID itemId, 
                                               @Param("date") LocalDate date);

    /**
     * Find rebates that overlap with a given date range
     */
    @Query("SELECT r FROM ItemVendorRebateDo r WHERE r.vendorId = :vendorId AND r.itemId = :itemId " +
           "AND r.startDate <= :endDate AND (r.endDate IS NULL OR r.endDate >= :startDate)")
    List<ItemVendorRebateDo> findOverlappingRebates(@Param("vendorId") UUID vendorId,
                                                     @Param("itemId") UUID itemId,
                                                     @Param("startDate") LocalDate startDate,
                                                     @Param("endDate") LocalDate endDate);

    /**
     * Check if a rebate exists for the given vendor, item, and start date combination
     */
    boolean existsByVendorIdAndItemIdAndStartDate(UUID vendorId, UUID itemId, LocalDate startDate);

    /**
     * Find rebates expiring within a date range
     */
    @Query("SELECT r FROM ItemVendorRebateDo r WHERE r.endDate BETWEEN :fromDate AND :toDate")
    List<ItemVendorRebateDo> findByEndDateBetween(@Param("fromDate") LocalDate fromDate, 
                                                   @Param("toDate") LocalDate toDate);

    /**
     * Find currently active rebates for a vendor and item
     */
    @Query("SELECT r FROM ItemVendorRebateDo r WHERE r.vendorId = :vendorId AND r.itemId = :itemId " +
           "AND r.startDate <= CURRENT_DATE AND (r.endDate IS NULL OR r.endDate >= CURRENT_DATE)")
    List<ItemVendorRebateDo> findCurrentlyActiveRebates(@Param("vendorId") UUID vendorId, 
                                                         @Param("itemId") UUID itemId);
}
