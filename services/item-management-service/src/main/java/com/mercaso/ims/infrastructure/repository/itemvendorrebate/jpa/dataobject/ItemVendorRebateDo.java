package com.mercaso.ims.infrastructure.repository.itemvendorrebate.jpa.dataobject;

import com.mercaso.ims.infrastructure.repository.BaseDo;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;
import lombok.experimental.SuperBuilder;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.SQLRestriction;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.UUID;

/**
 * Data Object for ItemVendorRebate entity
 *
 * Business Rules:
 * - Start Date: Required - when rebate becomes effective
 * - End Date: Optional - if null, rebate is continuous and valid indefinitely
 * - Supplier: Required - must be a direct supplier
 * - SKU: Required - item identification
 * - Rebate Amount per Unit Sold: Required - amount supplier refunds per sales unit
 */
@Entity
@ToString(callSuper = true)
@Table(name = "item_vendor_rebate")
@Data
@SuperBuilder
@AllArgsConstructor
@NoArgsConstructor
@SQLDelete(sql = "update item_vendor_rebate set deleted_at = current_timestamp where id = ? and updated_at = ?")
@SQLRestriction("deleted_at is null")
@EqualsAndHashCode(callSuper = true)
public class ItemVendorRebateDo extends BaseDo {

    @Column(name = "vendor_item_id", nullable = false)
    private UUID vendorItemId;

    /** Direct Supplier ID - must be a direct supplier */
    @Column(name = "vendor_id", nullable = false)
    private UUID vendorId;

    /** Item ID associated with the SKU */
    @Column(name = "item_id", nullable = false)
    private UUID itemId;

    /** Start Date - when rebate becomes effective (REQUIRED) */
    @Column(name = "start_date", nullable = false)
    private LocalDate startDate;

    /** End Date - when rebate terminates (OPTIONAL - null means continuous) */
    @Column(name = "end_date")
    private LocalDate endDate;

    /** Rebate Amount per Unit Sold ($) - amount refunded per sales unit */
    @Column(name = "rebate_per_unit", nullable = false, precision = 10, scale = 2)
    private BigDecimal rebatePerUnit;
}
