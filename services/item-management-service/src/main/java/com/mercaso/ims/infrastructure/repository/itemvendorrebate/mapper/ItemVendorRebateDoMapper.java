package com.mercaso.ims.infrastructure.repository.itemvendorrebate.mapper;

import com.mercaso.ims.domain.itemvendorrebate.ItemVendorRebate;
import com.mercaso.ims.infrastructure.repository.itemvendorrebate.jpa.dataobject.ItemVendorRebateDo;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * Mapper for converting between ItemVendorRebate domain entity and ItemVendorRebateDo data object
 */
@Mapper
public interface ItemVendorRebateDoMapper {

    ItemVendorRebateDoMapper INSTANCE = Mappers.getMapper(ItemVendorRebateDoMapper.class);

    /**
     * Convert domain entity to data object
     * @param itemVendorRebate the domain entity
     * @return the data object
     */
    ItemVendorRebateDo toDataObject(ItemVendorRebate itemVendorRebate);

    /**
     * Convert data object to domain entity
     * @param itemVendorRebateDo the data object
     * @return the domain entity
     */
    ItemVendorRebate toDomainEntity(ItemVendorRebateDo itemVendorRebateDo);

    /**
     * Convert list of domain entities to list of data objects
     * @param itemVendorRebates the list of domain entities
     * @return the list of data objects
     */
    List<ItemVendorRebateDo> toDataObjects(List<ItemVendorRebate> itemVendorRebates);

    /**
     * Convert list of data objects to list of domain entities
     * @param itemVendorRebateDos the list of data objects
     * @return the list of domain entities
     */
    List<ItemVendorRebate> toDomainEntities(List<ItemVendorRebateDo> itemVendorRebateDos);
}
