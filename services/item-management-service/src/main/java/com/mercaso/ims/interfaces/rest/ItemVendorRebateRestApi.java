package com.mercaso.ims.interfaces.rest;

import com.mercaso.ims.application.command.BatchCreateItemVendorRebateCommand;
import com.mercaso.ims.application.command.CalculateRebateCommand;
import com.mercaso.ims.application.command.CreateItemVendorRebateCommand;
import com.mercaso.ims.application.command.DeleteItemVendorRebateCommand;
import com.mercaso.ims.application.command.UpdateItemVendorRebateCommand;
import com.mercaso.ims.application.dto.ItemVendorRebateDto;
import com.mercaso.ims.application.service.ItemVendorRebateApplicationService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;
import java.util.UUID;

/**
 * REST Controller for ItemVendorRebate operations
 *
 * Manages rebates for direct suppliers:
 * - Start Date: Required - when rebate becomes effective
 * - End Date: Optional - if empty, rebate is continuous and valid indefinitely
 * - Supplier: Must be direct supplier
 * - SKU: Required - item identification
 * - Rebate Amount per Unit Sold: Required - amount supplier refunds per sales unit
 */
@Slf4j
@RestController
@RequestMapping("/api/v1/item-vendor-rebates")
@RequiredArgsConstructor
@Tag(name = "Item Vendor Rebate", description = "Direct Supplier Rebate management APIs")
public class ItemVendorRebateRestApi {

    private final ItemVendorRebateApplicationService itemVendorRebateApplicationService;

    @PostMapping
    @Operation(summary = "Create a new direct supplier rebate",
               description = "Creates a rebate for a direct supplier. Start Date is required, End Date is optional (null means continuous rebate).")
    @PreAuthorize("hasAuthority('ims:write:vendor-items')")
    public ItemVendorRebateDto createRebate(@RequestBody CreateItemVendorRebateCommand command) {
        log.info("Creating new item vendor rebate for vendorItemId: {}", command.getVendorItemId());

        Assert.isTrue(command.isValid(), command.getValidationError());

        return itemVendorRebateApplicationService.createRebate(command);
    }

    @GetMapping("/{id}")
    @Operation(summary = "Get item vendor rebate by ID")
    public ResponseEntity<ItemVendorRebateDto> getRebateById(
            @Parameter(description = "Rebate ID") @PathVariable UUID id) {
        log.debug("Getting item vendor rebate by id: {}", id);
        
        return itemVendorRebateApplicationService.getRebateById(id)
                .map(rebate -> ResponseEntity.ok(rebate))
                .orElse(ResponseEntity.notFound().build());
    }

    @PutMapping("/{id}")
    @Operation(summary = "Update an existing direct supplier rebate",
               description = "Updates a rebate for a direct supplier. All fields are required except End Date (null means continuous rebate).")
    @PreAuthorize("hasAuthority('ims:write:vendor-items')")
    public ItemVendorRebateDto updateRebate(
            @Parameter(description = "Rebate ID") @PathVariable UUID id,
            @RequestBody UpdateItemVendorRebateCommand command) {
        log.info("Updating item vendor rebate with id: {}", id);

        Assert.isTrue(command.getId().equals(id), "Rebate ID in path must match command ID");
        Assert.isTrue(command.isValid(), command.getValidationError());

        return itemVendorRebateApplicationService.updateRebate(command);
    }

    @DeleteMapping("/{id}")
    @Operation(summary = "Delete a direct supplier rebate")
    @PreAuthorize("hasAuthority('ims:write:vendor-items')")
    public void deleteRebate(
            @Parameter(description = "Rebate ID") @PathVariable UUID id) {
        log.info("Deleting item vendor rebate with id: {}", id);

        DeleteItemVendorRebateCommand command = DeleteItemVendorRebateCommand.builder()
                .id(id)
                .build();

        Assert.isTrue(command.isValid(), command.getValidationError());

        itemVendorRebateApplicationService.deleteRebate(command.getId());
    }

    @GetMapping("/vendor/{vendorId}")
    @Operation(summary = "Get all rebates for a vendor")
    public ResponseEntity<List<ItemVendorRebateDto>> getRebatesByVendor(
            @Parameter(description = "Vendor ID") @PathVariable UUID vendorId) {
        log.debug("Getting rebates for vendorId: {}", vendorId);
        
        List<ItemVendorRebateDto> rebates = itemVendorRebateApplicationService.getRebatesByVendor(vendorId);
        return ResponseEntity.ok(rebates);
    }

    @GetMapping("/item/{itemId}")
    @Operation(summary = "Get all rebates for an item")
    public ResponseEntity<List<ItemVendorRebateDto>> getRebatesByItem(
            @Parameter(description = "Item ID") @PathVariable UUID itemId) {
        log.debug("Getting rebates for itemId: {}", itemId);
        
        List<ItemVendorRebateDto> rebates = itemVendorRebateApplicationService.getRebatesByItem(itemId);
        return ResponseEntity.ok(rebates);
    }

    @GetMapping("/vendor-item/{vendorItemId}")
    @Operation(summary = "Get all rebates for a vendor item")
    public ResponseEntity<List<ItemVendorRebateDto>> getRebatesByVendorItem(
            @Parameter(description = "Vendor Item ID") @PathVariable UUID vendorItemId) {
        log.debug("Getting rebates for vendorItemId: {}", vendorItemId);
        
        List<ItemVendorRebateDto> rebates = itemVendorRebateApplicationService.getRebatesByVendorItem(vendorItemId);
        return ResponseEntity.ok(rebates);
    }

    @GetMapping("/active")
    @Operation(summary = "Get currently active rebates for a vendor and item")
    public ResponseEntity<List<ItemVendorRebateDto>> getCurrentlyActiveRebates(
            @Parameter(description = "Vendor ID") @RequestParam UUID vendorId,
            @Parameter(description = "Item ID") @RequestParam UUID itemId) {
        log.debug("Getting currently active rebates for vendorId: {}, itemId: {}", vendorId, itemId);
        
        List<ItemVendorRebateDto> rebates = itemVendorRebateApplicationService.getCurrentlyActiveRebates(vendorId, itemId);
        return ResponseEntity.ok(rebates);
    }

    @GetMapping("/calculate-rebate")
    @Operation(summary = "Calculate total rebate amount for a direct supplier and item",
               description = "Calculates the total rebate amount based on sales quantity. Uses current date if date not specified.")
    public ResponseEntity<BigDecimal> calculateTotalRebate(
            @Parameter(description = "Direct Supplier ID") @RequestParam UUID vendorId,
            @Parameter(description = "Item ID (SKU)") @RequestParam UUID itemId,
            @Parameter(description = "Sales Quantity") @RequestParam BigDecimal quantity,
            @Parameter(description = "Date (optional, defaults to today)") @RequestParam(required = false) LocalDate date) {
        log.debug("Calculating total rebate for vendorId: {}, itemId: {}, quantity: {}, date: {}",
                  vendorId, itemId, quantity, date);

        CalculateRebateCommand command = CalculateRebateCommand.builder()
                .vendorId(vendorId)
                .itemId(itemId)
                .quantity(quantity)
                .date(date)
                .build();

        Assert.isTrue(command.isValid(), command.getValidationError());

        BigDecimal totalRebate = itemVendorRebateApplicationService.calculateTotalRebate(
                command.getVendorId(), command.getItemId(), command.getQuantity(), command.getEffectiveDate());
        return ResponseEntity.ok(totalRebate);
    }

    @PostMapping("/batch")
    @Operation(summary = "Batch create direct supplier rebates",
               description = "Creates multiple rebates for direct suppliers in a single operation.")
    public ResponseEntity<List<ItemVendorRebateDto>> batchCreateRebates(
            @RequestBody BatchCreateItemVendorRebateCommand command) {
        log.info("Batch creating {} item vendor rebates",
                 command.getRebateItems() != null ? command.getRebateItems().size() : 0);

        Assert.isTrue(command.isValid(), command.getValidationError());

        List<ItemVendorRebateDto> createdRebates = itemVendorRebateApplicationService.batchCreateRebates(command);
        return ResponseEntity.status(HttpStatus.CREATED).body(createdRebates);
    }

    @GetMapping("/expiring")
    @Operation(summary = "Get rebates expiring within specified days",
               description = "Returns rebates that will expire within the specified number of days. Continuous rebates (no end date) are not included.")
    public ResponseEntity<List<ItemVendorRebateDto>> getExpiringRebates(
            @Parameter(description = "Number of days to look ahead") @RequestParam(defaultValue = "30") int days) {
        log.debug("Getting rebates expiring within {} days", days);

        List<ItemVendorRebateDto> rebates = itemVendorRebateApplicationService.getExpiringRebates(days);
        return ResponseEntity.ok(rebates);
    }
}
