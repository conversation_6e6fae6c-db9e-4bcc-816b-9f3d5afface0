package com.mercaso.ims.interfaces.rest;

import com.mercaso.ims.application.dto.ItemVendorRebateDto;
import com.mercaso.ims.application.service.ItemVendorRebateApplicationService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;
import java.util.UUID;

/**
 * REST Controller for ItemVendorRebate operations
 */
@Slf4j
@RestController
@RequestMapping("/api/v1/item-vendor-rebates")
@RequiredArgsConstructor
@Tag(name = "Item Vendor Rebate", description = "Item Vendor Rebate management APIs")
public class ItemVendorRebateController {

    private final ItemVendorRebateApplicationService itemVendorRebateApplicationService;

    @PostMapping
    @Operation(summary = "Create a new item vendor rebate")
    public ResponseEntity<ItemVendorRebateDto> createRebate(@RequestBody ItemVendorRebateDto rebateDto) {
        log.info("Creating new item vendor rebate for vendorId: {}, itemId: {}", 
                 rebateDto.getVendorId(), rebateDto.getItemId());
        
        ItemVendorRebateDto createdRebate = itemVendorRebateApplicationService.createRebate(rebateDto);
        return ResponseEntity.status(HttpStatus.CREATED).body(createdRebate);
    }

    @GetMapping("/{id}")
    @Operation(summary = "Get item vendor rebate by ID")
    public ResponseEntity<ItemVendorRebateDto> getRebateById(
            @Parameter(description = "Rebate ID") @PathVariable UUID id) {
        log.debug("Getting item vendor rebate by id: {}", id);
        
        return itemVendorRebateApplicationService.getRebateById(id)
                .map(rebate -> ResponseEntity.ok(rebate))
                .orElse(ResponseEntity.notFound().build());
    }

    @PutMapping("/{id}")
    @Operation(summary = "Update an existing item vendor rebate")
    public ResponseEntity<ItemVendorRebateDto> updateRebate(
            @Parameter(description = "Rebate ID") @PathVariable UUID id,
            @RequestBody ItemVendorRebateDto rebateDto) {
        log.info("Updating item vendor rebate with id: {}", id);
        
        ItemVendorRebateDto updatedRebate = itemVendorRebateApplicationService.updateRebate(id, rebateDto);
        return ResponseEntity.ok(updatedRebate);
    }

    @DeleteMapping("/{id}")
    @Operation(summary = "Delete an item vendor rebate")
    public ResponseEntity<Void> deleteRebate(
            @Parameter(description = "Rebate ID") @PathVariable UUID id) {
        log.info("Deleting item vendor rebate with id: {}", id);
        
        itemVendorRebateApplicationService.deleteRebate(id);
        return ResponseEntity.noContent().build();
    }

    @GetMapping("/vendor/{vendorId}")
    @Operation(summary = "Get all rebates for a vendor")
    public ResponseEntity<List<ItemVendorRebateDto>> getRebatesByVendor(
            @Parameter(description = "Vendor ID") @PathVariable UUID vendorId) {
        log.debug("Getting rebates for vendorId: {}", vendorId);
        
        List<ItemVendorRebateDto> rebates = itemVendorRebateApplicationService.getRebatesByVendor(vendorId);
        return ResponseEntity.ok(rebates);
    }

    @GetMapping("/item/{itemId}")
    @Operation(summary = "Get all rebates for an item")
    public ResponseEntity<List<ItemVendorRebateDto>> getRebatesByItem(
            @Parameter(description = "Item ID") @PathVariable UUID itemId) {
        log.debug("Getting rebates for itemId: {}", itemId);
        
        List<ItemVendorRebateDto> rebates = itemVendorRebateApplicationService.getRebatesByItem(itemId);
        return ResponseEntity.ok(rebates);
    }

    @GetMapping("/vendor-item/{vendorItemId}")
    @Operation(summary = "Get all rebates for a vendor item")
    public ResponseEntity<List<ItemVendorRebateDto>> getRebatesByVendorItem(
            @Parameter(description = "Vendor Item ID") @PathVariable UUID vendorItemId) {
        log.debug("Getting rebates for vendorItemId: {}", vendorItemId);
        
        List<ItemVendorRebateDto> rebates = itemVendorRebateApplicationService.getRebatesByVendorItem(vendorItemId);
        return ResponseEntity.ok(rebates);
    }

    @GetMapping("/active")
    @Operation(summary = "Get currently active rebates for a vendor and item")
    public ResponseEntity<List<ItemVendorRebateDto>> getCurrentlyActiveRebates(
            @Parameter(description = "Vendor ID") @RequestParam UUID vendorId,
            @Parameter(description = "Item ID") @RequestParam UUID itemId) {
        log.debug("Getting currently active rebates for vendorId: {}, itemId: {}", vendorId, itemId);
        
        List<ItemVendorRebateDto> rebates = itemVendorRebateApplicationService.getCurrentlyActiveRebates(vendorId, itemId);
        return ResponseEntity.ok(rebates);
    }

    @GetMapping("/calculate-rebate")
    @Operation(summary = "Calculate total rebate amount for a vendor and item")
    public ResponseEntity<BigDecimal> calculateTotalRebate(
            @Parameter(description = "Vendor ID") @RequestParam UUID vendorId,
            @Parameter(description = "Item ID") @RequestParam UUID itemId,
            @Parameter(description = "Quantity") @RequestParam BigDecimal quantity,
            @Parameter(description = "Date (optional, defaults to today)") @RequestParam(required = false) LocalDate date) {
        log.debug("Calculating total rebate for vendorId: {}, itemId: {}, quantity: {}, date: {}", 
                  vendorId, itemId, quantity, date);
        
        BigDecimal totalRebate = itemVendorRebateApplicationService.calculateTotalRebate(vendorId, itemId, quantity, date);
        return ResponseEntity.ok(totalRebate);
    }

    @GetMapping("/expiring")
    @Operation(summary = "Get rebates expiring within specified days")
    public ResponseEntity<List<ItemVendorRebateDto>> getExpiringRebates(
            @Parameter(description = "Number of days to look ahead") @RequestParam(defaultValue = "30") int days) {
        log.debug("Getting rebates expiring within {} days", days);
        
        List<ItemVendorRebateDto> rebates = itemVendorRebateApplicationService.getExpiringRebates(days);
        return ResponseEntity.ok(rebates);
    }
}
