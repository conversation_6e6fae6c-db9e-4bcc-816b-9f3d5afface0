-- V1__create_item_vendor_rebate_table.sql
CREATE TABLE item_vendor_rebate (
                                    id UUID PRIMARY KEY,
                                    vendor_item_id UUID NOT NULL,
                                    vendor_id UUID NOT NULL,
                                    item_id UUID NOT NULL,
                                    start_date DATE NOT NULL,
                                    end_date DATE,
                                    rebate_per_unit DECIMAL(10, 2) NOT NULL,
                                    CONSTRAINT uq_item_vendor_rebate UNIQUE (vendor_id, item_id, start_date)
);

CREATE INDEX idx_item_vendor_rebate_vendor_item ON item_vendor_rebate(vendor_id);
CREATE INDEX idx_item_vendor_rebate_vendor ON item_vendor_rebate(vendor_id);
CREATE INDEX idx_item_vendor_rebate_item ON item_vendor_rebate(item_id);
CREATE INDEX idx_item_vendor_rebate_dates ON item_vendor_rebate(start_date, end_date);