package com.mercaso.ims.application.service;

import com.mercaso.ims.domain.itemvendorrebate.ItemVendorRebate;
import com.mercaso.ims.domain.itemvendorrebate.ItemVendorRebateRepository;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.UUID;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.when;

/**
 * Test class for ItemVendorRebateInMemoryService
 */
@ExtendWith(MockitoExtension.class)
class ItemVendorRebateInMemoryServiceTest {

    @Mock
    private ItemVendorRebateRepository itemVendorRebateRepository;

    @InjectMocks
    private ItemVendorRebateInMemoryService inMemoryService;

    private UUID vendorItemId1;
    private UUID vendorItemId2;
    private UUID vendorId;
    private UUID itemId1;
    private UUID itemId2;
    private List<ItemVendorRebate> testRebates;

    @BeforeEach
    void setUp() {
        vendorItemId1 = UUID.randomUUID();
        vendorItemId2 = UUID.randomUUID();
        vendorId = UUID.randomUUID();
        itemId1 = UUID.randomUUID();
        itemId2 = UUID.randomUUID();

        // Create test rebates
        ItemVendorRebate rebate1 = ItemVendorRebate.builder()
                .id(UUID.randomUUID())
                .vendorItemId(vendorItemId1)
                .vendorId(vendorId)
                .itemId(itemId1)
                .startDate(LocalDate.of(2024, 1, 1))
                .endDate(LocalDate.of(2024, 6, 30))
                .rebatePerUnit(new BigDecimal("5.00"))
                .build();

        ItemVendorRebate rebate2 = ItemVendorRebate.builder()
                .id(UUID.randomUUID())
                .vendorItemId(vendorItemId1)
                .vendorId(vendorId)
                .itemId(itemId1)
                .startDate(LocalDate.of(2024, 7, 1))
                .endDate(null) // Continuous rebate
                .rebatePerUnit(new BigDecimal("3.00"))
                .build();

        ItemVendorRebate rebate3 = ItemVendorRebate.builder()
                .id(UUID.randomUUID())
                .vendorItemId(vendorItemId2)
                .vendorId(vendorId)
                .itemId(itemId2)
                .startDate(LocalDate.of(2024, 1, 1))
                .endDate(LocalDate.of(2024, 12, 31))
                .rebatePerUnit(new BigDecimal("2.50"))
                .build();

        testRebates = Arrays.asList(rebate1, rebate2, rebate3);
    }

    @Test
    void testGetRebatesForVendorItem() {
        // Given
        when(itemVendorRebateRepository.findAll()).thenReturn(testRebates);

        // When
        List<ItemVendorRebate> result = inMemoryService.getRebatesForVendorItem(vendorItemId1);

        // Then
        assertEquals(2, result.size());
        assertTrue(result.stream().allMatch(r -> r.getVendorItemId().equals(vendorItemId1)));
    }

    @Test
    void testGetActiveRebatesForVendorItem() {
        // Given
        when(itemVendorRebateRepository.findAll()).thenReturn(testRebates);

        // When - test for date within first rebate period
        List<ItemVendorRebate> result = inMemoryService.getActiveRebatesForVendorItem(vendorItemId1);

        // Then - should find active rebates based on current date
        assertNotNull(result);
        assertTrue(result.stream().allMatch(r -> r.getVendorItemId().equals(vendorItemId1)));
    }

    @Test
    void testGetRebatesForVendorItemsBatch() {
        // Given
        when(itemVendorRebateRepository.findAll()).thenReturn(testRebates);
        List<UUID> vendorItemIds = Arrays.asList(vendorItemId1, vendorItemId2);

        // When
        Map<UUID, List<ItemVendorRebate>> result = inMemoryService.getRebatesForVendorItemsBatch(vendorItemIds);

        // Then
        assertEquals(2, result.size());
        assertTrue(result.containsKey(vendorItemId1));
        assertTrue(result.containsKey(vendorItemId2));
        assertEquals(2, result.get(vendorItemId1).size());
        assertEquals(1, result.get(vendorItemId2).size());
    }

    @Test
    void testCalculateTotalRebateForVendorItem() {
        // Given
        when(itemVendorRebateRepository.findAll()).thenReturn(testRebates);
        BigDecimal quantity = new BigDecimal("10");
        LocalDate testDate = LocalDate.of(2024, 3, 15); // Within first rebate period

        // When
        BigDecimal result = inMemoryService.calculateTotalRebateForVendorItem(vendorItemId1, quantity, testDate);

        // Then
        assertEquals(new BigDecimal("50.00"), result); // 5.00 * 10
    }

    @Test
    void testCalculateTotalRebateForVendorItem_ContinuousRebate() {
        // Given
        when(itemVendorRebateRepository.findAll()).thenReturn(testRebates);
        BigDecimal quantity = new BigDecimal("10");
        LocalDate testDate = LocalDate.of(2024, 8, 15); // Within continuous rebate period

        // When
        BigDecimal result = inMemoryService.calculateTotalRebateForVendorItem(vendorItemId1, quantity, testDate);

        // Then
        assertEquals(new BigDecimal("30.00"), result); // 3.00 * 10
    }

    @Test
    void testGetHighestRebatePerUnitForVendorItem() {
        // Given
        when(itemVendorRebateRepository.findAll()).thenReturn(testRebates);
        LocalDate testDate = LocalDate.of(2024, 3, 15); // Within first rebate period

        // When
        BigDecimal result = inMemoryService.getHighestRebatePerUnitForVendorItem(vendorItemId1, testDate);

        // Then
        assertEquals(new BigDecimal("5.00"), result);
    }

    @Test
    void testHasActiveRebates_True() {
        // Given
        when(itemVendorRebateRepository.findAll()).thenReturn(testRebates);

        // When
        boolean result = inMemoryService.hasActiveRebates(vendorItemId2);

        // Then - vendorItemId2 has a rebate active for most of 2024
        assertTrue(result);
    }

    @Test
    void testHasActiveRebates_False() {
        // Given
        UUID nonExistentVendorItemId = UUID.randomUUID();
        when(itemVendorRebateRepository.findAll()).thenReturn(testRebates);

        // When
        boolean result = inMemoryService.hasActiveRebates(nonExistentVendorItemId);

        // Then
        assertFalse(result);
    }

    @Test
    void testGetExpiringRebatesForVendorItem() {
        // Given
        when(itemVendorRebateRepository.findAll()).thenReturn(testRebates);
        int days = 365; // Look ahead one year

        // When
        List<ItemVendorRebate> result = inMemoryService.getExpiringRebatesForVendorItem(vendorItemId2, days);

        // Then
        assertEquals(1, result.size()); // vendorItemId2 has one rebate expiring in 2024
        assertEquals(LocalDate.of(2024, 12, 31), result.get(0).getEndDate());
    }

    @Test
    void testGetExpiringRebatesForVendorItem_NoContinuousRebates() {
        // Given
        when(itemVendorRebateRepository.findAll()).thenReturn(testRebates);
        int days = 365;

        // When
        List<ItemVendorRebate> result = inMemoryService.getExpiringRebatesForVendorItem(vendorItemId1, days);

        // Then - Should not include continuous rebate (null end date)
        assertEquals(0, result.size()); // The first rebate already expired, continuous rebate has no end date
    }

    @Test
    void testCalculateTotalRebateForVendorItem_ZeroQuantity() {
        // Given
        when(itemVendorRebateRepository.findAll()).thenReturn(testRebates);
        BigDecimal quantity = BigDecimal.ZERO;

        // When
        BigDecimal result = inMemoryService.calculateTotalRebateForVendorItem(vendorItemId1, quantity, LocalDate.now());

        // Then
        assertEquals(BigDecimal.ZERO, result);
    }

    @Test
    void testCalculateTotalRebateForVendorItem_NullQuantity() {
        // Given
        when(itemVendorRebateRepository.findAll()).thenReturn(testRebates);

        // When
        BigDecimal result = inMemoryService.calculateTotalRebateForVendorItem(vendorItemId1, null, LocalDate.now());

        // Then
        assertEquals(BigDecimal.ZERO, result);
    }

    @Test
    void testClearCache() {
        // When
        inMemoryService.clearCache();

        // Then - Should not throw exception
        assertDoesNotThrow(() -> inMemoryService.clearCache());
    }

    @Test
    void testGetCacheStatistics() {
        // When
        Map<String, Object> stats = inMemoryService.getCacheStatistics();

        // Then
        assertNotNull(stats);
        assertTrue(stats.containsKey("cacheSize"));
        assertTrue(stats.containsKey("lastCacheUpdate"));
        assertTrue(stats.containsKey("cacheAge"));
        assertTrue(stats.containsKey("cacheTtl"));
    }
}
