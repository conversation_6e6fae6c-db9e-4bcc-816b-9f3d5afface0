package com.mercaso.ims.infrastructure.repository.itemvendorrebate;

import com.mercaso.ims.infrastructure.repository.itemvendorrebate.jpa.ItemVendorRebateJpaRepository;
import com.mercaso.ims.infrastructure.repository.itemvendorrebate.jpa.dataobject.ItemVendorRebateDo;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.orm.jpa.DataJpaTest;
import org.springframework.boot.test.autoconfigure.orm.jpa.TestEntityManager;
import org.springframework.test.context.ActiveProfiles;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.UUID;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Integration test for ItemVendorRebate overlap detection logic
 * 
 * Tests the JPA query for proper overlap detection:
 * - inputStartDate < dbEndDate AND inputEndDate > dbStartDate
 * - Special handling for continuous rebates (null end dates)
 */
@DataJpaTest
@ActiveProfiles("test")
class ItemVendorRebateOverlapIntegrationTest {

    @Autowired
    private TestEntityManager entityManager;

    @Autowired
    private ItemVendorRebateJpaRepository jpaRepository;

    private UUID vendorId;
    private UUID itemId;
    private UUID vendorItemId;

    @BeforeEach
    void setUp() {
        vendorId = UUID.randomUUID();
        itemId = UUID.randomUUID();
        vendorItemId = UUID.randomUUID();
    }

    @Test
    void testExistsOverlappingRebates_NoOverlap_ShouldReturnFalse() {
        // Given: Existing rebate from 2024-01-01 to 2024-03-31
        createAndSaveRebate(LocalDate.of(2024, 1, 1), LocalDate.of(2024, 3, 31));

        // When: Check for new rebate from 2024-04-01 to 2024-06-30 (no overlap)
        boolean hasOverlap = jpaRepository.existsOverlappingRebates(
                vendorId, itemId,
                LocalDate.of(2024, 4, 1),
                LocalDate.of(2024, 6, 30)
        );

        // Then
        assertFalse(hasOverlap);
    }

    @Test
    void testExistsOverlappingRebates_NewStartsBeforeExistingEnds_ShouldReturnTrue() {
        // Given: Existing rebate from 2024-01-01 to 2024-06-30
        createAndSaveRebate(LocalDate.of(2024, 1, 1), LocalDate.of(2024, 6, 30));

        // When: Check for new rebate from 2024-05-01 to 2024-08-31 (overlaps)
        boolean hasOverlap = jpaRepository.existsOverlappingRebates(
                vendorId, itemId,
                LocalDate.of(2024, 5, 1),
                LocalDate.of(2024, 8, 31)
        );

        // Then
        assertTrue(hasOverlap);
    }

    @Test
    void testExistsOverlappingRebates_NewEndsAfterExistingStarts_ShouldReturnTrue() {
        // Given: Existing rebate from 2024-05-01 to 2024-08-31
        createAndSaveRebate(LocalDate.of(2024, 5, 1), LocalDate.of(2024, 8, 31));

        // When: Check for new rebate from 2024-01-01 to 2024-06-30 (overlaps)
        boolean hasOverlap = jpaRepository.existsOverlappingRebates(
                vendorId, itemId,
                LocalDate.of(2024, 1, 1),
                LocalDate.of(2024, 6, 30)
        );

        // Then
        assertTrue(hasOverlap);
    }

    @Test
    void testExistsOverlappingRebates_NewCompletelyContainsExisting_ShouldReturnTrue() {
        // Given: Existing rebate from 2024-03-01 to 2024-06-30
        createAndSaveRebate(LocalDate.of(2024, 3, 1), LocalDate.of(2024, 6, 30));

        // When: Check for new rebate from 2024-01-01 to 2024-08-31 (contains existing)
        boolean hasOverlap = jpaRepository.existsOverlappingRebates(
                vendorId, itemId,
                LocalDate.of(2024, 1, 1),
                LocalDate.of(2024, 8, 31)
        );

        // Then
        assertTrue(hasOverlap);
    }

    @Test
    void testExistsOverlappingRebates_NewCompletelyContainedByExisting_ShouldReturnTrue() {
        // Given: Existing rebate from 2024-01-01 to 2024-08-31
        createAndSaveRebate(LocalDate.of(2024, 1, 1), LocalDate.of(2024, 8, 31));

        // When: Check for new rebate from 2024-03-01 to 2024-06-30 (contained by existing)
        boolean hasOverlap = jpaRepository.existsOverlappingRebates(
                vendorId, itemId,
                LocalDate.of(2024, 3, 1),
                LocalDate.of(2024, 6, 30)
        );

        // Then
        assertTrue(hasOverlap);
    }

    @Test
    void testExistsOverlappingRebates_TwoContinuousRebates_ShouldReturnTrue() {
        // Given: Existing continuous rebate from 2024-01-01 to null
        createAndSaveRebate(LocalDate.of(2024, 1, 1), null);

        // When: Check for new continuous rebate from 2024-06-01 to null
        boolean hasOverlap = jpaRepository.existsOverlappingRebates(
                vendorId, itemId,
                LocalDate.of(2024, 6, 1),
                null
        );

        // Then
        assertTrue(hasOverlap);
    }

    @Test
    void testExistsOverlappingRebates_ContinuousVsFixedPeriod_ShouldReturnTrue() {
        // Given: Existing continuous rebate from 2024-01-01 to null
        createAndSaveRebate(LocalDate.of(2024, 1, 1), null);

        // When: Check for new fixed period rebate from 2024-06-01 to 2024-12-31
        boolean hasOverlap = jpaRepository.existsOverlappingRebates(
                vendorId, itemId,
                LocalDate.of(2024, 6, 1),
                LocalDate.of(2024, 12, 31)
        );

        // Then
        assertTrue(hasOverlap);
    }

    @Test
    void testExistsOverlappingRebates_FixedPeriodVsContinuous_ShouldReturnTrue() {
        // Given: Existing fixed period rebate from 2024-06-01 to 2024-12-31
        createAndSaveRebate(LocalDate.of(2024, 6, 1), LocalDate.of(2024, 12, 31));

        // When: Check for new continuous rebate from 2024-01-01 to null
        boolean hasOverlap = jpaRepository.existsOverlappingRebates(
                vendorId, itemId,
                LocalDate.of(2024, 1, 1),
                null
        );

        // Then
        assertTrue(hasOverlap);
    }

    @Test
    void testExistsOverlappingRebates_ContinuousStartsAfterFixedEnds_ShouldReturnFalse() {
        // Given: Existing fixed period rebate from 2024-01-01 to 2024-03-31
        createAndSaveRebate(LocalDate.of(2024, 1, 1), LocalDate.of(2024, 3, 31));

        // When: Check for new continuous rebate from 2024-04-01 to null (no overlap)
        boolean hasOverlap = jpaRepository.existsOverlappingRebates(
                vendorId, itemId,
                LocalDate.of(2024, 4, 1),
                null
        );

        // Then
        assertFalse(hasOverlap);
    }

    @Test
    void testExistsOverlappingRebates_DifferentVendorItem_ShouldReturnFalse() {
        // Given: Existing rebate for different vendor/item
        UUID differentVendorId = UUID.randomUUID();
        UUID differentItemId = UUID.randomUUID();
        
        createAndSaveRebate(LocalDate.of(2024, 1, 1), LocalDate.of(2024, 6, 30));

        // When: Check for new rebate with different vendor/item (no overlap)
        boolean hasOverlap = jpaRepository.existsOverlappingRebates(
                differentVendorId, differentItemId,
                LocalDate.of(2024, 3, 1),
                LocalDate.of(2024, 8, 31)
        );

        // Then
        assertFalse(hasOverlap);
    }

    @Test
    void testExistsOverlappingRebates_ExactSamePeriod_ShouldReturnTrue() {
        // Given: Existing rebate from 2024-01-01 to 2024-06-30
        createAndSaveRebate(LocalDate.of(2024, 1, 1), LocalDate.of(2024, 6, 30));

        // When: Check for new rebate with exact same period
        boolean hasOverlap = jpaRepository.existsOverlappingRebates(
                vendorId, itemId,
                LocalDate.of(2024, 1, 1),
                LocalDate.of(2024, 6, 30)
        );

        // Then
        assertTrue(hasOverlap);
    }

    private void createAndSaveRebate(LocalDate startDate, LocalDate endDate) {
        ItemVendorRebateDo rebate = ItemVendorRebateDo.builder()
                .id(UUID.randomUUID())
                .vendorItemId(vendorItemId)
                .vendorId(vendorId)
                .itemId(itemId)
                .startDate(startDate)
                .endDate(endDate)
                .rebatePerUnit(new BigDecimal("5.00"))
                .build();

        entityManager.persistAndFlush(rebate);
    }
}
