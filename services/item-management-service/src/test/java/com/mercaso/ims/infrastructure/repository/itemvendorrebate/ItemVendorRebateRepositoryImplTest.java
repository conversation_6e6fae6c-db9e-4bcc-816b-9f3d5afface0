package com.mercaso.ims.infrastructure.repository.itemvendorrebate;

import com.mercaso.ims.domain.itemvendorrebate.ItemVendorRebate;
import com.mercaso.ims.infrastructure.repository.itemvendorrebate.jpa.ItemVendorRebateJpaRepository;
import com.mercaso.ims.infrastructure.repository.itemvendorrebate.jpa.dataobject.ItemVendorRebateDo;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.orm.jpa.DataJpaTest;
import org.springframework.boot.test.autoconfigure.orm.jpa.TestEntityManager;
import org.springframework.context.annotation.Import;
import org.springframework.test.context.ActiveProfiles;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Integration tests for ItemVendorRebateRepositoryImpl
 */
@DataJpaTest
@ActiveProfiles("test")
@Import(ItemVendorRebateRepositoryImpl.class)
class ItemVendorRebateRepositoryImplTest {

    @Autowired
    private TestEntityManager entityManager;

    @Autowired
    private ItemVendorRebateJpaRepository jpaRepository;

    @Autowired
    private ItemVendorRebateRepositoryImpl repository;

    private UUID vendorId;
    private UUID itemId;
    private UUID vendorItemId;

    @BeforeEach
    void setUp() {
        vendorId = UUID.randomUUID();
        itemId = UUID.randomUUID();
        vendorItemId = UUID.randomUUID();
    }

    @Test
    void testSaveAndFindById() {
        // Given
        ItemVendorRebate rebate = createTestRebate();

        // When
        ItemVendorRebate savedRebate = repository.save(rebate);
        Optional<ItemVendorRebate> foundRebate = repository.findById(savedRebate.getId());

        // Then
        assertTrue(foundRebate.isPresent());
        assertEquals(savedRebate.getId(), foundRebate.get().getId());
        assertEquals(vendorId, foundRebate.get().getVendorId());
        assertEquals(itemId, foundRebate.get().getItemId());
        assertEquals(vendorItemId, foundRebate.get().getVendorItemId());
    }

    @Test
    void testFindByVendorId() {
        // Given
        ItemVendorRebate rebate1 = createTestRebate();
        ItemVendorRebate rebate2 = createTestRebateWithDifferentItem();
        repository.save(rebate1);
        repository.save(rebate2);

        // When
        List<ItemVendorRebate> rebates = repository.findByVendorId(vendorId);

        // Then
        assertEquals(2, rebates.size());
        assertTrue(rebates.stream().allMatch(r -> r.getVendorId().equals(vendorId)));
    }

    @Test
    void testFindByItemId() {
        // Given
        ItemVendorRebate rebate = createTestRebate();
        repository.save(rebate);

        // When
        List<ItemVendorRebate> rebates = repository.findByItemId(itemId);

        // Then
        assertEquals(1, rebates.size());
        assertEquals(itemId, rebates.get(0).getItemId());
    }

    @Test
    void testFindByVendorItemId() {
        // Given
        ItemVendorRebate rebate = createTestRebate();
        repository.save(rebate);

        // When
        List<ItemVendorRebate> rebates = repository.findByVendorItemId(vendorItemId);

        // Then
        assertEquals(1, rebates.size());
        assertEquals(vendorItemId, rebates.get(0).getVendorItemId());
    }

    @Test
    void testFindActiveRebates() {
        // Given
        LocalDate testDate = LocalDate.of(2024, 6, 15);
        
        // Active rebate
        ItemVendorRebate activeRebate = ItemVendorRebate.builder()
                .id(UUID.randomUUID())
                .vendorId(vendorId)
                .itemId(itemId)
                .vendorItemId(vendorItemId)
                .startDate(LocalDate.of(2024, 1, 1))
                .endDate(LocalDate.of(2024, 12, 31))
                .rebatePerUnit(new BigDecimal("5.00"))
                .build();
        
        // Expired rebate
        ItemVendorRebate expiredRebate = ItemVendorRebate.builder()
                .id(UUID.randomUUID())
                .vendorId(vendorId)
                .itemId(itemId)
                .vendorItemId(vendorItemId)
                .startDate(LocalDate.of(2024, 1, 1))
                .endDate(LocalDate.of(2024, 5, 31))
                .rebatePerUnit(new BigDecimal("3.00"))
                .build();
        
        repository.save(activeRebate);
        repository.save(expiredRebate);

        // When
        List<ItemVendorRebate> activeRebates = repository.findActiveRebates(vendorId, itemId, testDate);

        // Then
        assertEquals(1, activeRebates.size());
        assertEquals(activeRebate.getId(), activeRebates.get(0).getId());
    }

    @Test
    void testExistsByVendorIdAndItemIdAndStartDate() {
        // Given
        ItemVendorRebate rebate = createTestRebate();
        repository.save(rebate);

        // When
        boolean exists = repository.existsByVendorIdAndItemIdAndStartDate(
                vendorId, itemId, rebate.getStartDate());
        boolean notExists = repository.existsByVendorIdAndItemIdAndStartDate(
                vendorId, itemId, LocalDate.of(2025, 1, 1));

        // Then
        assertTrue(exists);
        assertFalse(notExists);
    }

    @Test
    void testDeleteById() {
        // Given
        ItemVendorRebate rebate = createTestRebate();
        ItemVendorRebate savedRebate = repository.save(rebate);

        // When
        repository.deleteById(savedRebate.getId());

        // Then
        Optional<ItemVendorRebate> foundRebate = repository.findById(savedRebate.getId());
        assertFalse(foundRebate.isPresent());
    }

    private ItemVendorRebate createTestRebate() {
        return ItemVendorRebate.builder()
                .id(UUID.randomUUID())
                .vendorId(vendorId)
                .itemId(itemId)
                .vendorItemId(vendorItemId)
                .startDate(LocalDate.of(2024, 1, 1))
                .endDate(LocalDate.of(2024, 12, 31))
                .rebatePerUnit(new BigDecimal("5.00"))
                .build();
    }

    private ItemVendorRebate createTestRebateWithDifferentItem() {
        return ItemVendorRebate.builder()
                .id(UUID.randomUUID())
                .vendorId(vendorId)
                .itemId(UUID.randomUUID()) // Different item ID
                .vendorItemId(UUID.randomUUID()) // Different vendor item ID
                .startDate(LocalDate.of(2024, 2, 1))
                .endDate(LocalDate.of(2024, 11, 30))
                .rebatePerUnit(new BigDecimal("3.50"))
                .build();
    }
}
