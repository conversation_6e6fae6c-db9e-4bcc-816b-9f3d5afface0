package com.mercaso.wms.application.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class PickedShippingOrderItemExportDto {
    private String item;

    private String orderNumber;

    private Integer line;

    private String itemDescription;

    private Integer requestedQty;

    private Integer pickedQty;

    private Integer fulfilledQty;

    private Integer filled;

    private String breakdown;

    private String initialFrom;

    private String finalFrom;
} 