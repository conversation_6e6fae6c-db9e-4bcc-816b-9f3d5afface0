package com.mercaso.wms.application.dto.document;

import com.mercaso.wms.application.dto.BaseDto;
import com.mercaso.wms.domain.document.enums.DocumentType;
import java.time.Instant;
import java.util.UUID;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class DocumentDto extends BaseDto {

    private UUID id;

    private UUID entityId;

    private String entityName;

    private DocumentType documentType;

    private String createdUserName;

    private String fileName;

    private String fileUrl;

    private Instant createdAt;

    private String createdBy;
} 