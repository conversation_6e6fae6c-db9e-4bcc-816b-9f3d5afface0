package com.mercaso.wms.application.dto.event;

import com.mercaso.wms.application.dto.BaseDto;
import com.mercaso.wms.infrastructure.external.shopify.dto.ShopifyLineItemUpdateDto;
import java.util.List;
import java.util.UUID;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class ShippingOrderSyncedToShopifyPayloadDto extends BusinessEventPayloadDto<BaseDto> {

    private UUID shippingOrderId;
    private Boolean syncedToShopify;
    private List<ShopifyLineItemUpdateDto> lineItems;

    @Builder
    public ShippingOrderSyncedToShopifyPayloadDto(BaseDto data,
        UUID shippingOrderId,
        Boolean syncedToShopify,
        List<ShopifyLineItemUpdateDto> lineItems) {
        super(data);
        this.shippingOrderId = shippingOrderId;
        this.syncedToShopify = syncedToShopify;
        this.lineItems = lineItems;
    }

} 