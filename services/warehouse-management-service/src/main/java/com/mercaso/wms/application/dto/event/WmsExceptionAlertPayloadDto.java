package com.mercaso.wms.application.dto.event;

import com.mercaso.wms.infrastructure.alert.dto.WmsExceptionAlertDto;
import java.util.UUID;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * WMS Exception Alert Event Payload
 * Used to publish WMS exception alerts as business events
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = false)
public class WmsExceptionAlertPayloadDto extends BusinessEventPayloadDto<WmsExceptionAlertDto> {

    private UUID alertId;

    /**
     * Creates a payload with alert data
     */
    public static WmsExceptionAlertPayloadDto create(WmsExceptionAlertDto alertDto) {
        WmsExceptionAlertPayloadDto payloadDto = WmsExceptionAlertPayloadDto.builder()
            .alertId(UUID.randomUUID())
            .build();
        payloadDto.setData(alertDto);
        return payloadDto;
    }
}
