package com.mercaso.wms.application.mapper.document;

import com.mercaso.wms.application.dto.document.DocumentDto;
import com.mercaso.wms.application.mapper.BaseDtoApplicationMapper;
import com.mercaso.wms.domain.document.Document;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;

@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface DocumentDtoApplicationMapper extends BaseDtoApplicationMapper<Document, DocumentDto> {

    DocumentDtoApplicationMapper INSTANCE = Mappers.getMapper(DocumentDtoApplicationMapper.class);
} 