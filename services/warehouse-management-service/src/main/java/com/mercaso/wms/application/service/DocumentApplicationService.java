package com.mercaso.wms.application.service;

import com.mercaso.document.operations.models.DocumentResponse;
import com.mercaso.document.operations.models.UploadDocumentRequest;
import com.mercaso.wms.application.command.document.UploadDocumentCommand;
import com.mercaso.wms.application.dto.document.DocumentDto;
import com.mercaso.wms.application.mapper.document.DocumentDtoApplicationMapper;
import com.mercaso.wms.domain.document.Document;
import com.mercaso.wms.domain.document.DocumentRepository;
import com.mercaso.wms.infrastructure.external.document.DocumentAdaptor;
import com.mercaso.wms.infrastructure.utils.DateUtils;
import jakarta.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.OutputStream;
import java.nio.file.Files;
import java.nio.file.Path;
import java.util.List;
import java.util.Map;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.Lists;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

@Slf4j
@Service
@RequiredArgsConstructor
public class DocumentApplicationService {

    private final DocumentRepository documentRepository;

    private final DocumentAdaptor documentAdaptor;

    private final DocumentDtoApplicationMapper documentDtoApplicationMapper;

    public List<DocumentDto> uploadDocuments(List<UploadDocumentCommand> commands, Map<String, MultipartFile> files) {
        List<DocumentDto> documentDtos = Lists.newArrayList();
        commands.forEach(command -> command.getClientFileIds().forEach(clientFileId -> {
            MultipartFile multipartFile = files.get(clientFileId);
            if (multipartFile != null) {
                upload(command, multipartFile, documentDtos);
            }
        }));
        return documentDtos;
    }

    private void upload(UploadDocumentCommand command, MultipartFile file, List<DocumentDto> results) {
        try {
            UploadDocumentRequest document = UploadDocumentRequest.builder()
                .content(file.getBytes())
                .documentName(generateFileName(command, file.getOriginalFilename()))
                .build();
            DocumentDto documentDto = uploadDocument(command, document);
            results.add(documentDto);
        } catch (Exception e) {
            log.error("[uploadDocuments] Error uploading file: {}", file.getOriginalFilename(), e);
        }
    }

    private DocumentDto uploadDocument(UploadDocumentCommand command, UploadDocumentRequest document) {
        DocumentResponse documentResponse = documentAdaptor.uploadToS3(document);

        Document newDocument = Document.builder()
            .build()
            .createDocument(command, documentResponse.getName());
        DocumentDto documentDto = documentDtoApplicationMapper.domainToDto(documentRepository.save(newDocument));
        documentDto.setFileUrl(documentResponse.getSignedUrl());
        return documentDto;
    }

    private String generateFileName(UploadDocumentCommand command, String fileName) {
        return command.getDocumentType().name().concat("-").concat(DateUtils.laDateTime()).concat("-")
            .concat(fileName);
    }

    public void download(String documentName, HttpServletResponse response) throws IOException {
        try {
            byte[] bytes = documentAdaptor.downloadDocument(documentName);
            if (bytes == null || bytes.length == 0) {
                response.setStatus(HttpServletResponse.SC_NOT_FOUND);
                return;
            }
            String mimeType = Files.probeContentType(Path.of(documentName));
            if (mimeType == null) {
                mimeType = "application/octet-stream";
            }
            response.setContentType(mimeType);
            response.setHeader("Access-Control-Expose-Headers", "Content-Disposition");
            response.setHeader("Content-Disposition", "attachment; filename=" + documentName);

            try (OutputStream os = response.getOutputStream()) {
                os.write(bytes);
                os.flush();
            }
        } catch (IOException e) {
            response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
            log.warn("Error downloading document: {}", documentName, e);
        }
    }

} 