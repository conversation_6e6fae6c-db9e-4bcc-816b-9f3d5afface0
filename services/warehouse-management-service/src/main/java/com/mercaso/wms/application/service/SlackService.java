package com.mercaso.wms.application.service;

import com.mercaso.wms.infrastructure.config.SlackConfig;
import com.mercaso.wms.infrastructure.external.slack.WmsSlackAdaptor;
import com.mercaso.wms.infrastructure.external.slack.dto.SlackMessageDto;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * High-level Slack service providing WMS exception alert capabilities
 * Uses SlackAdaptor for low-level message sending and SlackConfig for configuration
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class SlackService {

    private final WmsSlackAdaptor wmsSlackAdaptor;
    private final SlackConfig slackConfig;

    /**
     * Sends a WMS exception alert message
     *
     * @param message the alert message
     * @return true if sent successfully
     */
    public boolean sendWmsExceptionAlert(String message) {

        SlackMessageDto messageDto = SlackMessageDto.builder()
            .text(message)
            .username("WMS Alert Exception Bot")
            .iconEmoji(":exclamation:")
            .build();

        return wmsSlackAdaptor.sendMessage(slackConfig.getWmsExceptionWebhook(), messageDto);
    }

    /**
     * Sends a message to a custom webhook with default formatting
     *
     * @param webhookUrl the webhook URL
     * @param message the message text
     * @param botName the bot name
     * @param iconEmoji the bot icon
     * @return true if sent successfully
     */
    public boolean sendCustomMessage(String webhookUrl, String message, String botName, String iconEmoji) {
        SlackMessageDto messageDto = SlackMessageDto.builder()
            .text(message)
            .username(botName)
            .iconEmoji(iconEmoji)
            .build();

        return wmsSlackAdaptor.sendMessage(webhookUrl, messageDto);
    }

    /**
     * Sends a simple message to a custom webhook
     *
     * @param webhookUrl the webhook URL
     * @param message the message text
     * @return true if sent successfully
     */
    public boolean sendSimpleMessage(String webhookUrl, String message) {
        return wmsSlackAdaptor.sendMessage(webhookUrl, message);
    }

    /**
     * Sends a critical alert with retry mechanism
     *
     * @param webhookUrl the webhook URL
     * @param message the message text
     * @param retryCount number of retry attempts
     * @return true if sent successfully
     */
    public boolean sendCriticalAlert(String webhookUrl, String message, int retryCount) {
        SlackMessageDto messageDto = SlackMessageDto.builder()
            .text(message)
            .username("Critical Alert Bot")
            .iconEmoji(":fire:")
            .build();

        return wmsSlackAdaptor.sendMessageWithRetry(webhookUrl, messageDto, retryCount);
    }
} 