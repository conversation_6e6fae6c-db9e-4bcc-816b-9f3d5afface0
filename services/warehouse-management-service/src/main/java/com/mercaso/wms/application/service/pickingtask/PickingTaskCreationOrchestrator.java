package com.mercaso.wms.application.service.pickingtask;

import com.mercaso.wms.application.service.pickingtask.strategy.PickingTaskCreationContext;
import com.mercaso.wms.batch.config.PickingTaskAssignmentConfig;
import com.mercaso.wms.batch.enums.SourceEnum;
import com.mercaso.wms.domain.pickingtask.PickingTask;
import com.mercaso.wms.domain.pickingtask.PickingTaskRepository;
import com.mercaso.wms.domain.pickingtask.enums.PickingTaskType;
import com.mercaso.wms.infrastructure.cache.LocationCache;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Slf4j
@Service
@RequiredArgsConstructor
public class PickingTaskCreationOrchestrator {

    private final PickingTaskCreationStrategyFactory strategyFactory;

    private final LocationCache locationCache;

    private final PickingTaskAssignmentConfig assignmentConfig;

    private final AutoAssignBatchLevelPickingTaskService autoAssignService;

    private final AutoAssignOrderLevelPickingTaskService orderAssignService;

    private final PickingTaskRepository pickingTaskRepository;

    public void createAllPickingTasks(UUID batchId) {
        log.info("Starting picking task creation for batch: {}", batchId);
        PickingTaskCreationContext context = buildContext(batchId);

        List<PickingTask> mdcTasks = strategyFactory.getAllStrategies()
            .stream()
            .filter(strategy -> strategy.getSource() == SourceEnum.MDC)
            .flatMap(strategy -> {
                List<PickingTask> savedTasks = strategy.execute(context);
                log.info("MDC strategy created {} tasks for batch: {}", savedTasks.size(), batchId);
                return savedTasks.stream();
            })
            .toList();
        List<PickingTask> allSavedTasks = new ArrayList<>(mdcTasks);

        List<PickingTask> otherTasks = strategyFactory.getAllStrategies()
            .stream()
            .filter(strategy -> strategy.getSource() != SourceEnum.MDC)
            .flatMap(strategy -> {
                List<PickingTask> savedTasks = strategy.execute(context);
                log.info("Strategy {} created {} tasks for batch: {}",
                    strategy.getSource(), savedTasks.size(), batchId);
                return savedTasks.stream();
            })
            .toList();
        allSavedTasks.addAll(otherTasks);

        assignTasks(allSavedTasks, batchId);

        log.info("Completed picking task creation for batch: {}, total tasks: {}",
            batchId, allSavedTasks.size());
    }

    private PickingTaskCreationContext buildContext(UUID batchId) {
        return PickingTaskCreationContext.builder()
            .batchId(batchId)
            .locationMap(locationCache.getLocationMap())
            .pickingTaskRepository(pickingTaskRepository)
            .assignmentConfig(assignmentConfig)
            .build();
    }

    private void assignTasks(List<PickingTask> allTasks, UUID batchId) {
        if (allTasks.isEmpty()) {
            return;
        }

        List<PickingTask> orderTasks = allTasks.stream()
            .filter(task -> PickingTaskType.ORDER.equals(task.getType()))
            .toList();

        List<PickingTask> batchTasks = allTasks.stream()
            .filter(task -> PickingTaskType.BATCH.equals(task.getType()))
            .toList();

        // Assign order-level tasks
        if (!orderTasks.isEmpty()) {
            orderAssignService.assignPickingTask(orderTasks,
                assignmentConfig.getPickingTaskAssignmentConfig());
            log.info("Assigned {} order-level tasks for batch: {}", orderTasks.size(), batchId);
        }

        // Assign batch-level tasks
        if (!batchTasks.isEmpty()) {
            autoAssignService.assignPickingTask(batchId);
            log.info("Assigned {} batch-level tasks for batch: {}", batchTasks.size(), batchId);
        }
    }
} 