package com.mercaso.wms.application.service.pickingtask;

import com.mercaso.wms.application.service.pickingtask.strategy.PickingTaskCreationStrategy;
import com.mercaso.wms.batch.enums.SourceEnum;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;
import org.springframework.stereotype.Component;

@Component
public class PickingTaskCreationStrategyFactory {

    private final Map<SourceEnum, PickingTaskCreationStrategy> strategies;

    public PickingTaskCreationStrategyFactory(List<PickingTaskCreationStrategy> strategyList) {
        this.strategies = strategyList.stream()
            .collect(Collectors.toMap(
                PickingTaskCreationStrategy::getSource,
                Function.identity()
            ));
    }

    public List<PickingTaskCreationStrategy> getAllStrategies() {
        return new ArrayList<>(strategies.values());
    }
} 