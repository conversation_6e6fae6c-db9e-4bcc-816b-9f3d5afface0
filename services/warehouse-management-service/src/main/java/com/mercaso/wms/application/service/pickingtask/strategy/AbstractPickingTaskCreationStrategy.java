package com.mercaso.wms.application.service.pickingtask.strategy;

import com.mercaso.wms.application.queryservice.BatchItemQueryService;
import com.mercaso.wms.application.service.pickingtask.utils.BatchItemSortUtils;
import com.mercaso.wms.batch.constants.BatchConstants;
import com.mercaso.wms.batch.enums.SourceEnum;
import com.mercaso.wms.domain.batchitem.BatchItem;
import com.mercaso.wms.domain.location.Location;
import com.mercaso.wms.domain.pickingtask.PickingTask;
import com.mercaso.wms.domain.pickingtask.PickingTaskRepository;
import com.mercaso.wms.domain.pickingtask.enums.CategoryType;
import com.mercaso.wms.domain.pickingtask.enums.PickingTaskType;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.UUID;
import java.util.stream.Collectors;


public abstract class AbstractPickingTaskCreationStrategy implements PickingTaskCreationStrategy {

    protected final BatchItemQueryService batchItemQueryService;

    protected AbstractPickingTaskCreationStrategy(BatchItemQueryService batchItemQueryService) {
        this.batchItemQueryService = batchItemQueryService;
    }

    protected PickingTask createPickingTask(UUID batchId, SourceEnum source,
        List<BatchItem> items, Map<UUID, Location> locationMap) {
        items.sort(getItemComparator());
        PickingTask task = PickingTask.builder().build()
            .createTask(batchId, source, items, locationMap);
        task.setType(getTaskType(items));
        return task;
    }


    protected List<PickingTask> saveTasks(List<PickingTask> tasks, PickingTaskRepository repository) {
        if (tasks.isEmpty()) {
            return List.of();
        }
        return repository.saveAll(tasks);
    }

    protected static List<List<BatchItem>> splitBatchItems(List<BatchItem> batchItems, int targetSum) {
        List<List<BatchItem>> result = new ArrayList<>();
        List<BatchItem> currentBatch = new ArrayList<>();
        int currentSum = 0;
        String previousSkuNumber = null;

        for (BatchItem item : batchItems) {
            int qty = item.getExpectQty();
            if (Objects.equals(item.getSkuNumber(), previousSkuNumber)) {
                currentBatch.add(item);
                currentSum += qty;
                continue;
            }

            if (currentSum + qty > targetSum) {
                result.add(new ArrayList<>(currentBatch));
                currentBatch.clear();
                currentSum = 0;
            }
            currentBatch.add(item);
            currentSum += qty;
            previousSkuNumber = item.getSkuNumber();
        }
        if (!currentBatch.isEmpty()) {
            result.add(currentBatch);
        }
        return result;
    }

    protected void processItemsByDepartmentGrouping(List<BatchItem> items, List<PickingTask> tasks,
        PickingTaskCreationContext context) {
        items.stream()
            .collect(Collectors.groupingBy(item ->
                Optional.ofNullable(item.getDepartment()).orElse(CategoryType.OTHER.getKey())))
            .forEach((department, batchItems) -> {
                if (Objects.equals(BatchConstants.CANDY_AND_SNACKS, department)) {
                    batchItems.stream()
                        .collect(Collectors.groupingBy(item ->
                            item.getCategory() == null ? "NO_CATEGORY" : item.getCategory()))
                        .forEach((category, categoryItems) -> {
                            BatchItemSortUtils.sortBatchItems(categoryItems, getSource());
                            createTaskByTargetQty(categoryItems, CategoryType.CANDY_AND_SNACKS.getValue(), tasks, context);
                        });
                } else {
                    BatchItemSortUtils.sortBatchItems(batchItems, getSource());
                    createTaskByTargetQty(batchItems, CategoryType.fromKey(department), tasks, context);
                }
            });
    }

    protected void createTaskByTargetQty(List<BatchItem> batchItemList, int targetSum,
        List<PickingTask> tasks, PickingTaskCreationContext context) {
        splitBatchItems(batchItemList, targetSum).forEach(batchItems -> {
            if (batchItems.isEmpty()) {
                return;
            }
            PickingTask task = createPickingTask(context.getBatchId(), getSource(),
                batchItems, context.getLocationMap());
            task.setType(PickingTaskType.BATCH);
            tasks.add(task);
        });
    }

    protected abstract Comparator<BatchItem> getItemComparator();

    protected abstract PickingTaskType getTaskType(List<BatchItem> items);
} 