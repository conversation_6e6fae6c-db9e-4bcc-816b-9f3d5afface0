package com.mercaso.wms.application.service.pickingtask.strategy;

import com.mercaso.wms.application.queryservice.BatchItemQueryService;
import com.mercaso.wms.batch.enums.SourceEnum;
import com.mercaso.wms.domain.batchitem.BatchItem;
import com.mercaso.wms.domain.pickingtask.PickingTask;
import com.mercaso.wms.domain.pickingtask.enums.PickingTaskType;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class CostcoPickingTaskCreationStrategy extends AbstractPickingTaskCreationStrategy {

    public CostcoPickingTaskCreationStrategy(BatchItemQueryService batchItemQueryService) {
        super(batchItemQueryService);
    }

    @Override
    public SourceEnum getSource() {
        return SourceEnum.COSTCO;
    }

    @Override
    public List<BatchItem> extractData(PickingTaskCreationContext context) {
        return batchItemQueryService.findUnprocessedBy(
            context.getBatchId(), SourceEnum.COSTCO.name());
    }

    @Override
    public List<PickingTask> createAndSave(List<BatchItem> items, PickingTaskCreationContext context) {
        if (items.isEmpty()) {
            return List.of();
        }

        List<PickingTask> tasks = new ArrayList<>();

        processItemsByDepartmentGrouping(items, tasks, context);

        log.info("Created {} Costco picking tasks for batch: {}", tasks.size(), context.getBatchId());
        return saveTasks(tasks, context.getPickingTaskRepository());
    }

    @Override
    protected Comparator<BatchItem> getItemComparator() {
        return Comparator.comparing(BatchItem::getLocationName);
    }

    @Override
    protected PickingTaskType getTaskType(List<BatchItem> items) {
        return PickingTaskType.BATCH;
    }
} 