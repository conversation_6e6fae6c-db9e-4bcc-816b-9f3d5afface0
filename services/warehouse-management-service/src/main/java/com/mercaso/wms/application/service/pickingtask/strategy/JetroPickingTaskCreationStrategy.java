package com.mercaso.wms.application.service.pickingtask.strategy;

import com.mercaso.wms.application.queryservice.BatchItemQueryService;
import com.mercaso.wms.batch.enums.SourceEnum;
import com.mercaso.wms.domain.batchitem.BatchItem;
import com.mercaso.wms.domain.pickingtask.PickingTask;
import com.mercaso.wms.domain.pickingtask.enums.PickingTaskType;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class JetroPickingTaskCreationStrategy extends AbstractPickingTaskCreationStrategy {

    public JetroPickingTaskCreationStrategy(BatchItemQueryService batchItemQueryService) {
        super(batchItemQueryService);
    }

    @Override
    public SourceEnum getSource() {
        return SourceEnum.JETRO;
    }

    @Override
    public List<BatchItem> extractData(PickingTaskCreationContext context) {
        return batchItemQueryService.findUnprocessedBy(
            context.getBatchId(), SourceEnum.JETRO.name());
    }

    @Override
    public List<PickingTask> createAndSave(List<BatchItem> items, PickingTaskCreationContext context) {
        if (items.isEmpty()) {
            return List.of();
        }

        List<PickingTask> tasks = new ArrayList<>();

        items.forEach(item -> {
            item.setAisle(extractAisle(item.getLocationName()));
            item.setBin(extractBin(item.getLocationName()));
        });

        items.sort(Comparator.comparing(BatchItem::getAisle, Comparator.nullsLast(Comparator.naturalOrder()))
            .thenComparing(BatchItem::getBin, Comparator.nullsLast(Comparator.naturalOrder()))
            .thenComparing(BatchItem::getDepartment, Comparator.nullsLast(Comparator.naturalOrder()))
            .thenComparing(BatchItem::getSkuNumber, Comparator.nullsLast(Comparator.naturalOrder())));

        Map<String, List<BatchItem>> itemsByAisle = items.stream()
            .collect(Collectors.groupingBy(item -> Optional.ofNullable(item.getAisle()).orElse("N/A")));

        itemsByAisle.forEach((aisle, aisleItems) -> {
            PickingTask task = createPickingTask(context.getBatchId(), SourceEnum.JETRO,
                aisleItems, context.getLocationMap());
            task.setType(PickingTaskType.BATCH);
            tasks.add(task);
        });

        log.info("Created {} Jetro picking tasks for batch: {}", tasks.size(), context.getBatchId());
        return saveTasks(tasks, context.getPickingTaskRepository());
    }

    private String extractAisle(String locationCode) {
        if (StringUtils.isEmpty(locationCode) || !locationCode.matches("\\d+")) {
            return locationCode;
        }
        return locationCode.length() == 3
            ? locationCode.substring(0, 1)
            : locationCode.substring(0, Math.min(2, locationCode.length()));
    }

    private String extractBin(String locationCode) {
        if (StringUtils.isEmpty(locationCode) || !locationCode.matches("\\d+")) {
            return locationCode;
        }
        return locationCode.length() <= 2 ? locationCode : locationCode.substring(locationCode.length() == 3 ? 1 : 2);
    }


    @Override
    protected Comparator<BatchItem> getItemComparator() {
        return Comparator.comparing(BatchItem::getLocationName);
    }

    @Override
    protected PickingTaskType getTaskType(List<BatchItem> items) {
        return PickingTaskType.BATCH;
    }
} 