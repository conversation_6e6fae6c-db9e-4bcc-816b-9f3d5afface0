package com.mercaso.wms.application.service.pickingtask.strategy;

import com.mercaso.featureflags.service.FeatureFlagsManager;
import com.mercaso.wms.application.queryservice.BatchItemQueryService;
import com.mercaso.wms.batch.enums.SourceEnum;
import com.mercaso.wms.domain.batchitem.BatchItem;
import com.mercaso.wms.domain.location.Location;
import com.mercaso.wms.domain.pickingtask.PickingTask;
import com.mercaso.wms.domain.pickingtask.enums.CategoryType;
import com.mercaso.wms.domain.pickingtask.enums.PickingTaskType;
import com.mercaso.wms.infrastructure.contant.FeatureFlagKeys;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.UUID;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class MfcPickingTaskCreationStrategy extends AbstractPickingTaskCreationStrategy {

    private final FeatureFlagsManager featureFlagsManager;

    public MfcPickingTaskCreationStrategy(BatchItemQueryService batchItemQueryService,
        FeatureFlagsManager featureFlagsManager) {
        super(batchItemQueryService);
        this.featureFlagsManager = featureFlagsManager;
    }

    @Override
    public SourceEnum getSource() {
        return SourceEnum.MFC;
    }

    @Override
    public List<BatchItem> extractData(PickingTaskCreationContext context) {
        return batchItemQueryService.findUnprocessedBy(
            context.getBatchId(), SourceEnum.MFC.name());
    }

    @Override
    public List<PickingTask> createAndSave(List<BatchItem> items, PickingTaskCreationContext context) {
        if (items.isEmpty()) {
            return List.of();
        }
        List<PickingTask> tasks = new ArrayList<>();

        if (featureFlagsManager.isFeatureOn(FeatureFlagKeys.USE_NEW_MFC_BATCH_LEVEL_TASK_LOGIC)) {
            tasks.addAll(createMfcBatchTasksByBreakdownAisle(items, context));
        } else {
            tasks.addAll(createMfcBatchTasks(items, context));
        }

        return saveTasks(tasks, context.getPickingTaskRepository());
    }

    private List<PickingTask> createMfcBatchTasksByBreakdownAisle(List<BatchItem> items,
        PickingTaskCreationContext context) {
        log.info("[createMfcBatchTasksByBreakdownAisle] Found mfc {} batch items.", items.size());

        Map<String, List<BatchItem>> groupedByAisle = items.stream()
            .collect(Collectors.groupingBy(item ->
                StringUtils.isEmpty(item.getBreakdownName()) ? CategoryType.OTHER.getKey()
                    : item.getBreakdownName().split("-")[0]));

        List<PickingTask> tasks = new ArrayList<>();

        groupedByAisle.forEach((breakdownAisle, batchItemsByAisle) ->
            batchItemsByAisle.stream()
                .collect(Collectors.groupingBy(item ->
                    Optional.ofNullable(item.getDepartment()).orElse(CategoryType.OTHER.getKey())))
                .forEach((department, batchItems) ->
                    createTaskByLocationType(batchItems,
                        CategoryType.from(department),
                        tasks,
                        context.getBatchId(),
                        context.getLocationMap())
                )
        );
        return tasks;
    }

    private List<PickingTask> createMfcBatchTasks(List<BatchItem> items,
        PickingTaskCreationContext context) {
        log.info("[createMfcBatchTasks] Found mfc {} batch items.", items.size());

        List<PickingTask> tasks = new ArrayList<>();

        items.stream()
            .collect(Collectors.groupingBy(item ->
                Optional.ofNullable(item.getDepartment()).orElse(CategoryType.OTHER.getKey())))
            .forEach((department, batchItems) ->
                createTaskByLocationType(batchItems,
                    CategoryType.from(department),
                    tasks,
                    context.getBatchId(),
                    context.getLocationMap())
            );
        return tasks;
    }

    @Override
    protected Comparator<BatchItem> getItemComparator() {
        return Comparator.comparing(BatchItem::getLocationName,
                Comparator.nullsLast(Comparator.naturalOrder()))
            .thenComparing(BatchItem::getTitle, Comparator.nullsLast(Comparator.naturalOrder()));
    }

    @Override
    protected PickingTaskType getTaskType(List<BatchItem> items) {
        return PickingTaskType.BATCH;
    }

    private void createTaskByLocationType(List<BatchItem> batchItems,
        CategoryType categoryType,
        List<PickingTask> pickingTasks,
        UUID batchId,
        Map<UUID, Location> locationMap) {
        batchItems.sort(Comparator.comparing(BatchItem::getLocationName,
                Comparator.nullsLast(Comparator.naturalOrder()))
            .thenComparing(BatchItem::getTitle, Comparator.nullsLast(Comparator.naturalOrder())));

        Map<String, List<BatchItem>> groupedByLocations = batchItems.stream()
            .collect(Collectors.groupingBy(batchItem -> {
                if (batchItem.getLocationName() == null) {
                    return "OTHER_GROUP";
                }
                if (batchItem.getLocationName().startsWith(".RD")) {
                    return "RD_GROUP";
                } else if (batchItem.getLocationName().startsWith(".")) {
                    return batchItem.getLocationName().replace(".", "") + "_GROUP";
                } else if (batchItem.getLocationName().contains("-")) {
                    return "AISLE_" + batchItem.getLocationName().substring(0, 3);
                } else {
                    return "OTHER_GROUP";
                }
            }));

        groupedByLocations.forEach((location, items) -> {
            splitBatchItems(items, categoryType.getValue()).forEach(splitItems -> {
                if (splitItems.isEmpty()) {
                    return;
                }
                PickingTask task = createPickingTask(batchId, getSource(), splitItems, locationMap);
                task.setType(PickingTaskType.BATCH);
                pickingTasks.add(task);
            });
        });
    }
} 