package com.mercaso.wms.application.service.pickingtask.strategy;

import com.mercaso.wms.batch.config.PickingTaskAssignmentConfig;
import com.mercaso.wms.domain.location.Location;
import com.mercaso.wms.domain.pickingtask.PickingTaskRepository;
import java.util.Map;
import java.util.UUID;
import lombok.Builder;
import lombok.Data;

@Data
@Builder
public class PickingTaskCreationContext {
    private final UUID batchId;

    private final Map<UUID, Location> locationMap;

    private final PickingTaskRepository pickingTaskRepository;

    private final PickingTaskAssignmentConfig assignmentConfig;
} 