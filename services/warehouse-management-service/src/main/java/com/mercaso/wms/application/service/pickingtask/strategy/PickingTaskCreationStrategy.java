package com.mercaso.wms.application.service.pickingtask.strategy;

import com.mercaso.wms.batch.enums.SourceEnum;
import com.mercaso.wms.domain.batchitem.BatchItem;
import com.mercaso.wms.domain.pickingtask.PickingTask;
import java.util.List;

public interface PickingTaskCreationStrategy {

    SourceEnum getSource();

    List<BatchItem> extractData(PickingTaskCreationContext context);

    List<PickingTask> createAndSave(List<BatchItem> items, PickingTaskCreationContext context);

    default List<PickingTask> execute(PickingTaskCreationContext context) {
        List<BatchItem> items = extractData(context);
        if (items.isEmpty()) {
            return List.of();
        }
        return createAndSave(items, context);
    }
} 