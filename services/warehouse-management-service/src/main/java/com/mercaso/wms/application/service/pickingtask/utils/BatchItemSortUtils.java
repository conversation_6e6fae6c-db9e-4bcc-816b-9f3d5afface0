package com.mercaso.wms.application.service.pickingtask.utils;

import com.mercaso.wms.batch.enums.SourceEnum;
import com.mercaso.wms.domain.batchitem.BatchItem;
import java.util.Comparator;
import java.util.List;

public final class BatchItemSortUtils {

    private BatchItemSortUtils() {

    }

    public static void sortBatchItems(List<BatchItem> items, SourceEnum source) {
        if (items == null || items.isEmpty()) {
            return;
        }

        switch (source) {
            case DOWNEY -> items.sort(getDowneySortComparator());
            case COSTCO, JETRO -> items.sort(getStandardSortComparator());
            default -> items.sort(getLocationOnlyComparator());
        }
    }

    private static Comparator<BatchItem> getDowneySortComparator() {
        return getLocationComparator()
            .thenComparing(getPrepComparator())
            .thenComparing(getCommonComparator());
    }

    private static Comparator<BatchItem> getStandardSortComparator() {
        return getLocationComparator()
            .thenComparing(getCommonComparator());
    }

    private static Comparator<BatchItem> getLocationOnlyComparator() {
        return getLocationComparator();
    }

    private static Comparator<BatchItem> getLocationComparator() {
        return Comparator.nullsLast(Comparator.comparing(BatchItem::getLocationName,
            Comparator.nullsLast(Comparator.naturalOrder())));
    }

    private static Comparator<BatchItem> getPrepComparator() {
        return Comparator
            .nullsLast(Comparator.comparingInt((BatchItem item) ->
                    item.getPrep() != null ? item.getPrep().length() : 0)
                .thenComparing(BatchItem::getPrep, Comparator.nullsLast(Comparator.naturalOrder())))
            .reversed();
    }

    private static Comparator<BatchItem> getCommonComparator() {
        return Comparator.nullsLast(Comparator.comparing(BatchItem::getDepartment,
                Comparator.nullsLast(Comparator.naturalOrder())))
            .thenComparing(Comparator.nullsLast(Comparator.comparing(batchItem ->
                    batchItem != null ? batchItem.getSubCategory() : null,
                Comparator.nullsLast(Comparator.naturalOrder()))))
            .thenComparing(Comparator.nullsLast(Comparator.comparing(batchItem ->
                    batchItem != null ? batchItem.getTitle() : null,
                Comparator.nullsLast(Comparator.naturalOrder()))));
    }
} 