package com.mercaso.wms.batch.dto;

import com.mercaso.wms.batch.constants.BatchConstants;
import com.mercaso.wms.batch.enums.SourceEnum;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Slf4j
public class BreakdownDto {
    private String orderNumber;
    private String breakdown;
    private String districtName;
    private String picker;
    private long mfcBeverage;
    private long mfcOther;
    private long mission;
    private long mercasoProduce;
    private long threePL;
    private long jc;
    private long costco;
    private String jcPart;
    private long vernon;
    private long exotic;
    private long sevenStars;
    private long jetro;
    private long smartAndFinal;
    private long coreMark;
    private long total;
    private String colourMarking;
    private String originalBreakdown;

    public static BreakdownDto of(List<ExcelBatchDto> excelBatchDtos) {
        var beverageCount = 0;
        var mfcOtherCount = 0;
        var missionCount = 0;
        var mercasoProduceCount = 0;
        var jcCount = 0;
        var threePLCount = 0;
        var costcoCount = 0;
        var totalCount = 0;
        var vernonCount = 0;
        var exoticCount = 0;
        var sevenStarsCount = 0;
        var jetroCount = 0;
        var smartAndFinalCount = 0;
        var coreMarkCount = 0;

        for (var batchDto : excelBatchDtos) {
            totalCount += batchDto.getQuantity();
            if (StringUtils.isEmpty(batchDto.getSource())) {
                continue;
            }
            SourceEnum source = SourceEnum.fromName(batchDto.getSource());
            var department = batchDto.getDepartment();

            switch (source) {
                case SourceEnum.MFC, SourceEnum.MDC -> {
                    if (BatchConstants.BEVERAGE.equals(department)) {
                        beverageCount += batchDto.getQuantity();
                    } else {
                        mfcOtherCount += batchDto.getQuantity();
                    }
                }
                case SourceEnum.MISSION -> missionCount += batchDto.getQuantity();
                case SourceEnum.DOWNEY -> threePLCount += batchDto.getQuantity();
                case SourceEnum.COSTCO -> costcoCount += batchDto.getQuantity();
                case VERNON -> vernonCount += batchDto.getQuantity();
                case EXOTIC -> exoticCount += batchDto.getQuantity();
                case SEVEN_STARS -> sevenStarsCount += batchDto.getQuantity();
                case JETRO -> jetroCount += batchDto.getQuantity();
                case SMART_AND_FINAL -> smartAndFinalCount += batchDto.getQuantity();
                case CORE_MARK -> coreMarkCount += batchDto.getQuantity();
                default -> log.warn("Unknown source for batchDto: {}", batchDto);
            }
        }

        var firstBatchDto = excelBatchDtos.getFirst();
        return BreakdownDto.builder()
            .orderNumber(firstBatchDto.getOrderNumber())
            .breakdown(firstBatchDto.getPos())
            .districtName(firstBatchDto.getDistrictName())
            .mfcBeverage(beverageCount)
            .mfcOther(mfcOtherCount)
            .mission(missionCount)
            .mercasoProduce(mercasoProduceCount)
            .jc(jcCount)
            .threePL(threePLCount)
            .costco(costcoCount)
            .vernon(vernonCount)
            .exotic(exoticCount)
            .sevenStars(sevenStarsCount)
            .jetro(jetroCount)
            .smartAndFinal(smartAndFinalCount)
            .coreMark(coreMarkCount)
            .total(totalCount)
            .colourMarking(firstBatchDto.getColourMarking())
            .originalBreakdown(firstBatchDto.getOriginalBreakdown())
            .build();
    }
}
