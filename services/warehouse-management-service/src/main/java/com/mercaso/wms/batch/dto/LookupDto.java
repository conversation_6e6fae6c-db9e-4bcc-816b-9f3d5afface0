package com.mercaso.wms.batch.dto;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.mercaso.wms.batch.enums.SourceEnum;
import java.util.UUID;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class LookupDto {

    @ExcelIgnore
    private UUID id;
    @ExcelProperty(index = 1)
    private String itemNumber;
    @ExcelIgnore
    private String vendorItemNumber;
    private String orderNumber;
    @ExcelProperty(index = 3)
    private String line;
    @ExcelProperty(index = 4)
    private String itemDescription;
    private String pack;
    private String brand;
    @ExcelProperty(index = 9)
    private String aisle;
    @ExcelProperty(index = 8)
    private String from;
    @ExcelProperty(index = 10)
    private String department;
    @ExcelProperty(index = 11)
    private String category;
    @ExcelProperty(index = 12)
    private String subCategory;
    @ExcelProperty(index = 13)
    private String clazz;
    @ExcelIgnore
    private SourceEnum sourceEnum;

}
