package com.mercaso.wms.batch.dto;

import com.mercaso.ims.client.dto.ItemCategoryDto;
import com.mercaso.wms.batch.enums.SourceEnum;
import com.mercaso.wms.domain.location.Location;
import com.mercaso.wms.domain.shippingorder.ShippingOrder;
import java.util.List;
import java.util.Map;
import lombok.Builder;
import lombok.Data;

@Data
@Builder
public class PopulateCondition {

    private Map<SourceEnum, List<LookupDto>> lookUpData;

    private List<ExcelBatchDto> excelBatchDtoList;

    private List<ShippingOrder> shippingOrders;

    private List<Location> locations;

    private List<StockDto> mfcStocks;

    private List<StockDto> mdcStocks;

    private List<StockDto> mdcBigShippingStocks;

    private List<IgnoredOrderDto> ignoredOrders;

    private Map<String, ItemCategoryDto> itemMap;

    private String deliveryDate;

}
