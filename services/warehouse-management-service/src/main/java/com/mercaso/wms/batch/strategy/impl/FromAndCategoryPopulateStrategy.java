package com.mercaso.wms.batch.strategy.impl;

import static java.util.Comparator.comparing;

import com.mercaso.wms.batch.constants.BatchConstants;
import com.mercaso.wms.batch.dto.ExcelBatchDto;
import com.mercaso.wms.batch.dto.LookupDto;
import com.mercaso.wms.batch.dto.PopulateCondition;
import com.mercaso.wms.batch.dto.StockDto;
import com.mercaso.wms.batch.enums.SourceEnum;
import com.mercaso.wms.batch.strategy.PopulateBatchTemplateStrategy;
import com.mercaso.wms.batch.util.ClosestSumFinder;
import com.mercaso.wms.domain.location.enums.LocationType;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

@Component
@Order(3)
@Slf4j
@RequiredArgsConstructor
public class FromAndCategoryPopulateStrategy implements PopulateBatchTemplateStrategy {

    @Override
    public List<ExcelBatchDto> populateBatchTemplate(PopulateCondition populateCondition) {
        log.info("[FromAndCategoryPopulateStrategy] Start populating batch template");

        List<ExcelBatchDto> excelBatchDtoList = populateCondition.getExcelBatchDtoList();
        if (CollectionUtils.isEmpty(excelBatchDtoList)) {
            log.error("[FromAndCategoryPopulateStrategy] BatchDtoList is empty");
            return List.of();
        }

        Map<SourceEnum, List<LookupDto>> lookUpData = populateCondition.getLookUpData();
        List<LookupDto> masterLookupData = lookUpData.get(SourceEnum.MDC);

        List<ExcelBatchDto> batchList = matchFrom(populateCondition);

        Map<String, List<ExcelBatchDto>> orders = new HashMap<>();
        for (ExcelBatchDto dto : batchList) {
            orders.computeIfAbsent(dto.getOrderNumber(), k -> new ArrayList<>()).add(dto);
        }

        populateCategoryForEmptyDepartment(batchList, masterLookupData);

        for (ExcelBatchDto excelBatchDto : batchList) {
            setPrep(excelBatchDto, orders.get(excelBatchDto.getOrderNumber()), lookUpData);
        }
        log.info("[FromAndCategoryPopulateStrategy] Finish to populate batch template");
        return batchList;
    }

    private void populateCategoryForEmptyDepartment(List<ExcelBatchDto> batchList, List<LookupDto> masterLookupData) {
        if (CollectionUtils.isEmpty(masterLookupData)) {
            log.error("[FromAndCategoryPopulateStrategy] Master lookup data is empty");
            return;
        }

        Map<String, LookupDto> masterLookupMap = new HashMap<>();
        for (LookupDto lookupDto : masterLookupData) {
            masterLookupMap.put(lookupDto.getItemNumber(), lookupDto);
        }

        for (ExcelBatchDto batchDto : batchList) {
            if (StringUtils.isEmpty(batchDto.getDepartment())) {
                LookupDto lookupDto = masterLookupMap.get(batchDto.getItemNumber());
                if (lookupDto != null) {
                    populateCategory(batchDto, lookupDto);
                }
            }
        }
    }

    private List<ExcelBatchDto> matchFrom(PopulateCondition populateCondition) {
        Map<SourceEnum, List<LookupDto>> lookUpData = populateCondition.getLookUpData();
        List<ExcelBatchDto> excelBatchDtoList = populateCondition.getExcelBatchDtoList();

        if (CollectionUtils.isEmpty(lookUpData)) {
            return excelBatchDtoList;
        }

        Map<String, List<ExcelBatchDto>> batchDtoMap = new HashMap<>();
        List<LookupDto> masterLookupData = lookUpData.get(SourceEnum.MDC);

        for (ExcelBatchDto batchDto : excelBatchDtoList) {
            if (StringUtils.isEmpty(batchDto.getItemNumber()) && StringUtils.isNotEmpty(batchDto.getItemDescription())) {
                for (LookupDto lookup : masterLookupData) {
                    if (batchDto.getItemDescription().equals(lookup.getItemDescription())) {
                        batchDto.setItemNumber(lookup.getItemNumber());
                        break;
                    }
                }
                if (StringUtils.isEmpty(batchDto.getItemNumber())) {
                    continue;
                }
            }
            batchDtoMap.computeIfAbsent(batchDto.getItemNumber(), k -> new ArrayList<>()).add(batchDto);
        }
        for (Entry<String, List<ExcelBatchDto>> entry : batchDtoMap.entrySet()) {
            List<ExcelBatchDto> remainingBatchDtos = processBatchDtoListByItem(entry.getValue(),
                    !CollectionUtils.isEmpty(populateCondition.getMdcStocks()) ? populateCondition.getMdcStocks().stream()
                        .filter(stockDto -> stockDto.getSku().equals(entry.getKey()))
                        .collect(Collectors.groupingBy(StockDto::getType)) : Map.of(),
                    lookUpData);

            remainingBatchDtos = processBatchDtoListByItem(remainingBatchDtos,
                !CollectionUtils.isEmpty(populateCondition.getMfcStocks()) ? populateCondition.getMfcStocks().stream()
                    .filter(stockDto -> stockDto.getSku().equals(entry.getKey()))
                    .collect(Collectors.groupingBy(StockDto::getType)) : Map.of(),
                lookUpData);

            if (!CollectionUtils.isEmpty(remainingBatchDtos)) {
                for (ExcelBatchDto excelBatchDto : remainingBatchDtos) {
                    fillBatchDtoByLookupData(lookUpData, excelBatchDto);
                }
            }
        }

        excelBatchDtoList.sort(comparing(ExcelBatchDto::getOrderNumber));
        return excelBatchDtoList;
    }

    public List<ExcelBatchDto> processBatchDtoListByItem(List<ExcelBatchDto> excelBatchDtoListByItem,
        Map<LocationType, List<StockDto>> locationTypeListMap,
        Map<SourceEnum, List<LookupDto>> lookUpData) {
        if (CollectionUtils.isEmpty(locationTypeListMap) || CollectionUtils.isEmpty(excelBatchDtoListByItem)) {
            return excelBatchDtoListByItem;
        }
        String itemNumber = excelBatchDtoListByItem.getFirst().getItemNumber();

        List<ExcelBatchDto> remainingExcelBatchDtos = new ArrayList<>(excelBatchDtoListByItem);
        // SPECIAL BIN
        processLocation(locationTypeListMap.get(LocationType.SPECIAL_BIN),
            lookUpData,
            itemNumber,
            remainingExcelBatchDtos,
            LocationType.SPECIAL_BIN);
        // BIN
        List<StockDto> stockDtos = getStockDtos(locationTypeListMap);
        processLocation(stockDtos,
            lookUpData,
            itemNumber,
            remainingExcelBatchDtos,
            LocationType.BIN);
        // RACK
        processLocation(locationTypeListMap.get(LocationType.RACK),
            lookUpData,
            itemNumber,
            remainingExcelBatchDtos,
            LocationType.RACK);
        // ROOM
        processLocation(locationTypeListMap.get(LocationType.ROOM),
            lookUpData,
            itemNumber,
            remainingExcelBatchDtos,
            LocationType.ROOM);
        // RD
        processLocation(locationTypeListMap.get(LocationType.RD),
            lookUpData,
            itemNumber,
            remainingExcelBatchDtos,
            LocationType.RD);
        return remainingExcelBatchDtos;
    }

    private List<StockDto> getStockDtos(Map<LocationType, List<StockDto>> locationTypeListMap) {
        List<StockDto> binStockDtos = locationTypeListMap.get(LocationType.BIN);
        List<StockDto> stockStockDtos = locationTypeListMap.get(LocationType.STOCK);
        if (binStockDtos != null && stockStockDtos != null) {
            binStockDtos.addAll(stockStockDtos);
        } else {
            binStockDtos = binStockDtos != null ? binStockDtos : stockStockDtos;
        }
        return binStockDtos;
    }

    private void processLocation(List<StockDto> stockDtos,
        Map<SourceEnum, List<LookupDto>> lookUpData,
        String itemNumber,
        List<ExcelBatchDto> remainingExcelBatchDtos,
        LocationType locationType) {
        if (remainingExcelBatchDtos.isEmpty() || stockDtos == null) {
            return;
        }
        int stockNum = 0;
        for (StockDto stockDto : stockDtos) {
            if (stockDto.getSku().equals(itemNumber)) {
                stockNum += stockDto.getTotalQty();
            }
        }
        if (stockNum > 0) {
            List<ExcelBatchDto> closetExcelBatchDto = ClosestSumFinder.findClosestSumWithMaxElements(
                remainingExcelBatchDtos, stockNum);

            List<StockDto> subLocations = getSubLocations(stockDtos,
                itemNumber,
                locationType,
                closetExcelBatchDto.stream().mapToInt(ExcelBatchDto::getQuantity).sum());

            if (!CollectionUtils.isEmpty(subLocations)) {
                List<LookupDto> masterLookupData = lookUpData.get(SourceEnum.MDC);
                setLocations(closetExcelBatchDto, subLocations);
                for (ExcelBatchDto excelBatchDto : closetExcelBatchDto) {
                    fillBatchDtoByLookupData(masterLookupData, excelBatchDto, subLocations.getFirst().getSource());
                    remainingExcelBatchDtos.remove(excelBatchDto);
                }
            }
        }
    }

    private List<StockDto> getSubLocations(List<StockDto> stockList,
        String itemNumber,
        LocationType locationType,
        int requiredStockNum) {
        List<StockDto> stockDtos = stockList.stream()
            .filter(stockDto -> stockDto.getSku().equals(itemNumber))
            .toList();

        if (LocationType.BIN == locationType) {
            List<StockDto> binLocations = findStockNumMatchedSubLocations(stockDtos, requiredStockNum);
            if (!CollectionUtils.isEmpty(binLocations)) {
                return binLocations;
            }
        }

        return stockDtos.stream()
            .max(comparing(StockDto::getSubLocation, locationType == LocationType.RD
                ? comparing((String s) -> s.equals(".RD")).thenComparing(s -> s.contains(".RD-"))
                : comparing((String s) -> s.contains(".COOLER"))
                    .thenComparing(s -> s.contains("PHOTO-STUDIO"))
                    .thenComparing(s -> s.contains(".TOBACCO"))
                    .thenComparing(s -> s.contains(".WATER"))
                    .thenComparing(s -> s.contains("-A"))
                    .thenComparing(s -> s.contains("-B"))
                    .thenComparing(s -> s.contains("-C"))
                    .thenComparing(s -> s.contains("-D"))
                    .thenComparing(s -> s.contains("-E"))
                    .thenComparing(s -> s.contains("-F"))
                    .thenComparing(s -> s.contains("RACK"))
                    .thenComparing(s -> s.contains("ROOM"))
                    // the logic for special upper bins -> '.RD-MFC' & '.RD-MDC'
                    .thenComparing(s -> s.contains(".RD-")))).stream().toList();
    }

    private List<StockDto> findStockNumMatchedSubLocations(List<StockDto> stockDtos, int requiredStockNum) {
        List<StockDto> binLocations = stockDtos.stream()
            .filter(stockDto -> stockDto.getType() == LocationType.BIN)
            .filter(stockDto -> stockDto.getTotalQty() >= requiredStockNum)
            .min(Comparator.comparingInt(StockDto::getTotalQty)).stream().toList();

        if (CollectionUtils.isEmpty(binLocations)) {
            binLocations = stockDtos.stream()
                .filter(stockDto -> stockDto.getType() == LocationType.BIN)
                .sorted(Comparator.comparing(StockDto::getSubLocation))
                .toList();
        }

        if (CollectionUtils.isEmpty(binLocations)) {
            binLocations = stockDtos.stream()
                .filter(stockDto -> stockDto.getTotalQty() >= requiredStockNum)
                .min(Comparator.comparingInt(StockDto::getTotalQty)).stream().toList();
        }

        return binLocations;
    }

    private void setLocations(List<ExcelBatchDto> excelBatchDtoList, List<StockDto> stockDtos) {
        if (stockDtos.size() > 1) {
            for (ExcelBatchDto excelBatchDto : excelBatchDtoList) {
                for (StockDto stockDto : stockDtos) {
                    if (stockDto.getRemainingQty() >= excelBatchDto.getQuantity()) {
                        updateExcelBatchDto(excelBatchDto, stockDto);
                        stockDto.setRemainingQty(stockDto.getRemainingQty() - excelBatchDto.getQuantity());
                        break;
                    }
                }
            }

            // if inventory not enough, set the first location
            excelBatchDtoList.stream()
                .filter(excelBatchDto -> excelBatchDto.getLocationId() == null)
                .forEach(excelBatchDto -> updateExcelBatchDto(excelBatchDto, stockDtos.getFirst()));
        } else {
            StockDto stock = stockDtos.getFirst();
            excelBatchDtoList.forEach(excelBatchDto -> updateExcelBatchDto(excelBatchDto, stock));
        }
    }

    private void updateExcelBatchDto(ExcelBatchDto excelBatchDto, StockDto stockDto) {
        excelBatchDto.setFrom(stockDto.getSubLocation());
        excelBatchDto.setLocationId(stockDto.getLocationId());
        excelBatchDto.setSource(stockDto.getSource().name());
    }

    private void fillBatchDtoByLookupData(List<LookupDto> masterLookupData, ExcelBatchDto excelBatchDto, SourceEnum source) {
        if (CollectionUtils.isEmpty(masterLookupData)) {
            return;
        }
        Optional<LookupDto> lookupDtoOptional = masterLookupData.stream()
            .filter(lookup -> lookup.getItemNumber().equals(excelBatchDto.getItemNumber()))
            .findFirst();
        populateData(excelBatchDto, lookupDtoOptional, source);
    }

    private void fillBatchDtoByLookupData(Map<SourceEnum, List<LookupDto>> lookupMap, ExcelBatchDto excelBatchDto) {
        if (StringUtils.isEmpty(excelBatchDto.getSource())) {
            setDataIfExist(excelBatchDto, lookupMap.get(SourceEnum.JETRO), SourceEnum.JETRO);
            setDataIfExist(excelBatchDto, lookupMap.get(SourceEnum.SEVEN_STARS), SourceEnum.SEVEN_STARS);
            setDataIfExist(excelBatchDto, lookupMap.get(SourceEnum.EXOTIC), SourceEnum.EXOTIC);
            setDataIfExist(excelBatchDto, lookupMap.get(SourceEnum.VERNON), SourceEnum.VERNON);
            setDataIfExist(excelBatchDto, lookupMap.get(SourceEnum.MISSION), SourceEnum.MISSION);
            setDataIfExist(excelBatchDto, lookupMap.get(SourceEnum.COSTCO), SourceEnum.COSTCO);
            setDataIfExist(excelBatchDto, lookupMap.get(SourceEnum.DOWNEY), SourceEnum.DOWNEY);
            setDataIfExist(excelBatchDto, lookupMap.get(SourceEnum.SMART_AND_FINAL), SourceEnum.SMART_AND_FINAL);
            setDataIfExist(excelBatchDto, lookupMap.get(SourceEnum.CORE_MARK), SourceEnum.CORE_MARK);
            if (StringUtils.isEmpty(excelBatchDto.getSource()) || StringUtils.isEmpty(excelBatchDto.getDepartment())) {
                setDataIfExist(excelBatchDto, lookupMap.get(SourceEnum.MDC), SourceEnum.MDC);
                excelBatchDto.setFrom("N/A");
                if (StringUtils.isEmpty(excelBatchDto.getDepartment())) {
                    excelBatchDto.setDepartment("N/A");
                }
            }
        }
    }

    private void setDataIfExist(ExcelBatchDto excelBatchDto, List<LookupDto> lookupList, SourceEnum sourceEnum) {
        if (StringUtils.isEmpty(excelBatchDto.getSource()) && !CollectionUtils.isEmpty(lookupList)) {
            Optional<LookupDto> lookupDtoOptional = lookupList.stream()
                .filter(lookup -> lookup.getItemNumber().equals(excelBatchDto.getItemNumber()))
                .findFirst();
            populateData(excelBatchDto, lookupDtoOptional, sourceEnum);
        }
    }

    private static void populateData(ExcelBatchDto excelBatchDto, Optional<LookupDto> lookupDtoOptional, SourceEnum sourceEnum) {
        if (lookupDtoOptional.isPresent()) {
            LookupDto lookupDto = lookupDtoOptional.get();
            if (StringUtils.isBlank(excelBatchDto.getFrom())) {
                excelBatchDto.setFrom(StringUtils.isNotEmpty(lookupDto.getAisle()) ? lookupDto.getAisle() : "N/A");
            }
            excelBatchDto.setDepartment(lookupDto.getDepartment());
            excelBatchDto.setCategory(lookupDto.getCategory());
            excelBatchDto.setSubCategory(lookupDto.getSubCategory());
            excelBatchDto.setClazz(lookupDto.getClazz());
            if (StringUtils.isBlank(excelBatchDto.getSource())) {
                excelBatchDto.setSource(sourceEnum.name());
            }
            if (lookupDto.getId() == null) {
                log.warn("Item id is null for itemNumber: {}", excelBatchDto.getItemNumber());
            }
            excelBatchDto.setItemId(lookupDto.getId());
            excelBatchDto.setVendorItemNumber(lookupDto.getVendorItemNumber());
        }
    }

    private static void populateCategory(ExcelBatchDto excelBatchDto, LookupDto lookupDto) {
        excelBatchDto.setDepartment(lookupDto.getDepartment());
        excelBatchDto.setCategory(lookupDto.getCategory());
        excelBatchDto.setSubCategory(lookupDto.getSubCategory());
        excelBatchDto.setClazz(lookupDto.getClazz());
        excelBatchDto.setItemId(lookupDto.getId());
    }

    private void setPrep(ExcelBatchDto excelBatchDto,
        List<ExcelBatchDto> batchListForOneOrder,
        Map<SourceEnum, List<LookupDto>> lookUpData) {
        if (excelBatchDto.getSource() == null) {
            return;
        }
        if (excelBatchDto.getSource().equals(SourceEnum.MFC.name()) || excelBatchDto.getSource()
            .equals(SourceEnum.MDC.name())) {
            excelBatchDto.setPrep(getPrep(excelBatchDto.getDepartment()));
        } else if (excelBatchDto.getSource().equals(SourceEnum.DOWNEY.name())) {
            excelBatchDto.setPrep(getThreePlPrep(excelBatchDto,
                batchListForOneOrder,
                lookUpData.get(SourceEnum.DOWNEY)));
        } else if (excelBatchDto.getSource().equals(SourceEnum.COSTCO.name())) {
            excelBatchDto.setPrep(getPrep(excelBatchDto, lookUpData.get(SourceEnum.COSTCO)));
        } else if (excelBatchDto.getSource().equals(SourceEnum.MISSION.name())) {
            excelBatchDto.setPrep(getPrep(excelBatchDto, lookUpData.get(SourceEnum.MISSION)));
        }
        if (StringUtils.isEmpty(excelBatchDto.getPrep())) {
            excelBatchDto.setPrep(BatchConstants.MANUAL_INPUT_REQUIRED);
        }
    }

    private String getPrep(String department) {
        if (department == null) {
            return BatchConstants.OTHER;
        }
        Map<String, String> departmentPrepMap = Map.of(
            BatchConstants.BEVERAGE, BatchConstants.BEVERAGE.toUpperCase(Locale.ROOT),
            BatchConstants.CLEANING_AND_LAUNDRY, BatchConstants.CLEANING,
            BatchConstants.TOBACCO_DEPARTMENT, BatchConstants.TOBACCO,
            BatchConstants.FROZEN_DEPARTMENT, BatchConstants.FROZEN,
            BatchConstants.REFRIGERATED_DEPARTMENT, BatchConstants.REFRIGERATED
        );
        return departmentPrepMap.getOrDefault(department, BatchConstants.OTHER);
    }

    private String getThreePlPrep(ExcelBatchDto excelBatchDto,
        List<ExcelBatchDto> batchListForOneOrder,
        List<LookupDto> lookupData) {
        String department = excelBatchDto.getDepartment();
        if (StringUtils.isEmpty(department)) {
            return BatchConstants.OTHER;
        }
        return switch (department) {
            case BatchConstants.TOBACCO_DEPARTMENT -> BatchConstants.TOBACCO;
            case BatchConstants.GROCERY_DEPARTMENT -> BatchConstants.GROCERY;
            case BatchConstants.FROZEN_DEPARTMENT -> BatchConstants.FROZEN;
            case BatchConstants.REFRIGERATED_DEPARTMENT -> BatchConstants.REFRIGERATED;
            case BatchConstants.CANDY_AND_SNACKS -> getCandyAndSnacksPrep(excelBatchDto, batchListForOneOrder, lookupData);
            case BatchConstants.BEVERAGE -> getPrep(excelBatchDto, lookupData);
            default -> BatchConstants.OTHER;
        };
    }

    private String getPrep(ExcelBatchDto excelBatchDto, List<LookupDto> lookupData) {
        return lookupData.stream()
            .filter(Objects::nonNull)
            .filter(lookup -> Objects.equals(lookup.getItemNumber(), excelBatchDto.getItemNumber()))
            .map(lookup -> StringUtils.isNotEmpty(lookup.getAisle()) ? lookup.getAisle() : excelBatchDto.getDepartment())
            .findFirst()
            .orElse(excelBatchDto.getDepartment());
    }

    private String getCandyAndSnacksPrep(ExcelBatchDto excelBatchDto,
        List<ExcelBatchDto> batchListForOneOrder,
        List<LookupDto> lookupData) {
        String itemNumber = excelBatchDto.getItemNumber();
        String category = excelBatchDto.getCategory();
        Optional<LookupDto> lookupDtoOptional = lookupData.stream()
            .filter(lookup -> lookup.getItemNumber().equals(itemNumber))
            .findFirst();

        if (BatchConstants.CANDY_AND_CHOCOLATE.equals(category)) {
            int totalQuantity = batchListForOneOrder.stream()
                .filter(b -> BatchConstants.CANDY_AND_CHOCOLATE.equals(b.getCategory()))
                .filter(b -> SourceEnum.DOWNEY.name().equals(b.getSource()))
                .mapToInt(ExcelBatchDto::getQuantity)
                .sum();
            return totalQuantity > 20 ? excelBatchDto.getOrderNumber() + BatchConstants.CANDY + totalQuantity
                : BatchConstants.CANDY;
        } else if (BatchConstants.SNACKS_CHIPS_COOKIES.equals(category)) {
            return lookupDtoOptional.map(LookupDto::getAisle).orElse(BatchConstants.SNACKS);
        } else {
            return BatchConstants.SNACKS;
        }
    }

}