package com.mercaso.wms.batch.writer.impl;

import static com.alibaba.excel.EasyExcelFactory.writerSheet;

import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.util.StringUtils;
import com.alibaba.excel.write.metadata.fill.FillConfig;
import com.mercaso.wms.batch.dto.BreakdownDto;
import com.mercaso.wms.batch.dto.WriteTemplateCondition;
import com.mercaso.wms.batch.enums.GeneratedDocNameEnum;
import com.mercaso.wms.batch.writer.SheetWriter;
import java.util.ArrayList;
import java.util.LinkedList;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

@Component
@Order(4)
@Slf4j
public class BreakdownSheetsWriter implements SheetWriter {

    @Override
    public void write(ExcelWriter excelWriter, FillConfig fillConfig, WriteTemplateCondition condition) {
        log.info("[BreakdownSheetsWriter] Start to write breakdown sheets");

        List<BreakdownDto> allBreakdownDtos = new LinkedList<>();
        allBreakdownDtos.addAll(condition.getBigBreakdownDtos());
        allBreakdownDtos.addAll(condition.getSmallBreakdownDtos());

        excelWriter.fill(resort(condition.getBigBreakdownDtos()),
            fillConfig,
            writerSheet(GeneratedDocNameEnum.FULL_BREAKDOWN.getValue()).build());
        excelWriter.fill(resort(condition.getSmallBreakdownDtos()),
            fillConfig,
            writerSheet(GeneratedDocNameEnum.FULL_BREAKDOWN_SMALL.getValue()).build());

        excelWriter.fill(mergeBreakdownDtos(allBreakdownDtos),
            fillConfig,
            writerSheet(GeneratedDocNameEnum.FULL_BREAKDOWN_ALL.getValue()).build());
    }

    private List<BreakdownDto> mergeBreakdownDtos(List<BreakdownDto> allBreakdownDtos) {
        List<BreakdownDto> combinedBreakdownDtos = new ArrayList<>();
        for (BreakdownDto dto : allBreakdownDtos) {
            BreakdownDto existingDto = findByOrderNumber(combinedBreakdownDtos, dto.getOrderNumber());
            if (existingDto != null) {
                mergeDto(existingDto, dto);
            } else {
                combinedBreakdownDtos.add(dto);
            }
        }
        return combinedBreakdownDtos;
    }

    private BreakdownDto findByOrderNumber(List<BreakdownDto> dtos, String orderNumber) {
        return dtos.stream()
            .filter(dto -> dto.getOrderNumber().equals(orderNumber))
            .findFirst()
            .orElse(null);
    }

    private void mergeDto(BreakdownDto target, BreakdownDto source) {
        target.setJc(target.getJc() + source.getJc());
        target.setExotic(target.getExotic() + source.getExotic());
        target.setCostco(target.getCostco() + source.getCostco());
        target.setMfcBeverage(target.getMfcBeverage() + source.getMfcBeverage());
        target.setMfcOther(target.getMfcOther() + source.getMfcOther());
        target.setMission(target.getMission() + source.getMission());
        target.setSevenStars(target.getSevenStars() + source.getSevenStars());
        target.setVernon(target.getVernon() + source.getVernon());
        target.setThreePL(target.getThreePL() + source.getThreePL());
        target.setJetro(target.getJetro() + source.getJetro());
        target.setSmartAndFinal(target.getSmartAndFinal() + source.getSmartAndFinal());
        target.setCoreMark(target.getCoreMark() + source.getCoreMark());
        target.setTotal(target.getTotal() + source.getTotal());
    }

    private List<BreakdownDto> resort(List<BreakdownDto> breakdownDtos) {
        if (CollectionUtils.isEmpty(breakdownDtos)) {
            return breakdownDtos;
        }
        List<BreakdownDto> breakdowns = new LinkedList<>();
        for (BreakdownDto breakdownDto : breakdownDtos) {
            if (!StringUtils.isEmpty(breakdownDto.getOriginalBreakdown())) {
                breakdownDto.setBreakdown(breakdownDto.getOriginalBreakdown());
                breakdowns.add(breakdownDto);
            }
        }

        breakdownDtos.removeAll(breakdowns);
        breakdowns.addAll(breakdownDtos);
        return breakdowns;
    }

}
