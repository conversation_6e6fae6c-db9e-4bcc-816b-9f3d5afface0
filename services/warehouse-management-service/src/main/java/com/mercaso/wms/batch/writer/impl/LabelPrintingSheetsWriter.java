package com.mercaso.wms.batch.writer.impl;

import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.fill.FillConfig;
import com.mercaso.wms.batch.dto.ExcelBatchDto;
import com.mercaso.wms.batch.dto.WriteTemplateCondition;
import com.mercaso.wms.batch.enums.GeneratedDocNameEnum;
import com.mercaso.wms.batch.enums.SourceEnum;
import com.mercaso.wms.batch.util.ExcelUtil;
import com.mercaso.wms.batch.writer.SheetWriter;
import java.util.Collections;
import java.util.HashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

@Component
@Order(8)
@Slf4j
public class LabelPrintingSheetsWriter implements SheetWriter {

    @Override
    public void write(ExcelWriter excelWriter, FillConfig fillConfig, WriteTemplateCondition condition) {
        log.info("[LabelPrintingSheetsWriter] Start to write label printing sheets");
        Map<String, String> deliveryDateMap = new HashMap<>();
        deliveryDateMap.put("deliveryDate", condition.getTaggedWith());
        ExcelUtil.writerSheetToTemplate(generateBatchDtoListByQty(condition.getSourceAndListMap()
                .get(GeneratedDocNameEnum.DOWNEY.getValue())),
            null,
            excelWriter,
            fillConfig,
            GeneratedDocNameEnum.DOWNEY_LABELS.getValue(),
            deliveryDateMap);
        ExcelUtil.writerSheetToTemplate(generateBatchDtoListByQty(condition.getSourceAndListMap()
                .get(GeneratedDocNameEnum.COSTCO.getValue())),
            null,
            excelWriter,
            fillConfig,
            GeneratedDocNameEnum.COSTCO_LABELS.getValue(),
            deliveryDateMap);
        ExcelUtil.writerSheetToTemplate(generateBatchDtoListByQty(condition.getSourceAndListMap()
                .get(GeneratedDocNameEnum.MISSION.getValue())),
            null,
            excelWriter,
            fillConfig,
            GeneratedDocNameEnum.MISSION_LABELS.getValue(),
            deliveryDateMap);
        ExcelUtil.writerSheetToTemplate(generateBatchDtoListByQty(condition.getSourceAndListMap()
                .get(GeneratedDocNameEnum.VERNON.getValue())),
            null,
            excelWriter,
            fillConfig,
            GeneratedDocNameEnum.VERNON_LABELS.getValue(),
            deliveryDateMap);
        ExcelUtil.writerSheetToTemplate(generateBatchDtoListByQty(condition.getSourceAndListMap()
                .get(GeneratedDocNameEnum.EXOTIC.getValue())),
            null,
            excelWriter,
            fillConfig,
            GeneratedDocNameEnum.EXOTIC_LABELS.getValue(),
            deliveryDateMap);
        ExcelUtil.writerSheetToTemplate(generateBatchDtoListByQty(condition.getSourceAndListMap()
                .get(SourceEnum.SEVEN_STARS.name())),
            null,
            excelWriter,
            fillConfig,
            GeneratedDocNameEnum.SEVEN_STARS_LABELS.getValue(),
            deliveryDateMap);
        ExcelUtil.writerSheetToTemplate(generateBatchDtoListByQty(condition.getSourceAndListMap()
                .get(SourceEnum.JETRO.name())),
            null,
            excelWriter,
            fillConfig,
            GeneratedDocNameEnum.JETRO_LABELS.getValue(),
            deliveryDateMap);
        ExcelUtil.writerSheetToTemplate(generateBatchDtoListByQty(condition.getSourceAndListMap()
                .get(SourceEnum.SMART_AND_FINAL.name())),
            null,
            excelWriter,
            fillConfig,
            GeneratedDocNameEnum.SMART_AND_FINAL_LABELS.getValue(),
            deliveryDateMap);
        ExcelUtil.writerSheetToTemplate(generateBatchDtoListByQty(condition.getSourceAndListMap()
                .get(SourceEnum.CORE_MARK.name())),
            null,
            excelWriter,
            fillConfig,
            GeneratedDocNameEnum.CORE_MARK_LABELS.getValue(),
            deliveryDateMap);
        writeTemplateForCondition(condition, excelWriter, fillConfig, deliveryDateMap);
        log.info("[LabelPrintingSheetsWriter] Finish writing label printing sheets");
    }

    private void writeTemplateForCondition(WriteTemplateCondition condition,
        ExcelWriter excelWriter,
        FillConfig fillConfig,
        Map<String, String> deliveryDateMap) {
        ExcelUtil.writerSheetToTemplate(
            generateBatchDtoListByQty(condition.getMfcBeverageSmallOrder()), null, excelWriter, fillConfig,
            GeneratedDocNameEnum.MFC_BEVERAGES_SMALL_ORDER_LABELS.getValue(), deliveryDateMap);

        ExcelUtil.writerSheetToTemplate(
            generateBatchDtoListByQty(condition.getMfcBeverageBigOrder()), null, excelWriter, fillConfig,
            GeneratedDocNameEnum.MFC_BEVERAGES_BIG_ORDER_LABELS_WMS_PICKING.getValue(), deliveryDateMap);

        ExcelUtil.writerSheetToTemplate(
            generateBatchDtoListByQty(condition.getMfcCandyOrderItems()), null, excelWriter, fillConfig,
            GeneratedDocNameEnum.MFC_CANDY_LABELS.getValue(), deliveryDateMap);
    }

    private List<ExcelBatchDto> generateBatchDtoListByQty(List<ExcelBatchDto> excelBatchDtos) {
        if (CollectionUtils.isEmpty(excelBatchDtos)) {
            return Collections.emptyList();
        }
        List<ExcelBatchDto> newExcelBatchDtos = new LinkedList<>();
        for (ExcelBatchDto excelBatchDto : excelBatchDtos) {
            for (int i = 0; i < excelBatchDto.getQuantity(); i++) {
                ExcelBatchDto newExcelBatchDto = new ExcelBatchDto();
                BeanUtils.copyProperties(excelBatchDto, newExcelBatchDto);
                newExcelBatchDtos.add(newExcelBatchDto);
            }
        }
        return newExcelBatchDtos;
    }
}