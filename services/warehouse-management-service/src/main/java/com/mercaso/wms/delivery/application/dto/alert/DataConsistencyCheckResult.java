package com.mercaso.wms.delivery.application.dto.alert;

import java.time.Instant;
import java.time.LocalDate;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DataConsistencyCheckResult {

    private LocalDate deliveryDate;
    private Instant checkedAt;
    private boolean hasAlerts;
    private List<DataConsistencyAlert> alerts;
    private DataConsistencyStatistics statistics;

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class DataConsistencyStatistics {
        private int localTaskCount;
        private int routeManagerRouteCount;
        private int totalLocalOrderCount;
        private int totalRouteManagerDeliveryStepCount;
    }

    public int getTotalAlertCount() {
        return alerts != null ? alerts.size() : 0;
    }
} 