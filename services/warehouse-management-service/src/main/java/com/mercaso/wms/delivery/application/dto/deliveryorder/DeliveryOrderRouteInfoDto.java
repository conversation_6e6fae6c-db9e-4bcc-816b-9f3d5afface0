package com.mercaso.wms.delivery.application.dto.deliveryorder;

import com.mercaso.wms.delivery.domain.deliveryorder.enums.DeliveryOrderStatus;
import java.util.UUID;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class DeliveryOrderRouteInfoDto {

    private UUID id;
    private UUID driverUserId;
    private String driverUserName;
    private DeliveryOrderStatus status;
    private String orderNumber;
    private String deliveryDate;
    private String truckNumber;

}
