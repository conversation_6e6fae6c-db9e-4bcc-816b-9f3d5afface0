package com.mercaso.wms.delivery.application.dto.document;

import com.mercaso.wms.application.dto.BaseDto;
import com.mercaso.wms.delivery.domain.document.enums.DeliveryDocumentType;
import java.time.Instant;
import java.util.UUID;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class DeliveryDocumentDto extends BaseDto {

    private UUID id;

    private UUID entityId;

    private String entityName;

    private DeliveryDocumentType documentType;

    private String createdUserName;

    private String fileName;

    private String fileUrl;

    private Instant createdAt;

    private String createdBy;

}
