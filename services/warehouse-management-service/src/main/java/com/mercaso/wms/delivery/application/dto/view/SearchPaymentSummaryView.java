package com.mercaso.wms.delivery.application.dto.view;

import com.mercaso.wms.application.dto.BaseDto;
import java.math.BigDecimal;
import java.util.UUID;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class SearchPaymentSummaryView extends BaseDto {

    private UUID driverUserId;
    private String driverName;
    private String deliveryDate;
    private String batchDate;
    private UUID orderId;
    private String orderNumber;
    private String paymentType;
    private String orderStatus;
    private String moneyBagNumber;
    private BigDecimal originalOrderPrice;
    private BigDecimal finalInvoicePaidPrice;
    /**
     * Value is  Y or N
     */
    private String returns;
    /**
     * Value is Y or N
     */
    private String shortShips;

    private String notes;
}
