package com.mercaso.wms.delivery.application.helper;

import com.mercaso.wms.delivery.application.dto.deliveryorder.DeliveryOrderDto;
import com.mercaso.wms.delivery.application.dto.deliveryorder.DeliveryOrderItemDto;
import com.mercaso.wms.delivery.application.dto.invoice.GenerateInvoiceDto;
import com.mercaso.wms.delivery.application.dto.invoice.GenerateInvoiceDto.AddressDetailData;
import com.mercaso.wms.delivery.application.dto.invoice.GenerateInvoiceDto.CustomerDetailData;
import com.mercaso.wms.delivery.application.dto.invoice.GenerateInvoiceDto.InvoiceLineItemData;
import com.mercaso.wms.delivery.application.dto.invoice.GenerateInvoiceDto.OrderNoteData;
import com.mercaso.wms.delivery.application.dto.invoice.GenerateInvoiceDto.OrganizationData;
import com.mercaso.wms.delivery.application.dto.invoice.GenerateInvoiceDto.SummaryData;
import com.mercaso.wms.delivery.application.service.DeliveryDocumentApplicationService;
import com.mercaso.wms.delivery.domain.deliveryorder.enums.PaymentType;
import com.mercaso.wms.delivery.domain.deliverytask.DeliveryTask;
import com.mercaso.wms.delivery.domain.deliverytask.DeliveryTaskRepository;
import com.mercaso.wms.delivery.domain.document.DeliveryDocument;
import com.mercaso.wms.delivery.domain.document.DeliveryDocumentRepository;
import com.mercaso.wms.delivery.domain.document.enums.DeliveryDocumentType;
import com.mercaso.wms.domain.businessevent.EntityEnums;
import java.math.BigDecimal;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
public class GenerateInvoiceHelper {

    private final DeliveryDocumentRepository deliveryDocumentRepository;
    private final DeliveryDocumentApplicationService deliveryDocumentApplicationService;
    private final DeliveryTaskRepository deliveryTaskRepository;

    public GenerateInvoiceDto buildGenerateInvoiceDto(DeliveryOrderDto deliveryOrderDto) {
        GenerateInvoiceDto generateInvoiceDto = new GenerateInvoiceDto();
        generateInvoiceDto.setDeliveryOrderId(deliveryOrderDto.getId());
        generateInvoiceDto.setOrderNumber(deliveryOrderDto.getOrderNumber().replace("M-", ""));
        generateInvoiceDto.setOrderDate(deliveryOrderDto.getDeliveryDate());
        generateInvoiceDto.setCustomerDetail(CustomerDetailData.builder()
            .email(deliveryOrderDto.getCustomer().getEmail())
            .fullName(getFullName(deliveryOrderDto))
            .build());
        if (deliveryOrderDto.getAddress() != null) {
            generateInvoiceDto.setShippingDetail(AddressDetailData.builder()
                .name(deliveryOrderDto.getAddress().getName())
                .address(
                    deliveryOrderDto.getAddress().getAddressOne())
                .city(deliveryOrderDto.getAddress().getCity())
                .zip(deliveryOrderDto.getAddress().getPostalCode())
                .provinceCode(
                    deliveryOrderDto.getAddress().getState())
                .phoneNumber(deliveryOrderDto.getAddress().getPhone())
                .build());
        }
        generateInvoiceDto.setSummaryData(SummaryData.builder().paid(deliveryOrderDto.getTotalPrice().doubleValue()).build());
        generateInvoiceDto.setLineItems(convert(deliveryOrderDto.getDeliveryOrderItems()));
        generateInvoiceDto.setOrderNote(OrderNoteData.builder().note(deliveryOrderDto.getCustomerNotes()).build());
        generateInvoiceDto.setDriverOrderNote(OrderNoteData.builder().note(deliveryOrderDto.getNotes()).build());
        generateInvoiceDto.setOrganization(OrganizationData.builder().name("Mercaso").build());

        String paymentType = Optional.ofNullable(deliveryOrderDto.getPaymentType())
            .filter(CollectionUtils::isNotEmpty)
            .map(types -> types.stream()
                .map(PaymentType::name)
                .collect(Collectors.joining(",")))
            .orElse(null);
        generateInvoiceDto.setPaymentType(paymentType);
        List<DeliveryDocument> deliveryDocuments = deliveryDocumentRepository.findByEntityIdAndEntityNameAndDocumentTypes(
            deliveryOrderDto.getId(),
            EntityEnums.DELIVERY_ORDER.name(),
            List.of(DeliveryDocumentType.SIGNATURE));
        if (deliveryDocuments != null && !deliveryDocuments.isEmpty()) {
            generateInvoiceDto.setSignature(deliveryDocumentApplicationService.getSingedUrl(deliveryDocuments.getLast()
                .getFileName()));
        }
        DeliveryTask deliveryTask = deliveryTaskRepository.findById(deliveryOrderDto.getDeliveryTaskId());
        if (deliveryTask != null) {
            generateInvoiceDto.setDriverName(deliveryTask.getDriverUserName());
        }
        generateInvoiceDto.setDiscountApplications(deliveryOrderDto.getDiscountApplications());
        return generateInvoiceDto;
    }

    private static String getFullName(DeliveryOrderDto deliveryOrderDto) {
        String name = "";
        if (deliveryOrderDto.getCustomer() != null) {
            String firstName = deliveryOrderDto.getCustomer().getFirstName();
            String lastName = deliveryOrderDto.getCustomer().getLastName();

            if (firstName != null && !firstName.isEmpty() && lastName != null && !lastName.isEmpty()) {
                name = firstName + " " + lastName;
            } else if (firstName != null && !firstName.isEmpty()) {
                name = firstName;
            } else if (lastName != null && !lastName.isEmpty()) {
                name = lastName;
            }
        }
        return name;
    }

    public List<InvoiceLineItemData> convert(List<DeliveryOrderItemDto> items) {
        return items.stream().map(item -> {
            InvoiceLineItemData itemData = new GenerateInvoiceDto.InvoiceLineItemData();
            itemData.setTitle(item.getTitle());
            itemData.setCasePriceInUsd(item.getPrice() != null ? item.getPrice().doubleValue() : 0.0);
            itemData.setSku(item.getSkuNumber());
            itemData.setQty(item.getDeliveredQty() != null ? item.getDeliveredQty().doubleValue() : 0.0);
            itemData.setOrderedQty(item.getQty().doubleValue());
            itemData.setPositionIdxInCart(item.getLine());
            itemData.setRefundedQty(itemData.getOrderedQty() - itemData.getQty());
            itemData.setAmount(itemData.getQty() * itemData.getCasePriceInUsd());
            itemData.setCrvPerCase(item.getCrvPrice() != null && item.getPackageSize() != null ? item.getCrvPrice().multiply(
                BigDecimal.valueOf(item.getPackageSize())).doubleValue() : 0.0);
            itemData.setFulfillmentStatus(itemData.getOrderedQty() - itemData.getQty() == 0 ? "fulfilled" : "partial");
            itemData.setProductContainsNicotine(item.isContainsNicotine());
            itemData.setDiscountAllocations(item.getDiscountAllocations());
            return itemData;
        }).toList();
    }


}
