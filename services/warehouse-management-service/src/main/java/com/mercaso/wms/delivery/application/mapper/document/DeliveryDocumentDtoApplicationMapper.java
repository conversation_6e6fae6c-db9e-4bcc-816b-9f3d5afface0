package com.mercaso.wms.delivery.application.mapper.document;

import com.mercaso.wms.application.mapper.BaseDtoApplicationMapper;
import com.mercaso.wms.delivery.application.dto.document.DeliveryDocumentDto;
import com.mercaso.wms.delivery.domain.document.DeliveryDocument;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;

@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface DeliveryDocumentDtoApplicationMapper extends BaseDtoApplicationMapper<DeliveryDocument, DeliveryDocumentDto> {

    DeliveryDocumentDtoApplicationMapper INSTANCE = Mappers.getMapper(DeliveryDocumentDtoApplicationMapper.class);

}
