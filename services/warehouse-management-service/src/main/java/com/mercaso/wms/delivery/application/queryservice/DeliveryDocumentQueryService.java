package com.mercaso.wms.delivery.application.queryservice;

import com.mercaso.wms.delivery.application.dto.document.DeliveryDocumentDto;
import com.mercaso.wms.delivery.application.mapper.document.DeliveryDocumentDtoApplicationMapper;
import com.mercaso.wms.delivery.domain.deliveryorder.DeliveryOrder;
import com.mercaso.wms.delivery.domain.deliveryorder.DeliveryOrderItem;
import com.mercaso.wms.delivery.domain.deliveryorder.DeliveryOrderRepository;
import com.mercaso.wms.delivery.domain.document.DeliveryDocumentRepository;
import com.mercaso.wms.delivery.domain.document.enums.DeliveryDocumentType;
import com.mercaso.wms.delivery.infrastructure.external.document.DeliveryDocumentAdaptor;
import com.mercaso.wms.domain.businessevent.EntityEnums;
import java.util.List;
import java.util.UUID;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

@Slf4j
@Service
@RequiredArgsConstructor
public class DeliveryDocumentQueryService {

    private final DeliveryDocumentRepository deliveryDocumentRepository;

    private final DeliveryDocumentDtoApplicationMapper deliveryDocumentDtoApplicationMapper;

    private final DeliveryDocumentAdaptor deliveryDocumentAdaptor;

    private final DeliveryOrderRepository deliveryOrderRepository;

    public List<DeliveryDocumentDto> findDocumentsBy(UUID entityId,
        EntityEnums entityName,
        List<DeliveryDocumentType> deliveryDocumentTypes) {
        List<DeliveryDocumentDto> deliveryDocumentDtos = deliveryDocumentDtoApplicationMapper.domainToDtos(
            deliveryDocumentRepository.findByEntityIdAndEntityNameAndDocumentTypes(
            entityId,
            entityName.name(),
                deliveryDocumentTypes));
        deliveryDocumentDtos.forEach(documentDto -> documentDto.setFileUrl(deliveryDocumentAdaptor.getSignedUrl(documentDto.getFileName())));
        return deliveryDocumentDtos;
    }

    public List<DeliveryDocumentDto> findItemDocumentsBy(UUID deliveryOrderId) {
        DeliveryOrder deliveryOrder = deliveryOrderRepository.findById(deliveryOrderId);
        if (deliveryOrder == null) {
            return List.of();
        }
        List<UUID> hasReasonItemIds = deliveryOrder.getDeliveryOrderItems()
            .stream()
            .filter(item -> StringUtils.isNotEmpty(item.getReasonCode()))
            .map(DeliveryOrderItem::getId)
            .toList();
        if (hasReasonItemIds.isEmpty()) {
            return List.of();
        }
        List<DeliveryDocumentDto> deliveryDocumentDtos = deliveryDocumentDtoApplicationMapper.domainToDtos(
            deliveryDocumentRepository.findByEntityIds(
            hasReasonItemIds));
        deliveryDocumentDtos.forEach(documentDto -> documentDto.setFileUrl(deliveryDocumentAdaptor.getSignedUrl(documentDto.getFileName())));
        return deliveryDocumentDtos;
    }

}
