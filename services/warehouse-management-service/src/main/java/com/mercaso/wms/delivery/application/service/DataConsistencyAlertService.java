package com.mercaso.wms.delivery.application.service;

import com.mercaso.wms.delivery.application.dto.alert.DataConsistencyAlert;
import com.mercaso.wms.delivery.application.dto.alert.DataConsistencyAlert.AlertType;
import com.mercaso.wms.delivery.application.dto.alert.DataConsistencyCheckResult;
import com.mercaso.wms.delivery.application.dto.alert.DataConsistencyCheckResult.DataConsistencyStatistics;
import com.mercaso.wms.delivery.domain.deliveryorder.DeliveryOrder;
import com.mercaso.wms.delivery.domain.deliveryorder.DeliveryOrderRepository;
import com.mercaso.wms.delivery.domain.deliverytask.DeliveryTask;
import com.mercaso.wms.delivery.domain.deliverytask.DeliveryTaskRepository;
import com.mercaso.wms.delivery.domain.route.RmRoute;
import com.mercaso.wms.delivery.domain.route.RmRouteRepository;
import com.mercaso.wms.delivery.infrastructure.external.routemanage.RouteManagerAdaptor;
import com.mercaso.wms.delivery.infrastructure.external.routemanage.dto.ApprovedRoute;
import com.mercaso.wms.delivery.infrastructure.external.routemanage.dto.CurrentRoutes;
import com.mercaso.wms.delivery.infrastructure.external.routemanage.dto.Order;
import com.mercaso.wms.delivery.infrastructure.external.routemanage.dto.Route;
import com.mercaso.wms.delivery.infrastructure.external.routemanage.dto.Step;
import com.mercaso.wms.delivery.infrastructure.external.routemanage.enums.OrderStepType;
import com.mercaso.wms.delivery.infrastructure.slackalert.DataConsistencySlackAlert;
import com.mercaso.wms.infrastructure.utils.DateUtils;
import java.time.Instant;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.UUID;
import java.util.function.BinaryOperator;
import java.util.function.Function;
import java.util.function.Predicate;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

/**
 * Service responsible for checking data consistency between local delivery system
 * and Route Manager system. Performs comprehensive validation of tasks, orders,
 * and sequences to ensure data integrity.
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class DataConsistencyAlertService {

    private static final String DISPLAY_LABEL_DELIMITER = "\\.";
    private static final int DISPLAY_LABEL_SEQUENCE_INDEX = 1;
    private static final int SEQUENCE_START_VALUE = 1;
    private static final String LOG_ERROR_FETCH_CURRENT_ROUTE = "Error fetching current route: routeId={}";
    private static final String LOG_WARN_DUPLICATE_ROUTE = "Duplicate route ID found: {}, keeping first occurrence";
    private static final String LOG_WARN_DUPLICATE_TASK_ROUTE = "Duplicate task to route mapping found: taskId maps to both {} and {}, keeping first";
    private static final String LOG_WARN_DUPLICATE_APPROVED_ROUTE = "Duplicate approved route ID found: {}, keeping first occurrence";

    private final DeliveryTaskRepository deliveryTaskRepository;
    private final DeliveryOrderRepository deliveryOrderRepository;
    private final RmRouteRepository rmRouteRepository;
    private final RouteManagerAdaptor routeManagerAdaptor;
    private final DataConsistencySlackAlert dataConsistencySlackAlert;

    /**
     * Main entry point for data consistency checking
     */
    public DataConsistencyCheckResult checkDataConsistency(LocalDate deliveryDate) {
        log.info("Starting data consistency check for delivery date: {}", deliveryDate);

        Instant checkedAt = Instant.now();

        try {
            DataConsistencyContext context = buildDataConsistencyContext(deliveryDate);
            List<DataConsistencyAlert> alerts = performConsistencyChecks(context);

            DataConsistencyCheckResult result = buildSuccessResult(deliveryDate, checkedAt, alerts, context);

            dataConsistencySlackAlert.sendDataConsistencyAlert(result);
            if (result.isHasAlerts()) {
                log.error("Data consistency check failed for delivery date: {}", deliveryDate);
                log.error("Data consistency check result: {}", result);
            }
            return result;

        } catch (Exception e) {
            // Two consecutive error logs can trigger PagerDuty.
            log.error("Data consistency check failed for delivery date: {}", deliveryDate, e);

            DataConsistencyCheckResult errorResult = buildErrorResult(deliveryDate, checkedAt, e);
            dataConsistencySlackAlert.sendDataConsistencyAlert(errorResult);

            log.error("Data consistency check failed for delivery date: {}, errorResult: {}", deliveryDate, errorResult, e);
            return errorResult;
        }
    }

    @Async
    public void checkDataConsistencyAsync(LocalDate deliveryDate) {
        checkDataConsistency(deliveryDate);
    }

    /**
     * Context holder for all data needed during consistency checking
     */
    private record DataConsistencyContext(
        List<DeliveryTask> localTasks,
        List<RmRoute> rmRoutes,
        Map<UUID, List<DeliveryOrder>> taskOrdersMap,
        List<ApprovedRoute> rmApprovedRoutes,
        Map<String, Route> rmRoutesMap,
        Map<UUID, String> taskToRouteMap,
        Map<String, ApprovedRoute> routeIdToApprovedRoute
    ) {

    }

    /**
     * Result of sequence extraction from display labels
     */
    private record SequenceExtractionResult(
        Map<String, Integer> labelSequences,
        List<Integer> sequences,
        boolean isValid
    ) {

    }

    /**
     * Builds the complete data context needed for consistency checking
     */
    private DataConsistencyContext buildDataConsistencyContext(LocalDate deliveryDate) {
        // Fetch local data
        List<DeliveryTask> localTasks = getLocalTasks(deliveryDate);
        List<RmRoute> rmRoutes = getRmRoutes(localTasks);
        Map<UUID, List<DeliveryOrder>> taskOrdersMap = getTaskOrdersMap(localTasks);

        // Fetch Route Manager data
        List<ApprovedRoute> rmApprovedRoutes = getRouteManagerData(deliveryDate);
        Map<String, Route> rmRoutesMap = convertToRouteMap(rmApprovedRoutes);

        // Create derived mappings
        Map<UUID, String> taskToRouteMap = createTaskToRouteMap(rmRoutes);
        Map<String, ApprovedRoute> routeIdToApprovedRoute = createRouteIdToApprovedRouteMap(rmApprovedRoutes);

        return new DataConsistencyContext(
            localTasks, rmRoutes, taskOrdersMap, rmApprovedRoutes,
            rmRoutesMap, taskToRouteMap, routeIdToApprovedRoute
        );
    }

    /**
     * Performs all consistency checks and returns collected alerts
     */
    private List<DataConsistencyAlert> performConsistencyChecks(DataConsistencyContext context) {
        List<DataConsistencyAlert> alerts = new ArrayList<>();

        checkTaskCountConsistency(context.localTasks(), context.rmApprovedRoutes(), alerts);
        checkTaskRouteMapping(context.localTasks(), context.rmRoutesMap(), context.taskToRouteMap(), alerts);
        checkOrderCountConsistency(context, alerts);
        checkSequenceConsistency(context, alerts);

        return alerts;
    }

    /**
     * Builds successful result with statistics
     */
    private DataConsistencyCheckResult buildSuccessResult(
        LocalDate deliveryDate,
        Instant checkedAt,
        List<DataConsistencyAlert> alerts,
        DataConsistencyContext context
    ) {
        DataConsistencyStatistics statistics = generateStatistics(
            context.localTasks(), context.rmApprovedRoutes(), context.taskOrdersMap(), context.rmRoutesMap()
        );

        return DataConsistencyCheckResult.builder()
            .deliveryDate(deliveryDate)
            .checkedAt(checkedAt)
            .hasAlerts(!alerts.isEmpty())
            .alerts(alerts)
            .statistics(statistics)
            .build();
    }

    /**
     * Builds error result when exception occurs
     */
    private DataConsistencyCheckResult buildErrorResult(LocalDate deliveryDate, Instant checkedAt, Exception e) {
        DataConsistencyAlert errorAlert = createAlert(
            AlertType.SYSTEM_ERROR,
            "Data Consistency Check Failed",
            "Failed to perform data consistency check: " + e.getMessage(),
            null, null, null, null, null
        );

        return DataConsistencyCheckResult.builder()
            .deliveryDate(deliveryDate)
            .checkedAt(checkedAt)
            .hasAlerts(true)
            .alerts(List.of(errorAlert))
            .build();
    }

    // =============================================================================
    // Data Fetching Methods
    // =============================================================================

    private List<DeliveryTask> getLocalTasks(LocalDate deliveryDate) {
        String formattedDate = deliveryDate.format(DateUtils.DATE_TO_STRING_FORMATTER);
        return deliveryTaskRepository.findByDeliveryDate(formattedDate);
    }

    private List<RmRoute> getRmRoutes(List<DeliveryTask> localTasks) {
        if (CollectionUtils.isEmpty(localTasks)) {
            return Collections.emptyList();
        }

        Set<UUID> taskIds = extractTaskIds(localTasks);
        return rmRouteRepository.findByDeliveryTaskIdIn(taskIds);
    }

    private Map<UUID, List<DeliveryOrder>> getTaskOrdersMap(List<DeliveryTask> localTasks) {
        if (CollectionUtils.isEmpty(localTasks)) {
            return Collections.emptyMap();
        }

        Set<UUID> taskIds = extractTaskIds(localTasks);
        List<DeliveryOrder> allOrders = deliveryOrderRepository.findAllByDeliveryTaskIdIn(taskIds);

        return allOrders.stream()
            .collect(Collectors.groupingBy(DeliveryOrder::getDeliveryTaskId));
    }

    private List<ApprovedRoute> getRouteManagerData(LocalDate deliveryDate) {
        String rmFormattedDate = deliveryDate.format(DateUtils.RM_DATE_TO_STRING_FORMATTER);
        return routeManagerAdaptor.getApprovedRoutesV2(List.of(rmFormattedDate))
            .getApprovedRoutes();
    }

    private Set<UUID> extractTaskIds(List<DeliveryTask> tasks) {
        return tasks.stream()
            .map(DeliveryTask::getId)
            .collect(Collectors.toSet());
    }

    // =============================================================================
    // Data Transformation Methods
    // =============================================================================

    /**
     * Generic method to create a map from a collection with duplicate handling
     */
    private <T, K, V> Map<K, V> createMapWithDuplicateHandling(
        Collection<T> items,
        Function<T, K> keyMapper,
        Function<T, V> valueMapper,
        BinaryOperator<V> duplicateHandler
    ) {
        if (CollectionUtils.isEmpty(items)) {
            return Collections.emptyMap();
        }

        return items.stream()
            .collect(Collectors.toMap(keyMapper, valueMapper, duplicateHandler));
    }

    /**
     * Generic method to create a filtered map with duplicate handling
     */
    private <T, K, V> Map<K, V> createFilteredMapWithDuplicateHandling(
        Collection<T> items,
        Predicate<T> filter,
        Function<T, K> keyMapper,
        Function<T, V> valueMapper,
        BinaryOperator<V> duplicateHandler
    ) {
        if (CollectionUtils.isEmpty(items)) {
            return Collections.emptyMap();
        }

        return items.stream()
            .filter(filter)
            .collect(Collectors.toMap(keyMapper, valueMapper, duplicateHandler));
    }

    private Map<String, Route> convertToRouteMap(List<ApprovedRoute> approvedRoutes) {
        return createFilteredMapWithDuplicateHandling(
            approvedRoutes,
            ar -> ar.getRoute() != null,
            ar -> ar.getRoute().getId(),
            ApprovedRoute::getRoute,
            this::handleDuplicateRoute
        );
    }

    private Map<UUID, String> createTaskToRouteMap(List<RmRoute> rmRoutes) {
        return createMapWithDuplicateHandling(
            rmRoutes,
            RmRoute::getDeliveryTaskId,
            RmRoute::getRouteId,
            this::handleDuplicateTaskRoute
        );
    }

    private Map<String, ApprovedRoute> createRouteIdToApprovedRouteMap(List<ApprovedRoute> rmApprovedRoutes) {
        return createFilteredMapWithDuplicateHandling(
            rmApprovedRoutes,
            ar -> ar.getRoute() != null,
            ar -> ar.getRoute().getId(),
            Function.identity(),
            this::handleDuplicateApprovedRoute
        );
    }

    private Route handleDuplicateRoute(Route existing, Route replacement) {
        log.warn(LOG_WARN_DUPLICATE_ROUTE, existing.getId());
        return existing;
    }

    private String handleDuplicateTaskRoute(String existing, String replacement) {
        log.warn(LOG_WARN_DUPLICATE_TASK_ROUTE, existing, replacement);
        return existing;
    }

    private ApprovedRoute handleDuplicateApprovedRoute(ApprovedRoute existing, ApprovedRoute replacement) {
        log.warn(LOG_WARN_DUPLICATE_APPROVED_ROUTE, existing.getRoute().getId());
        return existing;
    }

    // =============================================================================
    // Statistics Generation
    // =============================================================================

    private DataConsistencyStatistics generateStatistics(
        List<DeliveryTask> localTasks,
        List<ApprovedRoute> rmApprovedRoutes,
        Map<UUID, List<DeliveryOrder>> taskOrdersMap,
        Map<String, Route> rmRoutesMap
    ) {
        int totalLocalOrderCount = taskOrdersMap.values().stream()
            .mapToInt(List::size)
            .sum();

        int totalRmDeliveryStepCount = rmRoutesMap.values().stream()
            .mapToInt(this::countDeliverySteps)
            .sum();

        return DataConsistencyStatistics.builder()
            .localTaskCount(localTasks.size())
            .routeManagerRouteCount(rmApprovedRoutes.size())
            .totalLocalOrderCount(totalLocalOrderCount)
            .totalRouteManagerDeliveryStepCount(totalRmDeliveryStepCount)
            .build();
    }

    private int countDeliverySteps(Route route) {
        if (route == null || route.getSteps() == null) {
            return 0;
        }

        return (int) route.getSteps().stream()
            .filter(this::isDeliveryStep)
            .count();
    }

    private boolean isDeliveryStep(Step step) {
        return OrderStepType.DELIVERY.getValue().equalsIgnoreCase(step.getType());
    }

    // =============================================================================
    // Consistency Check Methods
    // =============================================================================

    private void checkTaskCountConsistency(
        List<DeliveryTask> localTasks,
        List<ApprovedRoute> rmApprovedRoutes,
        List<DataConsistencyAlert> alerts
    ) {
        int localTaskCount = localTasks.size();
        int rmRouteCount = rmApprovedRoutes.size();

        if (localTaskCount != rmRouteCount) {
            DataConsistencyAlert alert = createAlert(
                AlertType.TASK_COUNT_MISMATCH,
                "Task Count Mismatch",
                String.format("Local task count (%d) does not match Route Manager route count (%d)",
                    localTaskCount, rmRouteCount),
                null, null, null, rmRouteCount, localTaskCount
            );

            alerts.add(alert);
            log.warn("Task count mismatch detected: local={}, route_manager={}", localTaskCount, rmRouteCount);
        }
    }

    private void checkTaskRouteMapping(
        List<DeliveryTask> localTasks,
        Map<String, Route> rmRoutesMap,
        Map<UUID, String> taskToRouteMap,
        List<DataConsistencyAlert> alerts
    ) {
        for (DeliveryTask task : localTasks) {
            String routeId = taskToRouteMap.get(task.getId());

            if (routeId == null) {
                alerts.add(createOrphanedTaskAlert(task));
                log.warn("Orphaned task detected: taskId={}", task.getId());
                continue;
            }

            if (!rmRoutesMap.containsKey(routeId)) {
                alerts.add(createMissingRouteAlert(task, routeId));
                log.warn("Missing route detected: taskId={}, routeId={}", task.getId(), routeId);
            }
        }
    }

    private DataConsistencyAlert createOrphanedTaskAlert(DeliveryTask task) {
        return createAlert(
            AlertType.ORPHANED_TASK,
            "Orphaned Task Detected",
            "Local task exists but has no corresponding route mapping",
            task.getId(), null, null, null, null
        );
    }

    private DataConsistencyAlert createMissingRouteAlert(DeliveryTask task, String routeId) {
        return createAlert(
            AlertType.MISSING_ROUTE,
            "Missing Route in Route Manager",
            "Task maps to route that doesn't exist in Route Manager",
            task.getId(), routeId, null, null, null
        );
    }

    private void checkOrderCountConsistency(DataConsistencyContext context, List<DataConsistencyAlert> alerts) {
        for (DeliveryTask task : context.localTasks()) {
            String routeId = context.taskToRouteMap().get(task.getId());
            if (routeId == null) {
                continue; // Already handled in checkTaskRouteMapping
            }

            Route rmRoute = context.rmRoutesMap().get(routeId);
            if (rmRoute == null) {
                continue; // Already handled in checkTaskRouteMapping
            }

            int localOrderCount = context.taskOrdersMap().getOrDefault(task.getId(), Collections.emptyList()).size();
            int rmDeliveryStepCount = countDeliverySteps(rmRoute);

            if (localOrderCount != rmDeliveryStepCount) {
                handleOrderCountMismatch(task, routeId, localOrderCount, rmDeliveryStepCount, alerts);
            }
        }
    }

    /**
     * Common validation and logging pattern for mismatches
     */
    private void logMismatchResult(
        boolean confirmedMismatch,
        DataConsistencyAlert alert,
        String confirmedLogMessage,
        String resolvedLogMessage,
        List<DataConsistencyAlert> alerts
    ) {
        if (confirmedMismatch) {
            alerts.add(alert);
            log.warn(confirmedLogMessage);
        } else {
            log.info(resolvedLogMessage);
        }
    }

    /**
     * Common validation and logging pattern for sequence mismatches
     */
    private void logSequenceMismatchResult(
        boolean confirmedMismatch,
        List<DataConsistencyAlert> sequenceAlerts,
        String confirmedLogMessage,
        String resolvedLogMessage,
        List<DataConsistencyAlert> alerts
    ) {
        if (confirmedMismatch) {
            alerts.addAll(sequenceAlerts);
            log.warn(confirmedLogMessage);
        } else {
            log.info(resolvedLogMessage);
        }
    }

    private void handleOrderCountMismatch(
        DeliveryTask task,
        String routeId,
        int localOrderCount,
        int rmDeliveryStepCount,
        List<DataConsistencyAlert> alerts
    ) {
        boolean confirmedMismatch = validateOrderCountWithCurrentRoute(routeId, localOrderCount);

        DataConsistencyAlert alert = createAlert(
            AlertType.ORDER_COUNT_MISMATCH,
            "Order Count Mismatch",
            String.format(
                "Task %s: local order count (%d) does not match Route Manager delivery step count (%d) - confirmed by current route",
                task.getNumber(),
                localOrderCount,
                rmDeliveryStepCount),
            task.getId(), routeId, null, rmDeliveryStepCount, localOrderCount
        );

        logMismatchResult(
            confirmedMismatch,
            alert,
            String.format("Order count mismatch confirmed: taskId=%s, routeId=%s, local=%d, rm=%d",
                task.getId(), routeId, localOrderCount, rmDeliveryStepCount),
            String.format("Order count mismatch resolved by current route: taskId=%s, routeId=%s, local=%d, rm=%d",
                task.getId(), routeId, localOrderCount, rmDeliveryStepCount),
            alerts
        );
    }

    private void checkSequenceConsistency(DataConsistencyContext context, List<DataConsistencyAlert> alerts) {
        context.localTasks().stream()
            .filter(task -> shouldCheckTaskSequence(task, context))
            .forEach(task -> processTaskSequenceCheck(task, context, alerts));
    }

    private boolean shouldCheckTaskSequence(DeliveryTask task, DataConsistencyContext context) {
        String routeId = context.taskToRouteMap().get(task.getId());
        if (routeId == null) {
            return false;
        }

        Route rmRoute = context.rmRoutesMap().get(routeId);
        if (rmRoute == null) {
            return false;
        }

        ApprovedRoute approvedRoute = context.routeIdToApprovedRoute().get(routeId);
        if (approvedRoute == null) {
            log.warn("ApprovedRoute not found for routeId: {}", routeId);
            return false;
        }

        return true;
    }

    private void processTaskSequenceCheck(DeliveryTask task, DataConsistencyContext context, List<DataConsistencyAlert> alerts) {
        String routeId = context.taskToRouteMap().get(task.getId());
        ApprovedRoute approvedRoute = context.routeIdToApprovedRoute().get(routeId);
        List<DeliveryOrder> localOrders = context.taskOrdersMap().getOrDefault(task.getId(), Collections.emptyList());

        checkOrderSequences(task, localOrders, approvedRoute, routeId, alerts);
    }

    private void checkOrderSequences(
        DeliveryTask task,
        List<DeliveryOrder> localOrders,
        ApprovedRoute approvedRoute,
        String routeId,
        List<DataConsistencyAlert> alerts
    ) {
        Map<String, Integer> rmSequences = extractRmSequences(approvedRoute);
        List<DataConsistencyAlert> sequenceAlerts = collectSequenceAlerts(task, localOrders, rmSequences, approvedRoute);

        // If sequence mismatches found, validate with current route
        if (!sequenceAlerts.isEmpty()) {
            boolean confirmedMismatch = validateSequenceWithCurrentRoute(routeId, sequenceAlerts, approvedRoute);

            logSequenceMismatchResult(
                confirmedMismatch,
                sequenceAlerts,
                String.format("Sequence mismatches confirmed by current route: taskId=%s, routeId=%s, count=%d",
                    task.getId(), routeId, sequenceAlerts.size()),
                String.format("Sequence mismatches resolved by current route: taskId=%s, routeId=%s, count=%d",
                    task.getId(), routeId, sequenceAlerts.size()),
                alerts
            );
        }
    }

    private List<DataConsistencyAlert> collectSequenceAlerts(
        DeliveryTask task,
        List<DeliveryOrder> localOrders,
        Map<String, Integer> rmSequences,
        ApprovedRoute approvedRoute
    ) {
        List<DataConsistencyAlert> sequenceAlerts = new ArrayList<>();

        // Check sequence consistency
        for (DeliveryOrder order : localOrders) {
            String orderNumber = order.getOrderNumber();
            Integer localSequence = order.getSequence();
            Integer rmSequence = rmSequences.get(orderNumber);

            if (isSequenceMismatch(localSequence, rmSequence)) {
                DataConsistencyAlert alert = createAlert(
                    AlertType.SEQUENCE_MISMATCH,
                    "Order Sequence Mismatch",
                    String.format(
                        "Order %s in task %s: local sequence (%d) does not match Route Manager sequence (%d)",
                        orderNumber, task.getNumber(), localSequence, rmSequence),
                    task.getId(), approvedRoute.getRoute().getId(), orderNumber, rmSequence, localSequence
                );

                sequenceAlerts.add(alert);
                log.warn("Sequence mismatch detected: taskId={}, orderNumber={}, local={}, rm={}",
                    task.getId(), orderNumber, localSequence, rmSequence);
            }
        }

        return sequenceAlerts;
    }

    private boolean isSequenceMismatch(Integer localSequence, Integer rmSequence) {
        return localSequence != null && rmSequence != null && !localSequence.equals(rmSequence);
    }

    // =============================================================================
    // Route Manager Validation Methods
    // =============================================================================

    private Route getCurrentRoute(String routeId) {
        try {
            log.debug("Fetching current route: routeId={}", routeId);

            CurrentRoutes currentRoutes = routeManagerAdaptor.getCurrentRoute(routeId);
            if (currentRoutes == null || currentRoutes.getRoutes() == null ||
                !currentRoutes.getRoutes().containsKey(routeId)) {
                log.warn("Current route not found for routeId: {}", routeId);
                return null;
            }

            return currentRoutes.getRoutes().get(routeId);

        } catch (Exception e) {
            log.error(LOG_ERROR_FETCH_CURRENT_ROUTE, routeId, e);
            return null;
        }
    }

    private boolean validateOrderCountWithCurrentRoute(String routeId, int localOrderCount) {
        log.debug("Validating order count with current route: routeId={}, localCount={}", routeId, localOrderCount);

        Route currentRoute = getCurrentRoute(routeId);
        if (currentRoute == null) {
            return true; // Consider as confirmed mismatch if current route not available
        }

        RouteValidationResult validation = validateRouteAndExtractSteps(currentRoute);
        int currentRouteDeliveryStepCount = validation.isValid() ? validation.deliverySteps().size() : 0;
        boolean mismatch = localOrderCount != currentRouteDeliveryStepCount;

        log.debug("Current route validation result: routeId={}, localCount={}, currentCount={}, mismatch={}",
            routeId, localOrderCount, currentRouteDeliveryStepCount, mismatch);

        return mismatch;
    }

    private boolean validateSequenceWithCurrentRoute(String routeId, List<DataConsistencyAlert> sequenceAlerts,
        ApprovedRoute approvedRoute) {
        log.debug("Validating sequence mismatches with current route: routeId={}, alertCount={}",
            routeId, sequenceAlerts.size());

        CurrentRoutes currentRoutes = getCurrentRoutes(routeId);
        if (currentRoutes == null) {
            return true; // Consider as confirmed mismatch if current route not available
        }

        Route currentRoute = currentRoutes.getRoutes().get(routeId);
        if (currentRoute == null) {
            return true; // Consider as confirmed mismatch if current route not available
        }

        Map<String, Integer> currentRouteSequences = extractRmSequencesFromCurrentRoute(currentRoute, currentRoutes);

        // Check if any sequence mismatches still exist with current route
        return sequenceAlerts.stream().anyMatch(alert ->
            isSequenceMismatchConfirmed(alert, currentRouteSequences)
        );
    }

    /**
     * Gets the complete CurrentRoutes structure for proper order mapping
     */
    private CurrentRoutes getCurrentRoutes(String routeId) {
        try {
            log.debug("Fetching current routes structure: routeId={}", routeId);

            CurrentRoutes currentRoutes = routeManagerAdaptor.getCurrentRoute(routeId);
            if (currentRoutes == null || currentRoutes.getRoutes() == null ||
                !currentRoutes.getRoutes().containsKey(routeId)) {
                log.warn("Current route not found for routeId: {}", routeId);
                return null;
            }

            return currentRoutes;

        } catch (Exception e) {
            log.error(LOG_ERROR_FETCH_CURRENT_ROUTE, routeId, e);
            return null;
        }
    }

    /**
     * Extracts sequences from current route using its own order mapping
     */
    private Map<String, Integer> extractRmSequencesFromCurrentRoute(Route currentRoute, CurrentRoutes currentRoutes) {
        RouteValidationResult validation = validateRouteAndExtractSteps(currentRoute);
        if (!validation.isValid()) {
            return Collections.emptyMap();
        }

        return extractSequencesFromCurrentRoute(validation.deliverySteps(), currentRoutes, currentRoute.getId());
    }

    /**
     * Extracts sequences from delivery steps using current route's order mapping
     */
    private Map<String, Integer> extractSequencesFromCurrentRoute(
        List<Step> deliverySteps,
        CurrentRoutes currentRoutes,
        String routeId
    ) {
        // Try to extract sequences from displayLabel and validate
        SequenceExtractionResult extractionResult = extractSequencesFromDisplayLabelsWithCurrentRoute(
            deliverySteps, currentRoutes);

        if (extractionResult.isValid() && validateSequenceIntegrity(extractionResult.sequences(), deliverySteps.size())) {
            log.debug("Using displayLabel sequences for current route {}: {}", routeId, extractionResult.labelSequences());
            return extractionResult.labelSequences();
        } else {
            // Use array index + 1 as sequence
            log.debug("Using array index sequences for current route {} due to invalid displayLabel sequences", routeId);
            return generateIndexBasedSequencesFromCurrentRoute(deliverySteps, currentRoutes);
        }
    }

    /**
     * Extracts sequences from display labels using current route's order mapping
     */
    private SequenceExtractionResult extractSequencesFromDisplayLabelsWithCurrentRoute(
        List<Step> deliverySteps, CurrentRoutes currentRoutes) {
        Map<String, Integer> labelSequences = new HashMap<>();
        List<Integer> extractedSequences = new ArrayList<>();

        for (Step step : deliverySteps) {
            if (!processStepForSequenceExtractionWithCurrentRoute(step, currentRoutes, labelSequences, extractedSequences)) {
                return new SequenceExtractionResult(Collections.emptyMap(), Collections.emptyList(), false);
            }
        }

        return new SequenceExtractionResult(labelSequences, extractedSequences, true);
    }

    /**
     * Processes a step for sequence extraction using current route's order mapping
     */
    private boolean processStepForSequenceExtractionWithCurrentRoute(
        Step step,
        CurrentRoutes currentRoutes,
        Map<String, Integer> labelSequences,
        List<Integer> extractedSequences
    ) {
        Integer sequence = extractSequenceFromDisplayLabel(step.getDisplayLabel());
        if (sequence == null || step.getOrderId() == null) {
            return false;
        }

        Optional<String> orderNumber = getOrderNumberFromCurrentRoute(currentRoutes, step.getOrderId());
        if (orderNumber.isEmpty()) {
            log.warn("Could not find orderNumber for orderId: {} in current route",
                step.getOrderId());
            return false;
        }

        labelSequences.put(orderNumber.get(), sequence);
        extractedSequences.add(sequence);
        return true;
    }

    /**
     * Gets order number from current route's order mapping
     */
    private Optional<String> getOrderNumberFromCurrentRoute(CurrentRoutes currentRoutes, String orderId) {
        if (currentRoutes.getOrders() == null) {
            return Optional.empty();
        }

        return currentRoutes.getOrders().values().stream()
            .filter(order -> order != null && orderId.equals(order.getId()))
            .map(Order::getName)
            .filter(StringUtils::isNotBlank)
            .findFirst();
    }

    /**
     * Generates index-based sequences using current route's order mapping
     */
    private Map<String, Integer> generateIndexBasedSequencesFromCurrentRoute(
        List<Step> deliverySteps, CurrentRoutes currentRoutes) {
        Map<String, Integer> finalSequences = new HashMap<>();

        for (int i = 0; i < deliverySteps.size(); i++) {
            Step step = deliverySteps.get(i);
            if (step.getOrderId() != null) {
                Optional<String> orderNumber = getOrderNumberFromCurrentRoute(currentRoutes, step.getOrderId());
                if (orderNumber.isPresent()) {
                    finalSequences.put(orderNumber.get(), i + SEQUENCE_START_VALUE);
                } else {
                    log.warn("Could not find orderNumber for orderId: {} in current route",
                        step.getOrderId());
                }
            }
        }

        return finalSequences;
    }

    private boolean isSequenceMismatchConfirmed(
        DataConsistencyAlert alert,
        Map<String, Integer> currentRouteSequences
    ) {
        String orderNumber = alert.getOrderNumber();
        Integer localSequence = alert.getActualValue();
        Integer currentRouteSequence = currentRouteSequences.get(orderNumber);

        if (currentRouteSequence != null && !currentRouteSequence.equals(localSequence)) {
            log.debug("Sequence mismatch confirmed by current route: orderNumber={}, local={}, current={}",
                orderNumber, localSequence, currentRouteSequence);
            return true;
        }
        return false;
    }

    // =============================================================================
    // Utility Methods
    // =============================================================================

    private DataConsistencyAlert createAlert(
        AlertType alertType,
        String title,
        String description,
        UUID taskId,
        String routeId,
        String orderNumber,
        Integer expectedValue,
        Integer actualValue
    ) {
        return DataConsistencyAlert.builder()
            .alertType(alertType.getValue())
            .title(title)
            .description(description)
            .taskId(taskId)
            .routeId(routeId)
            .orderNumber(orderNumber)
            .expectedValue(expectedValue)
            .actualValue(actualValue)
            .detectedAt(Instant.now())
            .build();
    }

    // =============================================================================
    // Sequence Extraction Methods
    // =============================================================================

    private Map<String, Integer> extractRmSequences(ApprovedRoute approvedRoute) {
        return extractRmSequencesFromRoute(approvedRoute.getRoute(), approvedRoute);
    }

    private Map<String, Integer> extractRmSequencesFromRoute(Route rmRoute, ApprovedRoute approvedRoute) {
        RouteValidationResult validation = validateRouteAndExtractSteps(rmRoute);
        if (!validation.isValid()) {
            return Collections.emptyMap();
        }

        return extractSequencesWithFallback(validation.deliverySteps(), approvedRoute, rmRoute.getId());
    }

    /**
     * Extracts sequences from delivery steps with fallback mechanism
     */
    private Map<String, Integer> extractSequencesWithFallback(
        List<Step> deliverySteps,
        ApprovedRoute approvedRoute,
        String routeId
    ) {
        // Try to extract sequences from displayLabel and validate
        SequenceExtractionResult extractionResult = extractSequencesFromDisplayLabels(deliverySteps, approvedRoute);

        if (extractionResult.isValid() && validateSequenceIntegrity(extractionResult.sequences(), deliverySteps.size())) {
            log.debug("Using displayLabel sequences for route {}: {}", routeId, extractionResult.labelSequences());
            return extractionResult.labelSequences();
        } else {
            // Use array index + 1 as sequence
            log.debug("Using array index sequences for route {} due to invalid displayLabel sequences", routeId);
            return generateIndexBasedSequences(deliverySteps, approvedRoute);
        }
    }

    /**
     * Validates route and extracts delivery steps with common error handling
     */
    private record RouteValidationResult(boolean isValid, List<Step> deliverySteps) {

        public static RouteValidationResult invalid() {
            return new RouteValidationResult(false, Collections.emptyList());
        }

        public static RouteValidationResult valid(List<Step> deliverySteps) {
            return new RouteValidationResult(true, deliverySteps);
        }
    }

    private RouteValidationResult validateRouteAndExtractSteps(Route route) {
        if (route == null || route.getSteps() == null) {
            return RouteValidationResult.invalid();
        }

        List<Step> deliverySteps = filterDeliverySteps(route.getSteps());
        if (deliverySteps.isEmpty()) {
            return RouteValidationResult.invalid();
        }

        return RouteValidationResult.valid(deliverySteps);
    }

    private boolean isRouteInvalid(Route route) {
        return route == null || route.getSteps() == null;
    }

    // =============================================================================
    // Helper Methods for Sequence Processing
    // =============================================================================

    private List<Step> filterDeliverySteps(List<Step> steps) {
        return steps.stream()
            .filter(this::isDeliveryStep)
            .collect(Collectors.toList());
    }

    private SequenceExtractionResult extractSequencesFromDisplayLabels(List<Step> deliverySteps, ApprovedRoute approvedRoute) {
        Map<String, Integer> labelSequences = new HashMap<>();
        List<Integer> extractedSequences = new ArrayList<>();

        for (Step step : deliverySteps) {
            if (!processStepForSequenceExtraction(step, approvedRoute, labelSequences, extractedSequences)) {
                return new SequenceExtractionResult(Collections.emptyMap(), Collections.emptyList(), false);
            }
        }

        return new SequenceExtractionResult(labelSequences, extractedSequences, true);
    }

    private boolean processStepForSequenceExtraction(
        Step step,
        ApprovedRoute approvedRoute,
        Map<String, Integer> labelSequences,
        List<Integer> extractedSequences
    ) {
        Integer sequence = extractSequenceFromDisplayLabel(step.getDisplayLabel());
        if (sequence == null || step.getOrderId() == null) {
            return false;
        }

        Optional<String> orderNumber = getOrderNumberFromOrderId(approvedRoute, step.getOrderId());
        if (orderNumber.isEmpty()) {
            log.warn("Could not find orderNumber for orderId: {} in route: {}",
                step.getOrderId(), approvedRoute.getRoute().getId());
            return false;
        }

        labelSequences.put(orderNumber.get(), sequence);
        extractedSequences.add(sequence);
        return true;
    }


    private Map<String, Integer> generateIndexBasedSequences(List<Step> deliverySteps, ApprovedRoute approvedRoute) {
        Map<String, Integer> finalSequences = new HashMap<>();

        for (int i = 0; i < deliverySteps.size(); i++) {
            Step step = deliverySteps.get(i);
            if (step.getOrderId() != null) {
                Optional<String> orderNumber = getOrderNumberFromOrderId(approvedRoute, step.getOrderId());
                if (orderNumber.isPresent()) {
                    finalSequences.put(orderNumber.get(), i + SEQUENCE_START_VALUE);
                } else {
                    log.warn("Could not find orderNumber for orderId: {} in route: {}",
                        step.getOrderId(), approvedRoute.getRoute().getId());
                }
            }
        }

        return finalSequences;
    }


    private Optional<String> getOrderNumberFromOrderId(ApprovedRoute approvedRoute, String orderId) {
        if (approvedRoute.getOrders() == null) {
            return Optional.empty();
        }

        return approvedRoute.getOrders().values().stream()
            .filter(order -> order != null && orderId.equals(order.getId()))
            .map(Order::getName)
            .filter(StringUtils::isNotBlank)
            .findFirst();
    }

    private boolean validateSequenceIntegrity(List<Integer> extractedSequences, int expectedCount) {
        if (expectedCount == 0) {
            return handleEmptySequenceCase(extractedSequences);
        }

        if (extractedSequences.size() != expectedCount) {
            log.error("Sequence count mismatch: expected {}, found {}", expectedCount, extractedSequences.size());
            return false;
        }

        return validateSequenceCompleteness(extractedSequences, expectedCount);
    }

    private boolean handleEmptySequenceCase(List<Integer> extractedSequences) {
        boolean valid = extractedSequences.isEmpty();
        if (valid) {
            log.info("No delivery steps expected and none found - sequences are valid");
        } else {
            log.error("Expected no delivery steps but found {}", extractedSequences.size());
        }
        return valid;
    }

    private boolean validateSequenceCompleteness(List<Integer> extractedSequences, int expectedCount) {
        List<Integer> sortedSequences = extractedSequences.stream()
            .distinct()
            .sorted()
            .toList();

        if (sortedSequences.size() != expectedCount) {
            log.error("Duplicate sequences found: unique count {}, expected {}", sortedSequences.size(), expectedCount);
            return false;
        }

        return validateSequenceRange(sortedSequences, expectedCount);
    }

    private boolean validateSequenceRange(List<Integer> sortedSequences, int expectedCount) {
        if (sortedSequences.isEmpty()) {
            return false;
        }

        if (sortedSequences.getFirst() != SEQUENCE_START_VALUE) {
            log.error("Sequences don't start from {}: minimum sequence is {}", SEQUENCE_START_VALUE, sortedSequences.getFirst());
            return false;
        }

        if (sortedSequences.getLast() != expectedCount) {
            log.error("Max sequence doesn't match expected count: max {}, expected {}",
                sortedSequences.getLast(), expectedCount);
            return false;
        }

        return validateSequenceConsecutiveness(sortedSequences, expectedCount);
    }

    private boolean validateSequenceConsecutiveness(List<Integer> sortedSequences, int expectedCount) {
        for (int i = 0; i < sortedSequences.size(); i++) {
            if (sortedSequences.get(i) != (i + SEQUENCE_START_VALUE)) {
                log.error("Non-consecutive sequence found at position {}: expected {}, found {}",
                    i, i + SEQUENCE_START_VALUE, sortedSequences.get(i));
                return false;
            }
        }

        log.info("Sequence validation passed: sequences are valid from {} to {}", SEQUENCE_START_VALUE, expectedCount);
        return true;
    }

    private Integer extractSequenceFromDisplayLabel(String displayLabel) {
        if (StringUtils.isBlank(displayLabel)) {
            return null;
        }

        try {
            String[] parts = displayLabel.split(DISPLAY_LABEL_DELIMITER);
            if (parts.length > DISPLAY_LABEL_SEQUENCE_INDEX) {
                return Integer.parseInt(parts[DISPLAY_LABEL_SEQUENCE_INDEX].trim());
            }
        } catch (NumberFormatException e) {
            log.error("Failed to extract sequence from displayLabel: {}", displayLabel, e);
        }

        return null;
    }

} 