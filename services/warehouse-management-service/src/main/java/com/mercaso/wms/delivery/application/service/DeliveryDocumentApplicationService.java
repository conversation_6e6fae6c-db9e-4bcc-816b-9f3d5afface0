package com.mercaso.wms.delivery.application.service;

import static java.util.Base64.getEncoder;
import static java.util.Base64.getUrlDecoder;

import com.fasterxml.jackson.core.type.TypeReference;
import com.mercaso.document.operations.models.DocumentResponse;
import com.mercaso.document.operations.models.UploadDocumentRequest;
import com.mercaso.security.auth0.utils.CipherUtil;
import com.mercaso.wms.delivery.application.command.DeliveryUploadDocumentCommand;
import com.mercaso.wms.delivery.application.dto.document.DeliveryDocumentDto;
import com.mercaso.wms.delivery.application.mapper.document.DeliveryDocumentDtoApplicationMapper;
import com.mercaso.wms.delivery.domain.deliveryorder.DeliveryOrder;
import com.mercaso.wms.delivery.domain.deliveryorder.DeliveryOrderRepository;
import com.mercaso.wms.delivery.domain.document.DeliveryDocument;
import com.mercaso.wms.delivery.domain.document.DeliveryDocumentRepository;
import com.mercaso.wms.delivery.infrastructure.exception.DeliveryBadRequestException;
import com.mercaso.wms.delivery.infrastructure.exception.DeliveryBusinessException;
import com.mercaso.wms.delivery.infrastructure.external.document.DeliveryDocumentAdaptor;
import com.mercaso.wms.domain.businessevent.EntityEnums;
import com.mercaso.wms.infrastructure.config.EncryptConfig;
import com.mercaso.wms.infrastructure.utils.DateUtils;
import com.mercaso.wms.infrastructure.utils.SerializationUtils;
import jakarta.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.IOException;
import java.io.OutputStream;
import java.nio.file.Files;
import java.nio.file.Path;
import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.Lists;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

@Slf4j
@Service
@RequiredArgsConstructor
public class DeliveryDocumentApplicationService {

    private final DeliveryDocumentRepository deliveryDocumentRepository;

    private final DeliveryDocumentAdaptor deliveryDocumentAdaptor;

    private final DeliveryOrderRepository deliveryOrderRepository;

    private final DeliveryDocumentDtoApplicationMapper deliveryDocumentDtoApplicationMapper;

    private final EncryptConfig encryptConfig;

    @Value("${external.public-download-signature-url}")
    private String downloadSignatureUrl;

    public List<DeliveryDocumentDto> uploadDocuments(DeliveryUploadDocumentCommand command, MultipartFile[] files) {
        log.info("[uploadDocuments] Uploading multiple documents: {}, number of files: {}", command, files.length);
        validateEntityId(command);

        List<DeliveryDocumentDto> results = new ArrayList<>();
        for (MultipartFile file : files) {
            if (file != null && !file.isEmpty()) {
                upload(command, file, results);
            }
        }

        return results;
    }

    public List<DeliveryDocumentDto> uploadDocuments(List<DeliveryUploadDocumentCommand> commands,
        Map<String, MultipartFile> files) {
        List<DeliveryDocumentDto> deliveryDocumentDtos = Lists.newArrayList();
        commands.forEach(command -> command.getClientFileIds().forEach(clientFileId -> {
            MultipartFile multipartFile = files.get(clientFileId);
            if (multipartFile != null) {
                upload(command, multipartFile, deliveryDocumentDtos);
            }
        }));
        return deliveryDocumentDtos;
    }

    private void upload(DeliveryUploadDocumentCommand command, MultipartFile file, List<DeliveryDocumentDto> results) {
        try {
            UploadDocumentRequest document = UploadDocumentRequest.builder()
                .content(file.getBytes())
                .documentName(generateFileName(command, file.getOriginalFilename()))
                .build();
            DeliveryDocumentDto deliveryDocumentDto = uploadDocument(command, document);
            results.add(deliveryDocumentDto);
        } catch (Exception e) {
            log.error("[uploadDocuments] Error uploading file: {}", file.getOriginalFilename(), e);
        }
    }

    public DeliveryDocumentDto uploadDocument(DeliveryUploadDocumentCommand command, MultipartFile file) throws IOException {
        validateEntityId(command);
        UploadDocumentRequest document = UploadDocumentRequest.builder()
            .content(file.getBytes())
            .documentName(generateFileName(command, file.getName()))
            .build();
        return uploadDocument(command, document);
    }

    public DeliveryDocumentDto uploadDocument(DeliveryUploadDocumentCommand command, File file) throws IOException {
        validateEntityId(command);
        UploadDocumentRequest document = UploadDocumentRequest.builder()
            .content(Files.readAllBytes(file.toPath()))
            .documentName(generateFileName(command, file.getName()))
            .build();
        return uploadDocument(command, document);
    }

    private DeliveryDocumentDto uploadDocument(DeliveryUploadDocumentCommand command, UploadDocumentRequest document) {
        DocumentResponse documentResponse = deliveryDocumentAdaptor.uploadToS3(document);

        DeliveryDocument newDeliveryDocument = DeliveryDocument.builder()
            .build()
            .createDeliveryOrderDocument(command, documentResponse.getName());
        DeliveryDocumentDto deliveryDocumentDto = deliveryDocumentDtoApplicationMapper.domainToDto(deliveryDocumentRepository.save(
            newDeliveryDocument));
        deliveryDocumentDto.setFileUrl(documentResponse.getSignedUrl());
        return deliveryDocumentDto;
    }

    private String generateFileName(DeliveryUploadDocumentCommand command, String fileName) {
        return command.getDocumentType().name().concat("-").concat(DateUtils.laDateTime()).concat("-")
            .concat(fileName);
    }

    private void validateEntityId(DeliveryUploadDocumentCommand command) {
        if (EntityEnums.DELIVERY_ORDER.equals(command.getEntityName())) {
            DeliveryOrder deliveryOrder = deliveryOrderRepository.findById(command.getEntityId());
            if (null == deliveryOrder) {
                throw new DeliveryBadRequestException("Delivery order not found");
            }
        }
    }

    public void download(String documentName, HttpServletResponse response) throws IOException {
        try {
            byte[] bytes = deliveryDocumentAdaptor.downloadDocument(documentName);
            if (bytes == null || bytes.length == 0) {
                response.setStatus(HttpServletResponse.SC_NOT_FOUND);
                return;
            }
            String mimeType = Files.probeContentType(Path.of(documentName));
            if (mimeType == null) {
                mimeType = "application/octet-stream";
            }
            response.setContentType(mimeType);
            response.setHeader("Access-Control-Expose-Headers", "Content-Disposition");
            response.setHeader("Content-Disposition", "attachment; filename=" + documentName);

            try (OutputStream os = response.getOutputStream()) {
                os.write(bytes);
                os.flush();
            }
        } catch (IOException e) {
            response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
            response.getWriter().write("Error download document");
            log.warn("Error downloading document: {}", documentName, e);
        }
    }

    public void downloadInvoice(String signature, HttpServletResponse response) throws IOException {
        String documentName = verifySignatureAndGetDocumentName(signature);
        log.info("[downloadInvoice] Downloading document: {}", documentName);
        download(documentName, response);
    }

    private String verifySignatureAndGetDocumentName(String signature) throws IOException {
        String decrypt = new CipherUtil(encryptConfig.getCipherKey()).decrypt(new String(getUrlDecoder().decode(signature)));

        Map<String, String> encryptedData = SerializationUtils.readValue(decrypt, new TypeReference<>() {
        });

        String documentName = encryptedData.get("documentName");
        Instant createdAt = encryptedData.get("createdAt") != null ? Instant.parse(encryptedData.get("createdAt")) : null;

        if (createdAt == null || Instant.now().isAfter(createdAt.plus(30, ChronoUnit.DAYS))) {
            throw new DeliveryBusinessException("Download invoice failed, please contact our customer support team");
        }
        return documentName;
    }

    public String getSingedUrl(String documentName) {
        return deliveryDocumentAdaptor.getSignedUrl(documentName);
    }

    public String generateInvoiceSignatureWithExpiration(String fileName) throws IOException {
        Map<String, String> documentNameAndCreateAt = Map.of("documentName", fileName, "createdAt", Instant.now().toString());
        String encrypt = new CipherUtil(encryptConfig.getCipherKey()).encrypt(SerializationUtils.serialize(documentNameAndCreateAt));
        return downloadSignatureUrl.concat(getEncoder().encodeToString(encrypt.getBytes()));
    }

}
