package com.mercaso.wms.delivery.domain.document;

import com.mercaso.wms.delivery.domain.document.enums.DeliveryDocumentType;
import com.mercaso.wms.domain.BaseDomainRepository;
import java.util.List;
import java.util.UUID;

public interface DeliveryDocumentRepository extends BaseDomainRepository<DeliveryDocument, UUID> {

    List<DeliveryDocument> findByEntityIdAndEntityNameAndDocumentTypes(UUID entityId,
        String entityName,
        List<DeliveryDocumentType> deliveryDocumentTypes);

    List<DeliveryDocument> findByEntityIds(List<UUID> entityIds);

} 