package com.mercaso.wms.delivery.infrastructure.repository.document;

import com.mercaso.wms.delivery.domain.document.DeliveryDocument;
import com.mercaso.wms.delivery.domain.document.DeliveryDocumentRepository;
import com.mercaso.wms.delivery.domain.document.enums.DeliveryDocumentType;
import com.mercaso.wms.delivery.infrastructure.repository.document.jpa.DeliveryDocumentJpaDao;
import com.mercaso.wms.delivery.infrastructure.repository.document.jpa.dataobject.DeliveryDocumentDo;
import com.mercaso.wms.delivery.infrastructure.repository.document.jpa.mapper.DeliveryDocumentDoMapper;
import java.util.List;
import java.util.Optional;
import java.util.UUID;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

@Slf4j
@Component
@RequiredArgsConstructor
@Transactional
public class DeliveryDocumentRepositoryImpl implements DeliveryDocumentRepository {

    private final DeliveryDocumentDoMapper mapper;
    private final DeliveryDocumentJpaDao jpaDao;

    @Override
    public DeliveryDocument save(DeliveryDocument domain) {
        return mapper.doToDomain(jpaDao.save(mapper.domainToDo(domain)));
    }

    @Override
    public DeliveryDocument findById(UUID id) {
        Optional<DeliveryDocumentDo> byId = jpaDao.findById(id);
        return byId.map(mapper::doToDomain).orElse(null);
    }

    @Override
    public DeliveryDocument update(DeliveryDocument domain) {
        //do not update the document
        return null;
    }

    @Override
    public List<DeliveryDocument> findByEntityIdAndEntityNameAndDocumentTypes(UUID entityId,
        String entityName,
        List<DeliveryDocumentType> deliveryDocumentTypes) {
        return mapper.doToDomains(jpaDao.findByEntityIdAndEntityNameAndDocumentTypes(entityId,
            entityName,
            deliveryDocumentTypes));
    }

    @Override
    public List<DeliveryDocument> findByEntityIds(List<UUID> entityIds) {
        return mapper.doToDomains(jpaDao.findByEntityIdIn(entityIds));
    }
}