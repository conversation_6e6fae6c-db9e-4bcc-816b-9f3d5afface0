package com.mercaso.wms.delivery.infrastructure.repository.document.jpa;

import com.mercaso.wms.delivery.domain.document.enums.DeliveryDocumentType;
import com.mercaso.wms.delivery.infrastructure.repository.document.jpa.dataobject.DeliveryDocumentDo;
import java.util.List;
import java.util.UUID;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

public interface DeliveryDocumentJpaDao extends JpaRepository<DeliveryDocumentDo, UUID> {

    @Query("SELECT d FROM DeliveryDocumentDo d WHERE d.entityId = :entityId AND d.entityName = :entityName " +
        "AND (:deliveryDocumentTypes IS NULL OR d.documentType in :deliveryDocumentTypes) order by d.createdAt asc")
    List<DeliveryDocumentDo> findByEntityIdAndEntityNameAndDocumentTypes(@Param("entityId") UUID entityId,
        @Param("entityName") String entityName,
        @Param("deliveryDocumentTypes") List<DeliveryDocumentType> deliveryDocumentTypes);

    List<DeliveryDocumentDo> findByEntityIdIn(List<UUID> entityIds);

}