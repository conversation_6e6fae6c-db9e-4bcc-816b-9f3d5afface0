package com.mercaso.wms.delivery.infrastructure.repository.document.jpa.dataobject;

import com.mercaso.wms.delivery.domain.document.enums.DeliveryDocumentType;
import com.mercaso.wms.infrastructure.repository.BaseDo;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.Table;
import java.util.UUID;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.SQLRestriction;

@Entity
@Data
@EqualsAndHashCode(callSuper = false)
@ToString
@Table(name = "da_document")
@SQLDelete(sql = "update da_document set deleted_at = current_timestamp where id = ? and updated_at = ?")
@SQLRestriction("deleted_at is null")
public class DeliveryDocumentDo extends BaseDo {

    @Column(name = "entity_id")
    private UUID entityId;

    @Column(name = "entity_name")
    private String entityName;

    @Column(name = "document_type")
    @Enumerated(EnumType.STRING)
    private DeliveryDocumentType documentType;

    @Column(name = "created_user_name")
    private String createdUserName;

    @Column(name = "file_name")
    private String fileName;

}
