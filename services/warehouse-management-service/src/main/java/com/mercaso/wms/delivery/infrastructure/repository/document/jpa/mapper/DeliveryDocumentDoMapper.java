package com.mercaso.wms.delivery.infrastructure.repository.document.jpa.mapper;

import com.mercaso.wms.delivery.domain.document.DeliveryDocument;
import com.mercaso.wms.delivery.infrastructure.repository.document.jpa.dataobject.DeliveryDocumentDo;
import com.mercaso.wms.infrastructure.repository.BaseDoMapper;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;

@Mapper(componentModel = "spring",
    unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface DeliveryDocumentDoMapper extends BaseDoMapper<DeliveryDocumentDo, DeliveryDocument> {

} 