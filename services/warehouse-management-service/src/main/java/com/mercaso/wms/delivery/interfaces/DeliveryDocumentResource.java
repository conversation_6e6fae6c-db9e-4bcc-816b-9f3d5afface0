package com.mercaso.wms.delivery.interfaces;

import com.fasterxml.jackson.core.type.TypeReference;
import com.mercaso.wms.delivery.application.command.DeliveryUploadDocumentCommand;
import com.mercaso.wms.delivery.application.dto.document.DeliveryDocumentDto;
import com.mercaso.wms.delivery.application.service.DeliveryDocumentApplicationService;
import com.mercaso.wms.delivery.infrastructure.annotation.SingleDeviceLoginCheck;
import com.mercaso.wms.infrastructure.utils.SerializationUtils;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.ArraySchema;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import java.io.IOException;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RequestPart;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;

@Tag(name = "Documents")
@Slf4j
@Validated
@RestController
@RequestMapping("/delivery/documents")
@RequiredArgsConstructor
public class DeliveryDocumentResource {

    private final DeliveryDocumentApplicationService deliveryDocumentApplicationService;

    @SingleDeviceLoginCheck
    @PostMapping
    @PreAuthorize("hasAuthority('da:write:documents')")
    public List<DeliveryDocumentDto> uploadDocuments(@ModelAttribute @Valid DeliveryUploadDocumentCommand command,
        @RequestParam("files") MultipartFile[] files) {
        return deliveryDocumentApplicationService.uploadDocuments(command, files);
    }

    @SingleDeviceLoginCheck
    @PostMapping("/multiple")
    @PreAuthorize("hasAuthority('da:write:documents')")
    public List<DeliveryDocumentDto> uploadDocuments(
        @Parameter(
            description = "Association data in JSON format.",
            required = true,
            array = @ArraySchema(
                schema = @Schema(implementation = DeliveryUploadDocumentCommand.class)
            ),
            example = """
                    [
                        {
                            "entityId": "123e4567-e89b-12d3-a456-426614174000",
                            "entityName": "DELIVERY_ORDER",
                            "documentType": "PRODUCT",
                            "clientFileIds": ["file1", "file2"]
                        },
                        {
                            "entityId": "123e4567-e89b-12d3-a456-426614174001",
                            "entityName": "DELIVERY_ORDER",
                            "documentType": "PAYMENT",
                            "clientFileIds": ["file3"]
                        }
                    ]
                """
        )
        @RequestPart("association") String association,
        MultipartHttpServletRequest request) throws IOException {
        List<DeliveryUploadDocumentCommand> command = SerializationUtils.readValue(association, new TypeReference<>() {
        });
        command.forEach(c -> {
            if (c.getClientFileIds() == null || c.getEntityId() == null || c.getEntityName() == null
                || c.getDocumentType() == null) {
                throw new IllegalArgumentException("Invalid command data");
            }
        });
        Map<String, MultipartFile> filesMap = request.getFileMap().entrySet().stream()
            .filter(entry -> !Objects.equals(entry.getKey(), "association"))
            .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue));
        if (filesMap.isEmpty()) {
            throw new IllegalArgumentException("No files found");
        }
        return deliveryDocumentApplicationService.uploadDocuments(command, filesMap);
    }

    @PreAuthorize("hasAuthority('da:read:documents')")
    @GetMapping("/download/{documentName}")
    public void download(@PathVariable String documentName, HttpServletResponse response) throws IOException {
        deliveryDocumentApplicationService.download(documentName, response);
    }

    @GetMapping("/download")
    public void downloadFile(@RequestParam String signature, HttpServletResponse response) throws IOException {
        deliveryDocumentApplicationService.downloadInvoice(signature, response);
    }

}
