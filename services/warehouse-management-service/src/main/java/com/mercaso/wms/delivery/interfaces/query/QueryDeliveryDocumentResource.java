package com.mercaso.wms.delivery.interfaces.query;

import com.mercaso.wms.delivery.application.dto.document.DeliveryDocumentDto;
import com.mercaso.wms.delivery.application.queryservice.DeliveryDocumentQueryService;
import com.mercaso.wms.delivery.domain.document.enums.DeliveryDocumentType;
import com.mercaso.wms.domain.businessevent.EntityEnums;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import java.util.List;
import java.util.UUID;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@Tag(name = "Documents")
@Slf4j
@Validated
@RestController
@RequestMapping("/delivery/query/documents")
@RequiredArgsConstructor
public class QueryDeliveryDocumentResource {

    private final DeliveryDocumentQueryService deliveryDocumentQueryService;

    @PreAuthorize("hasAuthority('da:read:documents')")
    @GetMapping("/{entityId}/{entityName}")
    public List<DeliveryDocumentDto> findDocuments(@PathVariable @NotNull UUID entityId,
        @PathVariable @NotNull @Valid EntityEnums entityName,
        @RequestParam(required = false) List<DeliveryDocumentType> documentTypes) {
        return deliveryDocumentQueryService.findDocumentsBy(entityId, entityName, documentTypes);
    }

    @PreAuthorize("hasAuthority('da:read:documents')")
    @GetMapping("/{deliveryOrderId}/items")
    public List<DeliveryDocumentDto> findItemDocuments(@PathVariable @NotNull UUID deliveryOrderId) {
        return deliveryDocumentQueryService.findItemDocumentsBy(deliveryOrderId);
    }

}
