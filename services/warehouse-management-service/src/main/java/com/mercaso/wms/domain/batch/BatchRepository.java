package com.mercaso.wms.domain.batch;

import com.mercaso.wms.domain.BaseDomainRepository;
import java.time.LocalDate;
import java.util.List;
import java.util.UUID;

public interface BatchRepository extends BaseDomainRepository<Batch, UUID> {

    List<Batch> findByTag(String tag);

    List<Batch> findUntransferredBatches(String tag);

    List<Batch> findActiveByDeliveryDate(LocalDate deliveryDate);

}
