package com.mercaso.wms.domain.batchitem;

import com.mercaso.wms.domain.BaseDomainRepository;
import java.util.List;
import java.util.UUID;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

public interface BatchItemRepository extends BaseDomainRepository<BatchItem, UUID> {

    List<BatchItem> saveAll(List<BatchItem> batchItemList);

    List<BatchItem> findBatchItemsBy(UUID batchId, String source, List<String> departments, boolean bigOrder);

    List<BatchItem> findBatchItemsByDepartmentNotIn(UUID batchId, String source, List<String> preps);

    List<BatchItem> findBatchItemsBy(UUID batchId, String source);

    List<BatchItem> findUnprocessedBy(UUID batchId, String source);

    List<BatchItem> findBatchItemsBy(UUID batchId, List<String> existingOrderNumbers);

    void deleteAll(List<BatchItem> batchItems);

    Page<BatchItem> searchBatchItems(String source, String deliveryDate, Pageable page);

    List<BatchItem> findByBatchIdAndSourceAndLocationName(UUID batchId, String source, String locationName);

    List<BatchItem> findBatchItemsBy(UUID batchId, String source, boolean bigOrder);

}
