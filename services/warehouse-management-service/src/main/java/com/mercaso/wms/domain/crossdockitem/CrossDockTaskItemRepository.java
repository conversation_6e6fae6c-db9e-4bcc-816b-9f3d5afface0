package com.mercaso.wms.domain.crossdockitem;

import com.mercaso.wms.domain.BaseDomainRepository;
import java.util.List;
import java.util.UUID;

public interface CrossDockTaskItemRepository extends BaseDomainRepository<CrossDockTaskItem, UUID> {

    List<CrossDockTaskItem> findByTaskItemId(UUID taskItemId);

    void deleteByIds(List<UUID> ids);

    void deleteAll();

    void deleteByTaskItemIds(List<UUID> taskItemIds);

    List<CrossDockTaskItem> saveAll(List<CrossDockTaskItem> crossDockTaskItems);

    List<CrossDockTaskItem> findByShippingOrderItemId(UUID shippingOrderItemId);

}