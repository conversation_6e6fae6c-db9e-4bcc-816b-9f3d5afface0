package com.mercaso.wms.domain.pickingtask;


import com.mercaso.security.auth0.utils.SecurityContextUtil;
import com.mercaso.wms.application.command.pickingtask.SplitPickingTaskCommand;
import com.mercaso.wms.application.command.pickingtask.UpdatePickingTaskCommand;
import com.mercaso.wms.application.command.pickingtask.UpdatePickingTaskCommand.UpdatePickingTaskItemDto;
import com.mercaso.wms.batch.enums.SourceEnum;
import com.mercaso.wms.domain.batchitem.BatchItem;
import com.mercaso.wms.domain.location.Location;
import com.mercaso.wms.domain.pickingtask.enums.PickingTaskStatus;
import com.mercaso.wms.domain.pickingtask.enums.PickingTaskTransitionEvents;
import com.mercaso.wms.domain.pickingtask.enums.PickingTaskType;
import com.mercaso.wms.domain.pickingtaskitem.PickingTaskItem;
import com.mercaso.wms.infrastructure.exception.WmsBusinessException;
import com.mercaso.wms.infrastructure.statemachine.BaseStateMachine;
import java.time.Instant;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.UUID;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.stream.Collectors;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.experimental.SuperBuilder;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Configurable;
import org.springframework.util.CollectionUtils;

@Data
@ToString
@SuperBuilder
@EqualsAndHashCode(callSuper = true)
@Configurable(preConstruction = true)
@Slf4j
public class PickingTask extends BaseStateMachine<PickingTask, PickingTaskStatus, PickingTaskTransitionEvents> {

    private final UUID id;

    private UUID batchId;

    private String number;

    private PickingTaskType type;

    private Instant pickingStartTime;

    private Instant pickedTime;

    private UUID pickerUserId;

    private String pickerUserName;

    private SourceEnum source;

    private String createdUserName;

    private Integer totalQty;

    private Integer totalLines;

    private Integer totalPickedQty;

    private List<PickingTaskItem> pickingTaskItems;

    private String department;

    public PickingTask createTask(UUID batchId, SourceEnum source, List<BatchItem> batchItems, Map<UUID, Location> locationMap) {
        this.batchId = batchId;
        this.source = source;
        this.createdUserName = SecurityContextUtil.getUsername();
        this.setState(PickingTaskStatus.CREATED);
        this.pickingTaskItems = convert(batchItems, locationMap);

        setPickingTaskType();
        calculateTotals();
        return this;
    }

    public PickingTask createSplitTask(UUID batchId,
        List<PickingTaskItem> pickingTaskItems,
        SplitPickingTaskCommand splitPickingTaskCommand) {
        this.batchId = batchId;
        this.source = SourceEnum.fromName(splitPickingTaskCommand.getWarehouseName());
        this.type = PickingTaskType.BATCH;
        this.pickerUserId = splitPickingTaskCommand.getPickerUserId();
        this.pickerUserName = splitPickingTaskCommand.getPickerUserName();
        this.createdUserName = SecurityContextUtil.getUsername();
        this.setState(PickingTaskStatus.ASSIGNED);
        pickingTaskItems.sort(Comparator.comparing(PickingTaskItem::getLocationName,
            Comparator.nullsLast(Comparator.naturalOrder())));
        for (int i = 0; i < pickingTaskItems.size(); i++) {
            pickingTaskItems.get(i).setPickingSequence(i + 1);
        }
        this.pickingTaskItems = pickingTaskItems;
        calculateTotals();
        return this;
    }

    private void setPickingTaskType() {
        long count = this.pickingTaskItems.stream().map(PickingTaskItem::getOrderNumber).distinct().count();
        if (count == 1) {
            this.type = PickingTaskType.ORDER;
        } else {
            this.type = PickingTaskType.BATCH;
        }
    }

    public void assignTask(UUID pickerUserId, String pickerUserName) {
        this.pickerUserId = pickerUserId;
        this.pickerUserName = pickerUserName;
        this.processEvent(PickingTaskTransitionEvents.ASSIGN);
    }

    public void reassignTask(UUID userId, String pickerUserName) {
        this.pickerUserId = userId;
        this.pickerUserName = pickerUserName;
        this.processEvent(PickingTaskTransitionEvents.REASSIGN);
    }

    public void unassignTask() {
        this.pickerUserId = null;
        this.pickerUserName = null;
        this.processEvent(PickingTaskTransitionEvents.UNASSIGN);
    }

    public PickingTask update(UpdatePickingTaskCommand updatePickingTaskCommand, Map<UUID, Location> locationMap) {
        return this.update(updatePickingTaskCommand.getUpdatePickingTaskItemDtos(), false, locationMap);
    }

    public PickingTask update(UpdatePickingTaskCommand updatePickingTaskCommand) {
        return this.update(updatePickingTaskCommand.getUpdatePickingTaskItemDtos(), false, null);
    }

    public PickingTask update(boolean splitTask) {
        return this.update(Lists.newArrayList(), splitTask, null);
    }

    private PickingTask update(List<UpdatePickingTaskItemDto> updateDtos, boolean splitTask, Map<UUID, Location> locationMap) {
        AtomicBoolean isPickedQtyChanged = new AtomicBoolean(false);
        updateDtos.forEach(updateDto -> pickingTaskItems.stream()
            .filter(item -> item.getId().equals(updateDto.getId()))
            .forEach(item -> {
                Integer pickedQty = updateDto.getPickedQty() == null ? 0 : updateDto.getPickedQty();
                if (!pickedQty.equals(item.getPickedQty()) || !Objects.equals(item.getErrorInfo(), updateDto.getErrorInfo())) {
                    item.setPickedQty(pickedQty);
                    item.setErrorInfo(item.getExpectQty().equals(pickedQty) ? null : updateDto.getErrorInfo());
                    updateLocation(item, updateDto, locationMap);
                    isPickedQtyChanged.set(true);
                }
            }));

        calculateTotals();
        boolean isErrorInfoChanged = updateDtos.stream()
            .anyMatch(dto -> StringUtils.isNotEmpty(dto.getErrorInfo()));

        if (isPickedQtyChanged.get() || splitTask || isErrorInfoChanged) {
            if (pickingTaskItems.isEmpty()) {
                cancelTask();
            } else if (isSplitTask(splitTask)) {
                completePicking();
            } else if (isTaskHasError(pickingTaskItems)) {
                failTask();
            } else if (allItemsPicked(pickingTaskItems)) {
                picked();
            } else if (isTaskStillPicking(pickingTaskItems)) {
                startPicking();
            }
        }
        return this;
    }

    public void deleteItemsByIds(List<UUID> pickingTaskItemIds) {
        if (CollectionUtils.isEmpty(pickingTaskItemIds)) {
            return;
        }

        this.pickingTaskItems.removeIf(pickingTaskItem -> pickingTaskItemIds.contains(pickingTaskItem.getId()));
        calculateTotals();

        if (pickingTaskItems.isEmpty()) {
            cancelTask();
        } else if (isTaskHasError(pickingTaskItems)) {
            failTask();
        } else if (allItemsPicked(pickingTaskItems)) {
            picked();
        } else if (isTaskStillPicking(pickingTaskItems)) {
            startPicking();
        }
    }

    private boolean isSplitTask(boolean splitTask) {
        return allItemsPicked(pickingTaskItems) && splitTask;
    }

    private boolean allItemsPicked(List<PickingTaskItem> pickingTaskItems) {
        return pickingTaskItems.stream().allMatch(pickingTaskItem -> Objects.equals(pickingTaskItem.getPickedQty(),
            pickingTaskItem.getExpectQty()));
    }

    private boolean isTaskHasError(List<PickingTaskItem> pickingTaskItems) {
        return pickingTaskItems.stream().anyMatch(pickingTaskItem -> StringUtils.isNotEmpty(pickingTaskItem.getErrorInfo()));
    }

    private boolean isTaskStillPicking(List<PickingTaskItem> pickingTaskItems) {
        return pickingTaskItems.stream()
            .anyMatch(pickingTaskItem -> pickingTaskItem.getPickedQty() < pickingTaskItem.getExpectQty()
                || StringUtils.isNotEmpty(pickingTaskItem.getErrorInfo()));
    }

    public void startPicking() {
        if (PickingTaskStatus.PICKING.equals(this.getState()) || PickingTaskStatus.CREATED.equals(this.getState())) {
            return;
        }
        this.processEvent(PickingTaskTransitionEvents.PICKING);
        if (this.pickingStartTime == null) {
            this.pickingStartTime = Instant.now();
        }
    }

    public void picked() {
        if (PickingTaskStatus.PICKED.equals(this.getState()) || PickingTaskStatus.COMPLETED.equals(this.getState())) {
            return;
        }
        this.processEvent(PickingTaskTransitionEvents.PICK_FINISH);
        this.pickedTime = Instant.now();
    }

    public void completePicking() {
        if (PickingTaskStatus.COMPLETED.equals(this.getState())) {
            return;
        }
        this.processEvent(PickingTaskTransitionEvents.COMPLETE);
    }

    public void partiallyCompletePicking() {
        if (!isItemAllPicked()) {
            throw new WmsBusinessException("All picking task items must be picked to complete the task.");
        }
        if (PickingTaskStatus.PARTIALLY_COMPLETED.equals(this.getState())) {
            return;
        }
        this.processEvent(PickingTaskTransitionEvents.PARTIALLY_COMPLETE);
    }

    public void removePickingTaskItems(List<PickingTaskItem> pickingTaskItems) {
        this.pickingTaskItems.removeAll(pickingTaskItems);
        calculateTotals();
    }

    public void updatePickingTaskItems(List<PickingTaskItem> pickingTaskItems) {
        pickingTaskItems.forEach(updatePickingTaskItem -> {
            PickingTaskItem pickingTaskItem = this.pickingTaskItems.stream()
                .filter(item -> item.getId().equals(updatePickingTaskItem.getId()))
                .findFirst()
                .orElseThrow(() -> new WmsBusinessException("Picking task item not found."));
            pickingTaskItem.setPickedQty(updatePickingTaskItem.getPickedQty());
            pickingTaskItem.setErrorInfo(updatePickingTaskItem.getErrorInfo());
            pickingTaskItem.setExpectQty(updatePickingTaskItem.getExpectQty());
        });
        calculateTotals();
    }

    private boolean isItemAllPicked() {
        return this.getPickingTaskItems()
            .stream()
            .filter(pickingTaskItem -> StringUtils.isEmpty(pickingTaskItem.getErrorInfo()))
            .allMatch(pickingTaskItem -> pickingTaskItem.getExpectQty().equals(pickingTaskItem.getPickedQty()));
    }

    public void cancelTask() {
        if (PickingTaskStatus.CANCELED.equals(this.getState())) {
            return;
        }
        this.processEvent(PickingTaskTransitionEvents.CANCEL);
    }

    public void failTask() {
        this.pickedTime = Instant.now();
        if (PickingTaskStatus.FAILED.equals(this.getState())) {
            return;
        }
        this.processEvent(PickingTaskTransitionEvents.FAIL);
    }

    public void calculateTotals() {
        if (this.pickingTaskItems == null || this.pickingTaskItems.isEmpty()) {
            this.totalQty = 0;
            this.totalLines = 0;
            this.totalPickedQty = 0;
            this.department = null;
            return;
        }

        this.totalQty = this.pickingTaskItems.stream()
            .mapToInt(item -> item.getExpectQty() != null ? item.getExpectQty() : 0)
            .sum();

        this.totalLines = this.pickingTaskItems.size();

        this.totalPickedQty = this.pickingTaskItems.stream()
            .mapToInt(item -> item.getPickedQty() != null ? item.getPickedQty() : 0)
            .sum();

        // Aggregate departments from items: distinct, alphabetically sorted, joined by comma
        this.department = this.pickingTaskItems.stream()
            .map(PickingTaskItem::getDepartment)
            .filter(StringUtils::isNotBlank)
            .map(String::trim)
            .distinct()
            .sorted(String.CASE_INSENSITIVE_ORDER)
            .collect(Collectors.joining(","));
    }

    private static List<PickingTaskItem> convert(List<BatchItem> batchItems, Map<UUID, Location> locationMap) {
        List<PickingTaskItem> list = Lists.newArrayList();
        for (int i = 0; i < batchItems.size(); i++) {
            BatchItem batchItem = batchItems.get(i);
            list.add(PickingTaskItem.builder()
                .batchItemId(batchItem.getId())
                .orderNumber(batchItem.getOrderNumber())
                .line(batchItem.getLine())
                .itemId(batchItem.getItemId())
                .department(batchItem.getDepartment())
                .category(batchItem.getCategory())
                .skuNumber(batchItem.getSkuNumber())
                .title(batchItem.getTitle())
                .locationName(batchItem.getLocationName())
                .locationId(batchItem.getLocationId())
                .aisleNumber(locationMap.get(batchItem.getLocationId()) == null ? batchItem.getLocationName()
                    : locationMap.get(batchItem.getLocationId()).getAisleNumber())
                .pickingSequence(i + 1)
                .prep(batchItem.getPrep())
                .expectQty(batchItem.getExpectQty())
                .pickedQty(0)
                .breakdownName(batchItem.getBreakdownName())
                .shippingOrderId(batchItem.getShippingOrderId())
                .shippingOrderItemId(batchItem.getShippingOrderItemId())
                .build());
        }
        return list;
    }

    private void updateLocation(PickingTaskItem pickingTaskItem,
        UpdatePickingTaskItemDto updatePickingTaskItemDto,
        Map<UUID, Location> locationMap) {
        if (CollectionUtils.isEmpty(locationMap)) {
            return;
        }

        UUID newLocationId = updatePickingTaskItemDto.getLocationId();
        UUID currentLocationId = pickingTaskItem.getLocationId();

        if (currentLocationId == null || !Objects.equals(currentLocationId, newLocationId)) {
            log.info("[updateLocation] Picking task: {} item location changed from {} to {}",
                this.number, currentLocationId, newLocationId);
            if (newLocationId != null) {
                Location location = locationMap.get(newLocationId);
                if (location == null) {
                    log.warn("[updateLocation] change location:{} location id not found", newLocationId);
                    return;
                }

                pickingTaskItem.setLocationId(location.getId());
                pickingTaskItem.setLocationName(location.getName());
            }
        }
    }
}
