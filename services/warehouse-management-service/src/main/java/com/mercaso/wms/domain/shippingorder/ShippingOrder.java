package com.mercaso.wms.domain.shippingorder;


import com.mercaso.wms.application.command.shippingorder.ShippingOrderValidateCommand;
import com.mercaso.wms.application.command.shippingorder.UpdateShippingOrderItemCommand;
import com.mercaso.wms.application.dto.scanrecord.OutboundScanRecordDto;
import com.mercaso.wms.application.dto.shopify.ShopifyOrderDto;
import com.mercaso.wms.application.dto.shopify.ShopifyOrderDto.ShippingAddressDto;
import com.mercaso.wms.application.dto.shopify.ShopifyOrderDto.ShopifyLineItemDto;
import com.mercaso.wms.delivery.application.dto.deliveryorder.DeliveryOrderRouteInfoDto;
import com.mercaso.wms.domain.customeraddress.CustomerAddress;
import com.mercaso.wms.domain.location.Location;
import com.mercaso.wms.domain.pickingtaskitem.PickingTaskItem;
import com.mercaso.wms.domain.receivingtaskitem.ReceivingTaskItem;
import com.mercaso.wms.domain.shippingorder.enums.ShippingOrderStatus;
import com.mercaso.wms.domain.shippingorder.enums.ShippingOrderTransitionEvents;
import com.mercaso.wms.domain.shippingorderitem.ShippingOrderItem;
import com.mercaso.wms.domain.warehouse.Warehouse;
import com.mercaso.wms.infrastructure.statemachine.BaseStateMachine;
import java.time.Instant;
import java.time.LocalDate;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.UUID;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.experimental.SuperBuilder;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Configurable;

@Slf4j
@Data
@ToString
@SuperBuilder
@EqualsAndHashCode(callSuper = true)
@Configurable(preConstruction = true)
public class ShippingOrder extends BaseStateMachine<ShippingOrder, ShippingOrderStatus, ShippingOrderTransitionEvents> {

    private final UUID id;

    private Warehouse warehouse;

    private UUID batchId;

    private CustomerAddress customerAddress;

    private String shopifyOrderId;

    private String orderNumber;

    private String fulfillmentStatus;

    private Instant orderDate;

    private LocalDate deliveryDate;

    private Instant shippedDate;

    private Location breakdownLocation;

    private Integer palletCount;

    private List<ShippingOrderItem> shippingOrderItems;

    private String truckNumber;

    private String driverUserName;

    private UUID driverUserId;

    public void bindRouteInfo(DeliveryOrderRouteInfoDto routeInfoDto) {
        if (routeInfoDto == null) {
            return;
        }
        this.setTruckNumber(routeInfoDto.getTruckNumber());
        this.setDriverUserName(routeInfoDto.getDriverUserName());
        this.setDriverUserId(routeInfoDto.getDriverUserId());
    }

    public void updateDeliveredItems(ShippingOrderValidateCommand command) {
        updateItems(command.getUpdateShippingOrderItemCommands());
        if (command.getPalletCount() != null) {
            this.palletCount = command.getPalletCount();
        }
    }

    private void updateItems(List<UpdateShippingOrderItemCommand> command) {
        if (CollectionUtils.isNotEmpty(command)) {
            shippingOrderItems.forEach(deliveryOrderItem -> command
                .forEach(updateShippingOrderItem -> {
                    if (deliveryOrderItem.getId().equals(updateShippingOrderItem.getId())) {
                        deliveryOrderItem.setValidatedQty(updateShippingOrderItem.getValidatedQty());
                        deliveryOrderItem.setReasonCode(updateShippingOrderItem.getReasonCode());
                    }
                }));
            validated();
        }
    }

    public void updateHighValueDeliveredItems(List<UpdateShippingOrderItemCommand> updateShippingOrderItemCommands) {
        updateItems(updateShippingOrderItemCommands);
    }

    public ShippingOrder create(ShopifyOrderDto shopifyOrderDto) {
        this.customerAddress = convertAddress(shopifyOrderDto);
        this.shopifyOrderId = shopifyOrderDto.getId();
        this.orderNumber = shopifyOrderDto.getName();
        this.orderDate = shopifyOrderDto.getUpdatedAt();
        this.fulfillmentStatus = shopifyOrderDto.getFulfillmentStatus();
        this.deliveryDate = convertDeliveryDate(shopifyOrderDto.getTags());
        if (shopifyOrderDto.getCancelledAt() != null) {
            this.setState(ShippingOrderStatus.CANCELED);
        } else {
            this.setState(ShippingOrderStatus.OPEN);
        }
        this.shippingOrderItems = convertShippingOrderItem(shopifyOrderDto);
        return this;
    }

    public ShippingOrder update(ShopifyOrderDto shopifyOrderDto) {
        if (shopifyOrderDto == null) {
            throw new IllegalArgumentException("ShopifyOrderDto cannot be null");
        }

        CustomerAddress address = convertAddress(shopifyOrderDto);
        if (this.customerAddress == null || address == null || !this.customerAddress.getSha1hex().equals(address.getSha1hex())) {
            this.customerAddress = address;
        }

        LocalDate newDeliveryDate = convertDeliveryDate(shopifyOrderDto.getTags());
        if (!Objects.equals(newDeliveryDate, this.deliveryDate)) {
            this.deliveryDate = newDeliveryDate;
        }

        if (shopifyOrderDto.getCancelledAt() != null) {
            this.setState(ShippingOrderStatus.CANCELED);
        }

        this.fulfillmentStatus = shopifyOrderDto.getFulfillmentStatus();

        List<ShippingOrderItem> newItems = convertShippingOrderItem(shopifyOrderDto);

        Map<String, ShippingOrderItem> newItemMap = newItems.stream()
            .collect(Collectors.toMap(ShippingOrderItem::getShopifyOrderItemId, item -> item));

        for (ShippingOrderItem existingItem : this.shippingOrderItems) {
            ShippingOrderItem updatedItem = newItemMap.get(existingItem.getShopifyOrderItemId());

            if (updatedItem != null) {
                existingItem.setQty(updatedItem.getQty());
                existingItem.setOriginalQty(updatedItem.getOriginalQty());
                existingItem.setLine(updatedItem.getLine());
                existingItem.setSkuNumber(updatedItem.getSkuNumber());
                if (!StringUtils.isEmpty(updatedItem.getTitle())) {
                    existingItem.setTitle(updatedItem.getTitle());
                } else {
                    log.warn("No title found for shopify order item {}", existingItem.getShopifyOrderItemId());
                }
                newItemMap.remove(existingItem.getShopifyOrderItemId());
            } else {
                existingItem.setDeletedAt(Instant.now());
                existingItem.setDeletedBy("Shopify");
            }
        }

        this.shippingOrderItems.addAll(newItemMap.values());
        return this;
    }

    public LocalDate convertDeliveryDate(String tags) {
        if (tags == null) {
            return null;
        }
        String[] tagArray = tags.split(",");
        String regex = "\\d{4}-\\d{2}-\\d{2}";
        Pattern pattern = Pattern.compile(regex);

        for (String tag : tagArray) {
            Matcher matcher = pattern.matcher(tag);
            if (matcher.find()) {
                return LocalDate.parse(matcher.group());
            }
        }
        return null;
    }

    public boolean isMfcOrder(String tags) {
        if (tags == null) {
            return false;
        }
        String[] tagArray = tags.split(",");
        for (String tag : tagArray) {
            if (tag.contains("SELLER_Mercaso")) {
                return true;
            }
        }
        return false;
    }

    public void picked(OutboundScanRecordDto dto) {
        for (ShippingOrderItem shippingOrderItem : shippingOrderItems) {
            if (shippingOrderItem.getId().equals(dto.getShippingOrderItemId())) {
                updateShippingOrderItem(shippingOrderItem, dto.getQty());
                break;
            }
        }
        updatePickedIfNeeded();
    }

    private void validated() {
        if (ShippingOrderStatus.CANCELED.equals(this.getState())) {
            log.info("Shipping order {} is canceled, skip update validated", this.getOrderNumber());
            return;
        }
        if (ShippingOrderStatus.VALIDATED.equals(this.getState())) {
            log.info("Shipping order {} is already validated, skip update", this.getOrderNumber());
            return;
        }
        this.processEvent(ShippingOrderTransitionEvents.VALIDATE);
    }

    private void updatePickedIfNeeded() {
        if (ShippingOrderStatus.CANCELED.equals(this.getState())) {
            log.info("Shipping order {} is canceled, skip update picked", this.getOrderNumber());
            return;
        }
        if (shippingOrderItems.stream()
            .filter(shippingOrderItem -> shippingOrderItem.getQty() > 0)
            .allMatch(ShippingOrderItem::isPicked)
            && !ShippingOrderStatus.PICKED.equals(this.getState())) {
            this.processEvent(ShippingOrderTransitionEvents.PICK_FINISH);
        }
    }

    private static void updateShippingOrderItem(ShippingOrderItem shippingOrderItem, Integer updateQty) {
        shippingOrderItem.setPicked(true);
        if ((shippingOrderItem.getPickedQty() == null && updateQty > shippingOrderItem.getQty())
            || (shippingOrderItem.getPickedQty() != null
            && shippingOrderItem.getPickedQty() + updateQty > shippingOrderItem.getQty())) {
            shippingOrderItem.setPickedQty(shippingOrderItem.getQty());
            log.warn("Picked qty is greater than order qty, set picked qty to order qty for shipping order item: {}",
                shippingOrderItem.getId());
        } else {
            shippingOrderItem.setPickedQty(shippingOrderItem.getPickedQty() != null ? shippingOrderItem.getPickedQty()
                + updateQty
                : updateQty);
        }
    }

    public void picked(List<PickingTaskItem> pickingTaskItems) {
        for (ShippingOrderItem shippingOrderItem : shippingOrderItems) {
            for (PickingTaskItem pickingTaskItem : pickingTaskItems) {
                if (shippingOrderItem.getId().equals(pickingTaskItem.getShippingOrderItemId())) {
                    updateShippingOrderItem(shippingOrderItem, pickingTaskItem.getPickedQty());
                    break;
                }
            }
        }

        updatePickedIfNeeded();
    }

    public void received(List<ReceivingTaskItem> receivingTaskItems) {
        for (ShippingOrderItem shippingOrderItem : shippingOrderItems) {
            for (ReceivingTaskItem receivingTaskItem : receivingTaskItems) {
                if (shippingOrderItem.getId().equals(receivingTaskItem.getShippingOrderItemId())) {
                    updateShippingOrderItem(shippingOrderItem, receivingTaskItem.getReceivedQty());
                    break;
                }
            }
        }
        updatePickedIfNeeded();
    }

    public void addToBatch() {
        this.processEvent(ShippingOrderTransitionEvents.ADD_TO_BATCH);
    }

    public List<ShippingOrderItem> convertShippingOrderItem(ShopifyOrderDto shopifyOrderDto) {
        List<ShopifyLineItemDto> lineItems = shopifyOrderDto.getLineItems();
        if (CollectionUtils.isEmpty(lineItems)) {
            return Collections.emptyList();
        }
        List<ShippingOrderItem> items = Lists.newArrayList();
        for (int i = 0; i < lineItems.size(); i++) {
            ShopifyLineItemDto shopifyLineItemDto = lineItems.get(i);
            items.add(ShippingOrderItem.builder()
                .skuNumber(shopifyLineItemDto.getSku())
                .qty(shopifyLineItemDto.getCurrentQuantity())
                .originalQty(shopifyLineItemDto.getQuantity())
                .title(shopifyLineItemDto.getTitle())
                .shopifyOrderItemId(shopifyLineItemDto.getId())
                .line(i + 1)
                .build());
        }
        return items;
    }

    public CustomerAddress convertAddress(ShopifyOrderDto shopifyOrderDto) {
        ShippingAddressDto shippingAddress = shopifyOrderDto.getShippingAddress();
        if (shippingAddress == null) {
            return null;
        }
        return CustomerAddress.builder()
            .addressOne(shippingAddress.getAddress1())
            .addressTwo(shippingAddress.getAddress2())
            .city(shippingAddress.getCity())
            .state(shippingAddress.getProvince())
            .country(shippingAddress.getCountry())
            .name(shippingAddress.getName())
            .firstName(shippingAddress.getFirstName())
            .lastName(shippingAddress.getLastName())
            .latitude(shippingAddress.getLatitude())
            .longitude(shippingAddress.getLongitude())
            .postalCode(shippingAddress.getZip())
            .phone(shippingAddress.getPhone())
            .build();
    }

    public void updateFulfilledQty(UUID shippingOrderItemId, Integer additionalFulfilledQty) {
        if (additionalFulfilledQty == null || additionalFulfilledQty <= 0) {
            return;
        }
        if (!CollectionUtils.isEmpty(shippingOrderItems)) {
            ShippingOrderItem item = shippingOrderItems.stream()
                .filter(orderItem -> orderItem.getId().equals(shippingOrderItemId))
                .findFirst()
                .orElse(null);
            if (item == null) {
                log.warn("[updateFulfilledQty] Shipping order item with ID {} not found in shipping order {}",
                    shippingOrderItemId, this.id);
                return;
            }
            if (item.getFulfilledQty() == null) {
                item.setFulfilledQty(additionalFulfilledQty);
            } else {
                item.setFulfilledQty(item.getFulfilledQty() + additionalFulfilledQty);
            }

            if (item.getQty() != null && item.getFulfilledQty() > item.getQty()) {
                log.warn("[updateFulfilledQty] Fulfilled quantity {} exceeds total quantity {} for item {}",
                    item.getFulfilledQty(), item.getQty(), this.id);
                item.setFulfilledQty(item.getQty());
            }
        }

    }

}
