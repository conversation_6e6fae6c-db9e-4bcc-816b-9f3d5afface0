package com.mercaso.wms.domain.shippingorder;

import com.mercaso.wms.batch.dto.SkuCountByDeliveryDate;
import com.mercaso.wms.domain.BaseDomainRepository;
import com.mercaso.wms.infrastructure.repository.shippingorder.criteria.ShippingOrderSearchCriteria;
import java.util.List;
import java.util.UUID;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

public interface ShippingOrderRepository extends BaseDomainRepository<ShippingOrder, UUID> {

    ShippingOrder findByNumber(String number);

    List<ShippingOrder> findByOrderNumbers(List<String> orderNumbers);

    List<ShippingOrder> findByOrderIds(List<UUID> ids);

    Page<ShippingOrder> findShippingOrderList(ShippingOrderSearchCriteria criteria, Pageable pageable);

    void deleteAll();

    List<ShippingOrder> saveAll(List<ShippingOrder> shippingOrders);

    List<ShippingOrder> findActiveShippingOrdersByDeliveryDate(String deliveryDate);

    List<ShippingOrder> findByBatchId(UUID batchId);

    List<SkuCountByDeliveryDate> skuCountByDeliveryDate(String deliveryDate);

    ShippingOrder findByOrderNumberAndShopifyOrderId(String orderNumber, String shopifyOrderId);

    List<ShippingOrder> findRescheduledShippingOrders(String deliveryDate);

    List<ShippingOrder> findByDeliveryDate(String deliveryDate);

    List<ShippingOrder> updateAll(List<ShippingOrder> shippingOrders);

    List<ShippingOrder> findByDeliveryDateAndDriverUserId(String deliveryDate, UUID driverUserId);

}
