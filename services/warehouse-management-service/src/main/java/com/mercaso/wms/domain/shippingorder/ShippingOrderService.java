package com.mercaso.wms.domain.shippingorder;

import com.mercaso.businessevents.dispatcher.BusinessEventDispatcher;
import com.mercaso.businessevents.dto.DispatchResponseDto;
import com.mercaso.wms.application.dto.event.ShippingOrderCanceledPayloadDto;
import com.mercaso.wms.application.dto.event.ShippingOrderCreatedPayloadDto;
import com.mercaso.wms.application.dto.event.ShippingOrderPickedPayloadDto;
import com.mercaso.wms.application.dto.event.ShippingOrderUpdatedPayloadDto;
import com.mercaso.wms.application.dto.event.ShippingOrderValidatedPayloadDto;
import com.mercaso.wms.application.dto.shippingorder.ShippingOrderDto;
import com.mercaso.wms.application.mapper.shippingorder.ShippingOrderDtoApplicationMapper;
import com.mercaso.wms.domain.customeraddress.CustomerAddress;
import com.mercaso.wms.domain.customeraddress.CustomerAddressRepository;
import com.mercaso.wms.domain.pickingtask.enums.PickingTaskType;
import com.mercaso.wms.domain.pickingtaskitem.PickingTaskItem;
import com.mercaso.wms.domain.shippingorder.enums.ShippingOrderStatus;
import com.mercaso.wms.infrastructure.config.PgAdvisoryLock;
import com.mercaso.wms.infrastructure.event.BusinessEventFactory;
import com.mercaso.wms.infrastructure.event.applicationevent.dispatcher.ApplicationEventDispatcher;
import com.mercaso.wms.infrastructure.repository.districs.CongressionalDistrictsDo;
import com.mercaso.wms.infrastructure.repository.districs.CongressionalDistrictsRepository;
import com.mercaso.wms.infrastructure.retryabletransaction.RetryableTransaction;
import java.util.List;
import java.util.UUID;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

@Slf4j
@Service
@RequiredArgsConstructor
public class ShippingOrderService {

    private final ShippingOrderRepository shippingOrderRepository;

    private final PgAdvisoryLock pgAdvisoryLock;

    private final BusinessEventDispatcher businessEventDispatcher;

    private final ShippingOrderDtoApplicationMapper shippingOrderDtoApplicationMapper;

    private final CustomerAddressRepository customerAddressRepository;

    private final CongressionalDistrictsRepository congressionalDistrictsRepository;

    private final ApplicationEventDispatcher applicationEventDispatcher;


    @RetryableTransaction(propagation = Propagation.REQUIRES_NEW)
    public void updateSingleOrderWithRetry(UUID orderId,
        List<PickingTaskItem> pickedPickingTaskItems,
        UUID pickingTaskId,
        PickingTaskType type) {
        pgAdvisoryLock.tryAcquireTransactionalLevelAdvisoryLock(
            orderId.hashCode(), "ShippingOrderApplicationService.updateOrderStatusByBatchLevelPickingTask");
        ShippingOrder latestOrder = shippingOrderRepository.findById(orderId);
        if (latestOrder == null) {
            log.error("[updateSingleOrderWithRetry] Order not found: {}", orderId);
            return;
        }
        if (latestOrder.getStatus() == ShippingOrderStatus.PICKED) {
            log.info("[updateSingleOrderWithRetry] Order already picked: {}, picking task: {}",
                latestOrder.getOrderNumber(), pickingTaskId);
            return;
        }

        latestOrder.picked(pickedPickingTaskItems);
        if (type == PickingTaskType.ORDER) {
            pickedPickingTaskItems.forEach(item -> latestOrder.updateFulfilledQty(item.getShippingOrderItemId(),
                item.getPickedQty()));
        }
        ShippingOrder savedOrder = shippingOrderRepository.update(latestOrder);
        log.info("[updateSingleOrderWithRetry] Order status updated: {}, status: {}, picking task: {}",
            savedOrder.getOrderNumber(), savedOrder.getStatus(), pickingTaskId);

        ShippingOrderDto orderDto = shippingOrderDtoApplicationMapper.domainToDto(savedOrder);
        saveBusinessEvent(orderDto, false, false);
    }

    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public void setCustomerAddress(ShippingOrder shippingOrder) {
        CustomerAddress address = shippingOrder.getCustomerAddress();
        if (address == null) {
            return;
        }
        pgAdvisoryLock.tryAcquireTransactionalLevelAdvisoryLock(
            address.sha1hex().hashCode(),
            "ShippingOrderApplicationService.setCustomerAddress"
        );
        CustomerAddress existingAddress = customerAddressRepository.findBySha1hex(address.sha1hex());
        if (existingAddress != null) {
            shippingOrder.setCustomerAddress(existingAddress);
            return;
        }
        CongressionalDistrictsDo districtsDo = congressionalDistrictsRepository.findByZipCode(address.getPostalCode());
        if (districtsDo != null) {
            address.setDistrict(districtsDo.getDistrictName());
        }
        address.setSha1hex(address.sha1hex());

        try {
            CustomerAddress savedAddress = customerAddressRepository.save(address);
            shippingOrder.setCustomerAddress(savedAddress);
        } catch (Exception e) {
            CustomerAddress concurrentAddress = customerAddressRepository.findBySha1hex(address.sha1hex());
            if (concurrentAddress != null) {
                shippingOrder.setCustomerAddress(concurrentAddress);
                log.info("[setCustomerAddress] Concurrent insert detected for order: {}, using existing address",
                    shippingOrder.getOrderNumber());
                return;
            }
            log.error("[setCustomerAddress] Failed to save address for order: {}", shippingOrder.getOrderNumber(), e);
        }
    }

    public void saveBusinessEvent(ShippingOrderDto shippingOrderDto, boolean firstCreate, boolean sendApplicationEvent) {
        DispatchResponseDto responseDto = null;
        if (ShippingOrderStatus.OPEN == shippingOrderDto.getStatus()) {
            if (firstCreate) {
                businessEventDispatcher.dispatch(BusinessEventFactory.build(ShippingOrderCreatedPayloadDto.builder()
                    .shippingOrderId(shippingOrderDto.getId())
                    .data(shippingOrderDto)
                    .build()));
            } else {
                businessEventDispatcher.dispatch(BusinessEventFactory.build(ShippingOrderUpdatedPayloadDto.builder()
                    .shippingOrderId(shippingOrderDto.getId())
                    .data(shippingOrderDto)
                    .build()));
            }
        } else if (ShippingOrderStatus.CANCELED == shippingOrderDto.getStatus()) {
            businessEventDispatcher.dispatch(BusinessEventFactory.build(ShippingOrderCanceledPayloadDto.builder()
                .shippingOrderId(shippingOrderDto.getId())
                .data(shippingOrderDto)
                .build()));
        } else if (ShippingOrderStatus.PICKED == shippingOrderDto.getStatus()) {
            businessEventDispatcher.dispatch(BusinessEventFactory.build(ShippingOrderPickedPayloadDto.builder()
                .shippingOrderId(shippingOrderDto.getId())
                .data(shippingOrderDto)
                .build()));
        } else if (ShippingOrderStatus.VALIDATED == shippingOrderDto.getStatus()) {
            responseDto = businessEventDispatcher.dispatch(BusinessEventFactory.build(
                ShippingOrderValidatedPayloadDto.builder()
                    .shippingOrderId(shippingOrderDto.getId())
                    .data(shippingOrderDto)
                    .build()));
        }
        if (sendApplicationEvent && responseDto != null) {
            applicationEventDispatcher.publishEvent(responseDto.getEvent());
        }
    }

    public void updateFulfilledQty(UUID shippingOrderId, UUID shippingOrderItemId, Integer additionalFulfilledQty) {
        if (shippingOrderId == null || shippingOrderItemId == null || additionalFulfilledQty == null
            || additionalFulfilledQty <= 0) {
            log.warn(
                "[updateFulfilledQty] Invalid parameters: shippingOrderId={}, shippingOrderItemId={}, additionalFulfilledQty={}",
                shippingOrderId,
                shippingOrderItemId,
                additionalFulfilledQty);
            return;
        }

        ShippingOrder shippingOrder = shippingOrderRepository.findById(shippingOrderId);
        if (shippingOrder == null) {
            log.warn("[updateFulfilledQty] Shipping order not found: {}", shippingOrderId);
            return;
        }
        shippingOrder.updateFulfilledQty(shippingOrderItemId, additionalFulfilledQty);
        shippingOrderRepository.save(shippingOrder);
    }

}
