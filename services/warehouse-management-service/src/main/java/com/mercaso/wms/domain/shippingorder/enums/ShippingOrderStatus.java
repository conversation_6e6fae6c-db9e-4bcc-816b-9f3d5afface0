package com.mercaso.wms.domain.shippingorder.enums;

import com.mercaso.wms.infrastructure.statemachine.StateType;
import java.util.List;

public enum ShippingOrderStatus implements StateType {
    OPEN,
    IN_PROGRESS,
    PICKED,
    VALIDATED,
    PACKED,
    SHIPPED,
    CANCELED;

    public static final List<ShippingOrderStatus> CAN_BE_VALIDATE_STATUSES = List.of(
        IN_PROGRESS, PICKED, VALIDATED
    );
}
