package com.mercaso.wms.domain.shippingorderitem;


import com.mercaso.wms.domain.BaseDomain;
import java.util.Objects;
import java.util.UUID;
import lombok.Data;
import lombok.ToString;
import lombok.experimental.SuperBuilder;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Configurable;

@Data
@ToString
@SuperBuilder
@Slf4j
@Configurable(preConstruction = true)
public class ShippingOrderItem extends BaseDomain {

    private final UUID id;

    private UUID shippingOrderId;

    private String shopifyOrderItemId;

    private UUID itemId;

    private String skuNumber;

    private String title;

    private Integer originalQty;

    private Integer qty;

    private Integer fulfilledQty;

    private Integer pickedQty;

    private Integer validatedQty;

    private String reasonCode;

    private Integer line;

    private String department;

    private String category;

    private String subCategory;

    private boolean picked;

    private UUID primaryVendorId;

    private UUID backupVendorId;

    private String primaryVendorName;

    private String backupVendorName;

    private Integer versionNumber;

    private boolean highValueItem;

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }

        ShippingOrderItem that = (ShippingOrderItem) o;

        return Objects.equals(shopifyOrderItemId, that.shopifyOrderItemId) &&
            Objects.equals(skuNumber, that.skuNumber);
    }

    @Override
    public int hashCode() {
        return Objects.hash(shopifyOrderItemId, skuNumber);
    }

}
