package com.mercaso.wms.infrastructure.config;

import com.mercaso.wms.infrastructure.external.finale.config.FinaleConfigProperties;
import java.nio.charset.StandardCharsets;
import java.util.Base64;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.MDC;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.MediaType;
import org.springframework.util.StringUtils;
import org.springframework.util.unit.DataSize;
import org.springframework.web.reactive.function.client.ExchangeFilterFunction;
import org.springframework.web.reactive.function.client.ExchangeStrategies;
import org.springframework.web.reactive.function.client.WebClient;

@Slf4j
@Configuration
@RequiredArgsConstructor
public class FinaleWebClientConfig {

    private final FinaleConfigProperties finaleConfigProperties;

    private static final String HEADER_CONTENT_TYPE = "Content-Type";
    private static final String HEADER_AUTHORIZATION = "Authorization";
    private static final String HEADER_REQUEST_ID = "X-Request-Id";
    private static final String HEADER_CORRELATION_ID = "X-Correlation-Id";

    @Bean(name = "finaleWebClient")
    public WebClient finaleWebClient() {
        String authHeader = toAuthHeader(finaleConfigProperties.getAuthScheme(), finaleConfigProperties.getBasicToken());

        ExchangeFilterFunction mdcPropagator = (request, next) -> {
            var requestId = MDC.get("requestId");
            var correlationId = MDC.get("correlationId");
            if (StringUtils.hasText(requestId)) {
                request.headers().add(HEADER_REQUEST_ID, requestId);
            }
            if (StringUtils.hasText(correlationId)) {
                request.headers().add(HEADER_CORRELATION_ID, correlationId);
            }

            request.headers().add(HEADER_AUTHORIZATION, authHeader);
            return next.exchange(request);
        };

        final int maxBytes = computeMaxInMemoryBytes(finaleConfigProperties.getMaxInMemorySize());

        return WebClient.builder()
            .baseUrl(finaleConfigProperties.getBaseUrl())
            .defaultHeaders(h -> h.add(HEADER_CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE))
            .filter(mdcPropagator)
            .exchangeStrategies(ExchangeStrategies.builder()
                .codecs(c -> c.defaultCodecs().maxInMemorySize(maxBytes))
                .build())
            .build();
    }

    private String toAuthHeader(String scheme, String rawTokenOrCredentials) {
        String token = rawTokenOrCredentials.contains(":")
            ? Base64.getEncoder().encodeToString(rawTokenOrCredentials.getBytes(StandardCharsets.UTF_8))
            : rawTokenOrCredentials;
        return scheme + " " + token;
    }

    private int computeMaxInMemoryBytes(DataSize configured) {
        long bytes = configured.toBytes();
        if (bytes > Integer.MAX_VALUE) {
            log.warn("finale.max-in-memory-size={} exceeds Integer.MAX_VALUE ({}), clamping to max", bytes, Integer.MAX_VALUE);
            return Integer.MAX_VALUE;
        }
        if (bytes <= 0) {
            long fallback = DataSize.ofMegabytes(2).toBytes();
            log.warn("finale.max-in-memory-size={} is non-positive, defaulting to {} bytes", bytes, fallback);
            return (int) fallback;
        }
        return (int) bytes;
    }
}


