package com.mercaso.wms.infrastructure.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * Slack configuration properties
 * Manages WMS exception alert configurations
 */
@Data
@Component
@ConfigurationProperties(prefix = "slack")
public class SlackConfig {

    private WmsExceptionAlert wmsExceptionAlert = new WmsExceptionAlert();

    @Data
    public static class WmsExceptionAlert {

        private String webhook;
    }

    /**
     * Gets webhook URL for WMS exception alerts
     */
    public String getWmsExceptionWebhook() {
        return wmsExceptionAlert.getWebhook();
    }
}