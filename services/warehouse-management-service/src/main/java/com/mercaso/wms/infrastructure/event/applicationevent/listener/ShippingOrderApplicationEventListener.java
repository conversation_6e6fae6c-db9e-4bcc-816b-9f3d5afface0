package com.mercaso.wms.infrastructure.event.applicationevent.listener;

import com.mercaso.businessevents.dispatcher.BusinessEventDispatcher;
import com.mercaso.wms.application.dto.event.ShippingOrderSyncedToShopifyPayloadDto;
import com.mercaso.wms.application.dto.event.ShippingOrderValidatedApplicationEvent;
import com.mercaso.wms.domain.shippingorder.ShippingOrder;
import com.mercaso.wms.domain.shippingorder.ShippingOrderRepository;
import com.mercaso.wms.infrastructure.event.BusinessEventFactory;
import com.mercaso.wms.infrastructure.external.shopify.ShopifyAdaptor;
import com.mercaso.wms.infrastructure.external.shopify.dto.ShopifyLineItemUpdateDto;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;
import org.springframework.transaction.event.TransactionalEventListener;


@Slf4j
@Component
@RequiredArgsConstructor
public class ShippingOrderApplicationEventListener {

    private final ShopifyAdaptor shopifyAdaptor;

    private final ShippingOrderRepository shippingOrderRepository;

    private final BusinessEventDispatcher businessEventDispatcher;

    @Async
    @TransactionalEventListener
    public void handleShippingOrderValidated(ShippingOrderValidatedApplicationEvent applicationEvent) {
        log.info("Handling ShippingOrderValidatedApplicationEvent for shipping order ID: {}",
            applicationEvent.getPayload().getShippingOrderId());
        ShippingOrder shippingOrder = shippingOrderRepository.findById(applicationEvent.getPayload().getShippingOrderId());
        if (shippingOrder == null) {
            return;
        }
        List<ShopifyLineItemUpdateDto> lineItems = getShopifyLineItemUpdateDtos(shippingOrder);
        if (lineItems.isEmpty()) {
            log.info("No line items to update for shipping order ID: {}", applicationEvent.getPayload().getShippingOrderId());
            return;
        }
        ShippingOrderSyncedToShopifyPayloadDto payloadDto = null;
        try {
            log.info("Updating Shopify order items for shipping order number: {}, line items: {}",
                shippingOrder.getOrderNumber(),
                lineItems);
            shopifyAdaptor.updateOrderItemsQuantity(shippingOrder.getShopifyOrderId(), lineItems);
            log.info("Updated Shopify order items for shipping order number: {}", shippingOrder.getOrderNumber());
            payloadDto = buildSyncToShopifyPayload(shippingOrder, lineItems, true);
        } catch (Exception e) {
            log.error("Failed to update Shopify order items for shipping order number: {}, error: {}",
                shippingOrder.getOrderNumber(), e.getMessage(), e);
            payloadDto = buildSyncToShopifyPayload(shippingOrder, lineItems, false);
        } finally {
            if (payloadDto != null) {
                businessEventDispatcher.dispatch(BusinessEventFactory.build(payloadDto));
            }
        }
    }

    private static ShippingOrderSyncedToShopifyPayloadDto buildSyncToShopifyPayload(ShippingOrder shippingOrder,
        List<ShopifyLineItemUpdateDto> lineItems,
        boolean syncedToShopify) {
        return ShippingOrderSyncedToShopifyPayloadDto.builder()
            .shippingOrderId(shippingOrder.getId())
            .lineItems(lineItems)
            .syncedToShopify(syncedToShopify)
            .build();
    }

    private List<ShopifyLineItemUpdateDto> getShopifyLineItemUpdateDtos(ShippingOrder shippingOrder) {
        List<ShopifyLineItemUpdateDto> lineItems = new ArrayList<>();
        if (shippingOrder.getShippingOrderItems() != null) {
            shippingOrder.getShippingOrderItems().forEach(item -> {
                if (item != null && item.getValidatedQty() != null && item.getShopifyOrderItemId() != null
                    && item.getQty() > 0 && !Objects.equals(item.getQty(), item.getValidatedQty())) {
                    lineItems.add(
                        ShopifyLineItemUpdateDto.builder()
                            .id(item.getShopifyOrderItemId())
                            .quantity(item.getValidatedQty())
                            .build()
                    );
                }
            });
        }
        return lineItems;
    }

}
