package com.mercaso.wms.infrastructure.event.applicationevent.listener;

import com.mercaso.wms.application.dto.event.WmsExceptionAlertApplicationEvent;
import com.mercaso.wms.infrastructure.slackalert.WmsExceptionAlert;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.transaction.event.TransactionalEventListener;

/**
 * Listener for WMS Exception Alert Application Events
 * Handles sending Slack notifications when WMS exception alerts are published
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class WmsExceptionAlertApplicationEventListener {

    private final WmsExceptionAlert wmsExceptionAlert;

    /**
     * Handles WMS exception alert events by sending Slack notifications
     */
    @TransactionalEventListener
    public void handleWmsExceptionAlert(WmsExceptionAlertApplicationEvent event) {
        try {
            log.info("Processing WMS exception alert event: alertId={}, eventType={}",
                event.getPayload().getAlertId(),
                event.getPayload().getData().getEventType());

            wmsExceptionAlert.sendWmsExceptionAlert(event.getPayload().getData());

            log.debug("Successfully processed WMS exception alert event: alertId={}",
                event.getPayload().getAlertId());

        } catch (Exception e) {
            log.error("Failed to process WMS exception alert event: alertId={}",
                event.getPayload().getAlertId(), e);
        }
    }
} 