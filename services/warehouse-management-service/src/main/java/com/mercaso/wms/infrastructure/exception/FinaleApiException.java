package com.mercaso.wms.infrastructure.exception;

public class FinaleApiException extends RuntimeException {

    public FinaleApiException(String message) {
        super(message);
    }

    public FinaleApiException(String message, Throwable cause) {
        super(message, cause);
    }

    public static FinaleApiException wrap(String msg, Throwable cause) {
        if (cause instanceof FinaleApiException e) {
            return e;
        }
        return new FinaleApiException(msg + ": " + cause.getMessage(), cause);
    }
}