package com.mercaso.wms.infrastructure.exception;

/**
 * Exception thrown when Shopify order editing operations fail
 */
public class ShopifyOrderEditException extends RuntimeException {

    private final String orderId;
    private final String operation;

    public ShopifyOrderEditException(String message, String orderId, String operation) {
        super(message);
        this.orderId = orderId;
        this.operation = operation;
    }

    public ShopifyOrderEditException(String message, String orderId, String operation, Throwable cause) {
        super(message, cause);
        this.orderId = orderId;
        this.operation = operation;
    }

    public String getOrderId() {
        return orderId;
    }

    public String getOperation() {
        return operation;
    }
} 