package com.mercaso.wms.infrastructure.external.finale;

import com.mercaso.wms.infrastructure.exception.FinaleApiException;
import com.mercaso.wms.infrastructure.external.finale.config.FinaleConfigProperties;
import com.mercaso.wms.infrastructure.external.finale.dto.CreateShipmentRequest;
import com.mercaso.wms.infrastructure.external.finale.dto.PackShipmentRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;
import org.springframework.util.StringUtils;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.core.publisher.Mono;

/**
 * FinaleAdaptor for interacting with Finale Inventory API
 * Handles shipment filling and packing operations
 */
@Slf4j
@Component
public class FinaleAdaptor {

    private static final String SHIPMENT_API_PREFIX = "/api/shipment";
    private static final String PACK_SEGMENT = "/pack";
    private static final String ENDPOINT_FILL = "shipment.fill";
    private static final String ENDPOINT_PACK = "shipment.pack";

    @Qualifier("finaleWebClient")
    private final WebClient finaleWebClient;
    private final FinaleConfigProperties finaleConfigProperties;
    private final FinaleRateLimiter finaleRateLimiter;

    public FinaleAdaptor(@Qualifier("finaleWebClient") WebClient finaleWebClient,
                         FinaleConfigProperties finaleConfigProperties,
                         FinaleRateLimiter finaleRateLimiter) {
        this.finaleWebClient = finaleWebClient;
        this.finaleConfigProperties = finaleConfigProperties;
        this.finaleRateLimiter = finaleRateLimiter;
    }

    public long fillShipment(CreateShipmentRequest request) {
        Assert.notNull(request, "CreateShipmentRequest must not be null");
        Assert.isTrue(StringUtils.hasText(request.getPrimaryOrderUrl()), "primaryOrderUrl must not be blank");
        Assert.notNull(request.getShipmentItemList(), "shipmentItemList must not be null");
        Assert.isTrue(!request.getShipmentItemList().isEmpty(), "shipmentItemList must not be empty");
        request.getShipmentItemList().forEach(item -> {
            Assert.isTrue(StringUtils.hasText(item.getProductUrl()), "productUrl must not be blank");
            Assert.isTrue(StringUtils.hasText(item.getFacilityUrl()), "facilityUrl must not be blank");
            Assert.isTrue(item.getQuantity() > 0, "quantity must be positive");
        });

        String path = buildShipmentBasePath();
        log.info("Filling shipment for domain: {}", finaleConfigProperties.getDomain());

        try {
            finaleRateLimiter.acquire(ENDPOINT_FILL);
            ShipmentCreateResponse response = finaleWebClient.post()
                .uri(path)
                .contentType(MediaType.APPLICATION_JSON)
                .bodyValue(request)
                .retrieve()
                .bodyToMono(ShipmentCreateResponse.class)
                .onErrorResume(err -> Mono.error(FinaleApiException.wrap("Fill shipment failed", err)))
                .block();

            if (response == null || response.shipmentUrl == null || response.shipmentUrl.isBlank()) {
                throw new FinaleApiException("Fill shipment returned empty shipmentUrl");
            }

            long shipmentId = extractShipmentId(response.shipmentUrl);
            log.info("Successfully filled shipment with ID: {}", shipmentId);
            return shipmentId;

        } catch (Exception e) {
            log.error("Failed to fill shipment for domain: {}", finaleConfigProperties.getDomain(), e);
            throw e;
        }
    }

    public void packShipment(long shipmentId, PackShipmentRequest request) {
        Assert.isTrue(shipmentId > 0, "shipmentId must be positive");
        Assert.notNull(request, "PackShipmentRequest must not be null");
        Assert.isTrue(request.getCountPackages() > 0, "countPackages must be positive");
        Assert.isTrue(StringUtils.hasText(request.getTransferTo()), "transferTo must not be blank");

        String path = buildShipmentBasePath() + "/" + shipmentId + PACK_SEGMENT;
        log.info("Packing shipment: {} for domain: {}", shipmentId, finaleConfigProperties.getDomain());

        try {
            finaleRateLimiter.acquire(ENDPOINT_PACK);
            finaleWebClient.post()
                .uri(path)
                .contentType(MediaType.APPLICATION_JSON)
                .bodyValue(request)
                .retrieve()
                .toBodilessEntity()
                .onErrorResume(err -> Mono.error(FinaleApiException.wrap("Pack shipment failed", err)))
                .block();

            log.info("Successfully packed shipment: {}", shipmentId);

        } catch (Exception e) {
            log.error("Failed to pack shipment: {} for domain: {}", shipmentId, finaleConfigProperties.getDomain(), e);
            throw e;
        }
    }

    /**
     * Extracts shipment ID from the shipment URL returned by Finale API
     *
     * @param shipmentUrl the shipment URL from Finale response
     * @return the extracted shipment ID
     * @throws FinaleApiException if unable to parse shipment ID
     */
    private long extractShipmentId(String shipmentUrl) {
        try {
            int idx = shipmentUrl != null ? shipmentUrl.lastIndexOf('/') : -1;
            if (idx == -1 || idx + 1 >= shipmentUrl.length()) {
                throw new IllegalArgumentException("Invalid shipmentUrl format");
            }
            return Long.parseLong(shipmentUrl.substring(idx + 1));
        } catch (Exception ex) {
            log.warn("Unexpected shipmentUrl format: {}", shipmentUrl, ex);
            throw new FinaleApiException("Unable to parse shipmentId from shipmentUrl: " + shipmentUrl);
        }
    }

    private String buildShipmentBasePath() {
        return "/" + finaleConfigProperties.getDomain() + SHIPMENT_API_PREFIX;
    }

    

    /**
     * Response class for shipment creation API
     * Used only for deserializing the create shipment response
     */
    static final class ShipmentCreateResponse {

        public String shipmentUrl;
    }
}