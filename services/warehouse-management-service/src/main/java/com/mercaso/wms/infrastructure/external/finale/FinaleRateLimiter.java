package com.mercaso.wms.infrastructure.external.finale;

import com.mercaso.wms.infrastructure.external.finale.config.FinaleConfigProperties;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.locks.Condition;
import java.util.concurrent.locks.ReentrantLock;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * Simple in-memory per-endpoint rate limiter for Finale API.
 * Thread-safe and process-local. For cross-instance rate limits use a shared store.
 */
@Component
@Slf4j
@RequiredArgsConstructor
public class FinaleRateLimiter {

    private static final String GLOBAL_KEY = "__global__";

    private final FinaleConfigProperties config;

    private static final class WindowState {

        final AtomicInteger counter = new AtomicInteger(0);
        volatile long windowStartMillis = System.currentTimeMillis();
        volatile long periodMillis;
        volatile int limit;
        final ReentrantLock lock = new ReentrantLock();
        final Condition windowCondition = lock.newCondition();
    }

    private final Map<String, WindowState> endpointState = new ConcurrentHashMap<>();

    public boolean tryAcquire() {
        return tryAcquire(null);
    }

    /**
     * Try acquire a permit using an optional endpoint key for per-endpoint overrides.
     */
    public boolean tryAcquire(String endpointKey) {
        String key = endpointKey == null ? GLOBAL_KEY : endpointKey;
        FinaleConfigProperties.EndpointLimit override = config.getEndpointLimits().get(endpointKey);
        boolean enabled = override != null ? override.isEnabled() : config.isRateLimitEnabled();
        if (!enabled) {
            return true;
        }

        long now = System.currentTimeMillis();
        long periodMillis = resolvePeriodMillis(override);
        int limit = resolveLimit(override);

        WindowState state = endpointState.computeIfAbsent(key, k -> newState(periodMillis, limit));

        state.lock.lock();
        try {
            // Reset window if config changed or window elapsed
            refreshWindowIfNeeded(state, periodMillis, limit, now);

            if (state.counter.get() < state.limit) {
                state.counter.incrementAndGet();
                return true;
            }
            return false;
        } finally {
            state.lock.unlock();
        }
    }

    /**
     * Blocking acquire. Sleeps until a permit is available for the given endpoint.
     */
    public void acquire(String endpointKey) {
        String key = endpointKey == null ? GLOBAL_KEY : endpointKey;
        FinaleConfigProperties.EndpointLimit override = config.getEndpointLimits().get(endpointKey);
        long periodMillis = resolvePeriodMillis(override);
        int limit = resolveLimit(override);

        WindowState state = endpointState.computeIfAbsent(key, k -> newState(periodMillis, limit));

        state.lock.lock();
        try {
            for (; ; ) {
                long now = System.currentTimeMillis();
                refreshWindowIfNeeded(state, periodMillis, limit, now);

                if (state.counter.get() < state.limit) {
                    state.counter.incrementAndGet();
                    return;
                }

                long waitMs = (state.windowStartMillis + state.periodMillis) - now;
                if (waitMs <= 0) {
                    continue; // loop will reset window
                }
                try {
                    long nanosRequested = waitMs * 1_000_000L;
                    long nanosRemaining = state.windowCondition.awaitNanos(nanosRequested);
                    // If nanosRemaining > 0, we were signalled before timeout; either way, the loop re-checks conditions
                } catch (InterruptedException ie) {
                    Thread.currentThread().interrupt();
                    return;
                }
            }
        } finally {
            state.lock.unlock();
        }
    }

    // No external remaining-window method is needed at the moment; the limiter blocks until available

    private long resolvePeriodMillis(FinaleConfigProperties.EndpointLimit override) {
        return (override != null && override.getRateLimitRefreshPeriod() != null)
            ? override.getRateLimitRefreshPeriod().toMillis() : config.getRateLimitRefreshPeriod().toMillis();
    }

    private int resolveLimit(FinaleConfigProperties.EndpointLimit override) {
        return (override != null && override.getRateLimitForPeriod() != null)
            ? override.getRateLimitForPeriod() : config.getRateLimitForPeriod();
    }

    private WindowState newState(long periodMillis, int limit) {
        WindowState ws = new WindowState();
        ws.periodMillis = periodMillis;
        ws.limit = limit;
        return ws;
    }

    private void refreshWindowIfNeeded(WindowState state, long periodMillis, int limit, long now) {
        if (state.periodMillis != periodMillis || now - state.windowStartMillis >= state.periodMillis) {
            state.periodMillis = periodMillis;
            state.limit = limit;
            state.windowStartMillis = now;
            state.counter.set(0);
            state.windowCondition.signalAll();
        }
    }
}
