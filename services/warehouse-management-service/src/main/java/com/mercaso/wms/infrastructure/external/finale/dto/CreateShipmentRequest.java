package com.mercaso.wms.infrastructure.external.finale.dto;

import java.util.List;
import lombok.Data;

/**
 * Request DTO for filling a shipment in Finale Inventory system
 * Maps to the JSON payload for POST /api/shipment endpoint
 */
@Data
public class CreateShipmentRequest {

    /**
     * Primary order URL in Finale system
     * e.g. "/mercasosandbox/api/order/M-2818"
     */
    private String primaryOrderUrl;

    /**
     * List of items to be shipped
     */
    private List<ShipmentItem> shipmentItemList;

    /**
     * Type of shipment
     * e.g. "SALES_SHIPMENT"
     */
    private String shipmentTypeId;

    /**
     * Shipment URL (can be null for new shipments to be filled)
     */
    private String shipmentUrl;
}