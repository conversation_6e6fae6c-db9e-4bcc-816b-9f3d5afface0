package com.mercaso.wms.infrastructure.external.finale.dto;

import java.time.OffsetDateTime;
import lombok.Data;

/**
 * Request DTO for packing a shipment in Finale Inventory system
 * Maps to the JSON payload for POST /api/shipment/{shipmentId}/pack endpoint
 */
@Data
public class PackShipmentRequest {

    /**
     * Number of packages to create
     */
    private int countPackages;

    /**
     * Date and time when packing occurs
     * e.g. "2025-08-01T04:00:00Z" (recommended to use timezone-aware format)
     */
    private OffsetDateTime packDate;

    /**
     * Facility URL to transfer to
     * e.g. "/mercasosandbox/api/facility/108602"
     */
    private String transferTo;
}