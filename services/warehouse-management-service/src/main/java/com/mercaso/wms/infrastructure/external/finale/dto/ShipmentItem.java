package com.mercaso.wms.infrastructure.external.finale.dto;

import lombok.Data;

/**
 * DTO representing a single item to be filled in a shipment
 * Maps to the shipmentItemList array elements in CreateShipmentRequest
 */
@Data
public class ShipmentItem {

    /**
     * Product URL in Finale system
     * e.g. "/mercasosandbox/api/product/SP038013"
     */
    private String productUrl;

    /**
     * Facility URL in Finale system
     * e.g. "/mercasosandbox/api/facility/103534"
     */
    private String facilityUrl;

    /**
     * Quantity of the item to be filled in shipment
     */
    private int quantity;
}