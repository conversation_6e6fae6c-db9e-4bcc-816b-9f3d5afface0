package com.mercaso.wms.infrastructure.external.shopify.dto;

import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ShopifyGraphQLResponse {

    private ShopifyGraphQLData data;
    private List<UserError> userErrors;

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ShopifyGraphQLData {

        private OrderEditBegin orderEditBegin;
        private OrderEditSetQuantity orderEditSetQuantity;
        private OrderEditCommit orderEditCommit;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class OrderEditBegin {

        private CalculatedOrder calculatedOrder;
        private List<UserError> userErrors;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class OrderEditSetQuantity {

        private CalculatedOrder calculatedOrder;
        private List<UserError> userErrors;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class OrderEditCommit {

        private ShopifyOrder order;
        private List<UserError> userErrors;
        private String message;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class CalculatedOrder {

        private String id;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ShopifyOrder {

        private String id;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class UserError {

        private Object field;
        private String message;
    }
} 