package com.mercaso.wms.infrastructure.external.slack;

import com.mercaso.wms.infrastructure.external.slack.dto.SlackMessageDto;
import com.mercaso.wms.infrastructure.utils.HttpClientUtils;
import java.util.HashMap;
import java.util.Map;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * Generic Slack adapter for sending messages to Slack channels
 * Provides common functionality for all Slack integrations
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class WmsSlackAdaptor {

    /**
     * Sends a simple text message to Slack
     *
     * @param webhookUrl the Slack webhook URL
     * @param message the text message to send
     * @return true if message was sent successfully, false otherwise
     */
    public boolean sendMessage(String webhookUrl, String message) {
        return sendMessage(webhookUrl, SlackMessageDto.builder()
            .text(message)
            .build());
    }

    /**
     * Sends a formatted message to <PERSON>lack
     *
     * @param webhookUrl the Slack webhook URL
     * @param messageDto the message DTO containing text and formatting
     * @return true if message was sent successfully, false otherwise
     */
    public boolean sendMessage(String webhookUrl, SlackMessageDto messageDto) {
        if (webhookUrl == null || webhookUrl.trim().isEmpty()) {
            log.warn("Slack webhook URL is not configured");
            return false;
        }

        if (messageDto == null || messageDto.getText() == null || messageDto.getText().trim().isEmpty()) {
            log.warn("Slack message is empty or null");
            return false;
        }

        try {
            Map<String, Object> payload = buildSlackPayload(messageDto);

            HttpClientUtils.executePostRequest(
                webhookUrl,
                payload,
                new HashMap<>(),
                String.class
            );

            log.info("Slack message sent successfully to webhook: {}", maskWebhookUrl(webhookUrl));
            return true;

        } catch (Exception e) {
            log.error("Failed to send Slack message to webhook: {}", maskWebhookUrl(webhookUrl), e);
            return false;
        }
    }

    /**
     * Sends a message with retry mechanism
     *
     * @param webhookUrl the Slack webhook URL
     * @param messageDto the message DTO
     * @param retryCount number of retry attempts
     * @return true if message was sent successfully, false otherwise
     */
    public boolean sendMessageWithRetry(String webhookUrl, SlackMessageDto messageDto, int retryCount) {
        for (int attempt = 1; attempt <= retryCount + 1; attempt++) {
            boolean success = sendMessage(webhookUrl, messageDto);
            if (success) {
                return true;
            }

            if (attempt <= retryCount) {
                log.warn("Failed to send Slack message, attempt {}/{}, retrying...", attempt, retryCount + 1);
                try {
                    Thread.sleep(1000L * attempt); // Exponential backoff
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                    log.error("Interrupted while waiting for retry", e);
                    break;
                }
            }
        }
        return false;
    }

    /**
     * Builds the Slack payload from message DTO
     */
    private Map<String, Object> buildSlackPayload(SlackMessageDto messageDto) {
        Map<String, Object> payload = new HashMap<>();
        payload.put("text", messageDto.getText());

        if (messageDto.getUsername() != null) {
            payload.put("username", messageDto.getUsername());
        }

        if (messageDto.getIconEmoji() != null) {
            payload.put("icon_emoji", messageDto.getIconEmoji());
        }

        if (messageDto.getChannel() != null) {
            payload.put("channel", messageDto.getChannel());
        }

        return payload;
    }

    /**
     * Masks the webhook URL for logging purposes
     */
    private String maskWebhookUrl(String webhookUrl) {
        if (webhookUrl == null || webhookUrl.length() < 20) {
            return "***";
        }
        return webhookUrl.substring(0, 20) + "***";
    }
} 