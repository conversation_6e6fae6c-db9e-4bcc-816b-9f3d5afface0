package com.mercaso.wms.infrastructure.external.slack.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Data Transfer Object for Slack messages
 * Contains all necessary fields for sending formatted messages to Slack
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SlackMessageDto {

    /**
     * The main text content of the message (required)
     */
    private String text;

    /**
     * Custom username for the bot (optional)
     */
    private String username;

    /**
     * Icon emoji for the bot (optional)
     * Example: ":robot_face:", ":warning:"
     */
    private String iconEmoji;

    /**
     * Target channel (optional, overrides webhook default)
     * Example: "#general", "@username"
     */
    private String channel;

    /**
     * Creates a simple text message
     *
     * @param text the message text
     * @return SlackMessageDto with basic text
     */
    public static SlackMessageDto of(String text) {
        return SlackMessageDto.builder()
            .text(text)
            .build();
    }

    /**
     * Creates a message with custom bot appearance
     *
     * @param text the message text
     * @param username the bot username
     * @param iconEmoji the bot icon emoji
     * @return SlackMessageDto with custom appearance
     */
    public static SlackMessageDto of(String text, String username, String iconEmoji) {
        return SlackMessageDto.builder()
            .text(text)
            .username(username)
            .iconEmoji(iconEmoji)
            .build();
    }
} 