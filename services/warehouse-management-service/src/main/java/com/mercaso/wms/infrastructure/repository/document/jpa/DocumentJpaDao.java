package com.mercaso.wms.infrastructure.repository.document.jpa;

import com.mercaso.wms.domain.document.enums.DocumentType;
import com.mercaso.wms.infrastructure.repository.document.jpa.dataobject.DocumentDo;
import java.util.List;
import java.util.UUID;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

public interface DocumentJpaDao extends JpaRepository<DocumentDo, UUID> {

    @Query(
        "SELECT d FROM DocumentDo d WHERE d.entityId = :entityId AND d.entityName = :entityName "
            + "AND (:documentTypes IS NULL OR d.documentType in :documentTypes) order by d.createdAt asc")
    List<DocumentDo> findByEntityIdAndEntityNameAndDocumentTypes(@Param("entityId") UUID entityId,
        @Param("entityName") String entityName,
        @Param("documentTypes") List<DocumentType> documentTypes);

    @Query("SELECT d FROM DocumentDo d WHERE d.entityId IN :entityIds")
    List<DocumentDo> findByEntityIdIn(@Param("entityIds") List<UUID> entityIds);
} 