package com.mercaso.wms.infrastructure.repository.document.jpa.dataobject;

import com.mercaso.wms.domain.document.enums.DocumentType;
import com.mercaso.wms.infrastructure.repository.BaseDo;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.Table;
import java.util.UUID;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.SQLRestriction;

@Entity
@Data
@EqualsAndHashCode(callSuper = false)
@ToString(callSuper = true)
@Table(name = "documents")
@SQLDelete(sql = "update documents set deleted_at = current_timestamp where id = ? and updated_at = ?")
@SQLRestriction("deleted_at is null")
public class DocumentDo extends BaseDo {

    @Column(name = "entity_id", nullable = false)
    private UUID entityId;

    @Column(name = "entity_name", nullable = false)
    private String entityName;

    @Enumerated(value = EnumType.STRING)
    @Column(name = "document_type", nullable = false)
    private DocumentType documentType;

    @Column(name = "created_user_name")
    private String createdUserName;

    @Column(name = "file_name", nullable = false)
    private String fileName;
} 