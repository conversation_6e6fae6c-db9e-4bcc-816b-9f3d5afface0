package com.mercaso.wms.infrastructure.repository.document.jpa.mapper;

import com.mercaso.wms.domain.document.Document;
import com.mercaso.wms.infrastructure.repository.BaseDoMapper;
import com.mercaso.wms.infrastructure.repository.document.jpa.dataobject.DocumentDo;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;

@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface DocumentDoMapper extends BaseDoMapper<DocumentDo, Document> {

}