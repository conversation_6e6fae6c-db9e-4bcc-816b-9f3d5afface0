package com.mercaso.wms.infrastructure.slackalert;

import com.mercaso.businessevents.dispatcher.BusinessEventDispatcher;
import com.mercaso.wms.application.dto.event.WmsExceptionAlertPayloadDto;
import com.mercaso.wms.domain.batch.Batch;
import com.mercaso.wms.domain.batch.BatchRepository;
import com.mercaso.wms.domain.pickingtask.PickingTask;
import com.mercaso.wms.domain.shippingorder.ShippingOrder;
import com.mercaso.wms.infrastructure.alert.dto.WmsExceptionAlertDto;
import com.mercaso.wms.infrastructure.event.BusinessEventFactory;
import com.mercaso.wms.infrastructure.event.applicationevent.dispatcher.ApplicationEventDispatcher;
import com.mercaso.wms.infrastructure.utils.DateUtils;
import java.time.LocalDate;
import java.time.ZonedDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

/**
 * WMS Exception Alert Service
 * Detects WMS exception scenarios and publishes alert events
 * Currently focused on ORDER_AFTER_BATCH_CREATION but designed for easy extension
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class WmsExceptionAlertService {

    private final BatchRepository batchRepository;
    private final BusinessEventDispatcher businessEventDispatcher;
    private final ApplicationEventDispatcher applicationEventDispatcher;

    /**
     * Checks if a new order is created after batch was created for the same delivery date
     * This scenario is considered an exception as it may require batch re-planning
     */
    @Async
    public void checkOrderAfterBatchCreation(ShippingOrder shippingOrder) {
        if (shippingOrder.getBatchId() != null) {
            return; // Only check for newly created orders
        }

        LocalDate orderDeliveryDate = shippingOrder.getDeliveryDate();
        if (orderDeliveryDate == null) {
            return; // Cannot determine delivery date
        }

        try {
            List<Batch> existingBatches = batchRepository.findActiveByDeliveryDate(orderDeliveryDate);
            if (!existingBatches.isEmpty()) {
                Batch batch = existingBatches.getFirst();
                WmsExceptionAlertDto alertDto = WmsExceptionAlertDto.createOrderAfterBatchAlert(
                    shippingOrder.getOrderNumber(),
                    shippingOrder.getId(),
                    batch.getNumber(),
                    batch.getId(),
                    orderDeliveryDate
                );

                publishAlert(alertDto);

                log.warn("Order after batch creation detected: Order {} for delivery date {} (Batch: {})",
                    shippingOrder.getOrderNumber(), orderDeliveryDate, batch.getNumber());
            }
        } catch (Exception e) {
            log.error("Error checking order after batch creation for order: {}",
                shippingOrder.getOrderNumber(), e);
        }
    }

    /**
     * Publishes WMS exception alert as business event and application event
     * Business event: for external systems and audit trail
     * Application event: for internal processing (Slack notification)
     */
    private void publishAlert(WmsExceptionAlertDto alertDto) {
        try {
            // Create event payload
            WmsExceptionAlertPayloadDto payload = WmsExceptionAlertPayloadDto.create(alertDto);

            // Build event instance once and reuse it
            var event = BusinessEventFactory.build(payload);
            if (event == null) {
                log.error("Failed to create business event for WMS exception alert: eventType={}, entityId={}",
                    alertDto.getEventType(), alertDto.getRelatedEntityId());
                return;
            }

            // Publish business event for external systems and audit trail
            businessEventDispatcher.dispatch(event);

            // Publish application event for internal processing (Slack notification)
            applicationEventDispatcher.publishEvent(event);

            log.info("Published WMS exception alert: alertId={}, eventType={}, entityId={}",
                payload.getAlertId(),
                alertDto.getEventType(),
                alertDto.getRelatedEntityId());

        } catch (Exception e) {
            log.error("Failed to publish WMS exception alert: eventType={}, entityId={}",
                alertDto.getEventType(), alertDto.getRelatedEntityId(), e);
        }
    }

    /**
     * Checks for incomplete picking tasks after 8PM LA time
     * Sends alert if picking tasks are not completed and current time is after 8PM LA
     */
    @Async
    public void checkPickingTasksIncompleteAfter8PM(UUID batchId, String batchNumber,
        String vendorName, String source,
        String deliveryDate, List<PickingTask> incompleteTasks) {
        try {
            // Check if current LA time is after 8PM
            if (!isAfter8PMLosAngeles()) {
                log.info("Current LA time is before 8PM, no alert needed for batch: {}", batchId);
                return;
            }

            if (incompleteTasks == null || incompleteTasks.isEmpty()) {
                log.info("No incomplete picking tasks found for batch: {}", batchId);
                return;
            }

            // Build incomplete task details
            List<Map<String, Object>> incompleteTaskDetails = incompleteTasks.stream()
                .map(task -> {
                    Map<String, Object> taskMap = new HashMap<>();
                    taskMap.put("taskId", task.getId() != null ? task.getId().toString() : "N/A");
                    taskMap.put("taskNumber", task.getNumber() != null ? task.getNumber() : "N/A");
                    taskMap.put("status", task.getStatus() != null ? task.getStatus().name() : "UNKNOWN");
                    
                    // Safe handling of picking task items
                    var pickingTaskItems = task.getPickingTaskItems();
                    if (pickingTaskItems != null) {
                        taskMap.put("totalItems", pickingTaskItems.size());
                        taskMap.put("incompleteItems", pickingTaskItems.stream()
                            .filter(item -> item != null && 
                                item.getPickedQty() != null && 
                                item.getExpectQty() != null && 
                                item.getPickedQty() < item.getExpectQty())
                            .count());
                    } else {
                        taskMap.put("totalItems", 0);
                        taskMap.put("incompleteItems", 0);
                    }
                    
                    taskMap.put("source", task.getSource() != null ? task.getSource().name() : "UNKNOWN");
                    taskMap.put("createdAt", task.getCreatedAt() != null ? task.getCreatedAt().toString() : "N/A");
                    return taskMap;
                })
                .collect(Collectors.toList());

            WmsExceptionAlertDto alertDto = WmsExceptionAlertDto.createPickingTasksIncompleteAlert(
                batchId,
                batchNumber,
                vendorName,
                source,
                deliveryDate,
                incompleteTaskDetails
            );

            publishAlert(alertDto);

            log.warn("Picking tasks incomplete after 8PM alert sent: Batch {} has {} incomplete tasks for vendor {}",
                batchNumber != null ? batchNumber : batchId, incompleteTasks.size(), vendorName);

        } catch (Exception e) {
            log.error("Error sending picking tasks incomplete alert for batch: {}", batchId, e);
        }
    }

    /**
     * Checks if current time in Los Angeles is after 8:00 PM
     */
    private boolean isAfter8PMLosAngeles() {
        ZonedDateTime laTime = ZonedDateTime.now(DateUtils.LA_ZONE);
        int hour = laTime.getHour();
        return hour >= 20; // 20:00 (8PM) and later
    }

}