package com.mercaso.wms.interfaces;

import com.mercaso.wms.application.command.crossdock.AssignNextSequenceCommand;
import com.mercaso.wms.application.dto.CrossDockItemDto;
import com.mercaso.wms.application.service.CrossDockTaskItemService;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@Validated
@RestController
@RequestMapping("/cross-dock-task-items")
@RequiredArgsConstructor
public class CrossDockTaskItemResource {

    private final CrossDockTaskItemService crossDockTaskItemService;

    @PreAuthorize("hasAuthority('wms:write:cross-dock-tasks')")
    @PutMapping("/assign-next-sequence")
    public CrossDockItemDto assignNextSequence(@Valid @RequestBody AssignNextSequenceCommand command) {
        return crossDockTaskItemService.assignNextSequence(command.getShippingOrderItemId());
    }
}