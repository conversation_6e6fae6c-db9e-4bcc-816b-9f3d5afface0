package com.mercaso.wms.interfaces.query;

import com.mercaso.wms.application.dto.document.DocumentDto;
import com.mercaso.wms.application.queryservice.DocumentQueryService;
import com.mercaso.wms.domain.businessevent.EntityEnums;
import com.mercaso.wms.domain.document.enums.DocumentType;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import java.util.List;
import java.util.UUID;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@Tag(name = "Documents")
@Slf4j
@Validated
@RestController
@RequestMapping("/query/documents")
@RequiredArgsConstructor
public class QueryDocumentResource {

    private final DocumentQueryService documentQueryService;

    @PreAuthorize("hasAuthority('wms:read:documents')")
    @GetMapping("/{entityId}/{entityName}")
    public List<DocumentDto> findDocuments(@PathVariable @NotNull UUID entityId,
        @PathVariable @NotNull @Valid EntityEnums entityName,
        @RequestParam(required = false) List<DocumentType> documentTypes) {
        return documentQueryService.findDocumentsBy(entityId, entityName, documentTypes);
    }

    @PreAuthorize("hasAuthority('wms:read:documents')")
    @GetMapping("/entity-ids")
    public List<DocumentDto> findDocumentsByEntityIds(@RequestParam @NotNull List<UUID> entityIds) {
        return documentQueryService.findDocumentsByEntityIds(entityIds);
    }
} 