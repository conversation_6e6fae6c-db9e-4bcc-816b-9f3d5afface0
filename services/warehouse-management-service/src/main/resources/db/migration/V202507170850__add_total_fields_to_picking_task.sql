ALTER TABLE picking_task
    ADD COLUMN IF NOT EXISTS total_qty        INTEGER DEFAULT 0,
    ADD COLUMN IF NOT EXISTS total_lines      INTEGER DEFAULT 0,
    ADD COLUMN IF NOT EXISTS total_picked_qty INTEGER DEFAULT 0;

COMMENT ON COLUMN picking_task.total_qty IS 'Total quantity of all items in the picking task';
COMMENT ON COLUMN picking_task.total_lines IS 'Total number of lines/items in the picking task';
COMMENT ON COLUMN picking_task.total_picked_qty IS 'Total quantity that has been picked';

CREATE INDEX IF NOT EXISTS picking_task_total_qty_idx ON picking_task (total_qty);
CREATE INDEX IF NOT EXISTS picking_task_total_lines_idx ON picking_task (total_lines);
CREATE INDEX IF NOT EXISTS picking_task_total_picked_qty_idx ON picking_task (total_picked_qty); 