CREATE TABLE documents
(
    id                UUID         NOT NULL PRIMARY KEY,
    entity_id         UUID         NOT NULL,
    entity_name       VARCHAR(50)  NOT NULL,
    document_type     VARCHAR(50)  NOT NULL,
    created_user_name VARCHAR(100),
    file_name         VARCHAR(255) NOT NULL,
    created_at        TIMESTAMP    NOT NULL DEFAULT NOW(),
    created_by        VARCHAR(50),
    updated_at        TIMESTAMP    NOT NULL DEFAULT NOW(),
    updated_by        VARCHAR(50),
    deleted_at        TIMESTAMP,
    deleted_by        VARCHAR(50)
);

comment on table documents is 'Table to store documents related to entities in the warehouse management system';
comment on column documents.id is 'Unique identifier for the document';
comment on column documents.entity_id is 'Identifier of the entity to which the document belongs';
comment on column documents.entity_name is 'Name of the entity to which the document belongs';
comment on column documents.document_type is 'Type of the document';
comment on column documents.created_user_name is 'Name of the user who created the document';
comment on column documents.file_name is 'Name of the file associated with the document';

CREATE INDEX idx_documents_entity_id_entity_name ON documents (entity_id, entity_name);
CREATE INDEX idx_documents_document_type ON documents (document_type);
CREATE INDEX idx_documents_entity_id_name_type ON documents (entity_id, entity_name, document_type);