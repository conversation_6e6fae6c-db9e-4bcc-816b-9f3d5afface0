ALTER TABLE shipping_order
    ADD COLUMN IF NOT EXISTS truck_number     VARCHAR(30),
    ADD COLUMN IF NOT EXISTS driver_user_name VARCHAR(100),
    ADD COLUMN IF NOT EXISTS driver_user_id   uuid;

ALTER TABLE shipping_order_items
    ADD COLUMN IF NOT EXISTS high_value_item boolean default false;

create index if not exists idx_shipping_order_truck_number on shipping_order (truck_number);
create index if not exists idx_shipping_order_driver_user_id on shipping_order (driver_user_id);
create index if not exists idx_shipping_order_items_high_value_item on shipping_order_items (high_value_item);

comment on column shipping_order.truck_number is 'The truck number for the shipping order';
comment on column shipping_order.driver_user_name is 'The name of the driver assigned to the shipping order';
comment on column shipping_order.driver_user_id is 'The user ID of the driver assigned to the shipping order';
comment on column shipping_order_items.high_value_item is 'Indicates if the item is a high-value item, used for special handling or tracking';