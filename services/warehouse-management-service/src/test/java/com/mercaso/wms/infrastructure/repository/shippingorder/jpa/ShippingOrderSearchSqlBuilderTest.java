package com.mercaso.wms.infrastructure.repository.shippingorder.jpa;

import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

import com.mercaso.wms.application.query.ShippingOrderQuery;
import com.mercaso.wms.application.query.SortType;
import org.junit.jupiter.api.Test;
import org.springframework.data.domain.PageRequest;
import org.springframework.jdbc.core.namedparam.MapSqlParameterSource;

class ShippingOrderSearchSqlBuilderTest {

    @Test
    void buildSelectSql_WithBasicQuery_ShouldGenerateValidSql() {
        // Given
        ShippingOrderQuery query = ShippingOrderQuery.builder()
            .orderNumber("SO-001")
            .deliveryDate("2025-01-15")
            .breakdownName("Breakdown A")
            .statuses(java.util.List.of("OPEN", "PICKED"))
            .sort(SortType.CREATED_AT_DESC)
            .page(1)
            .pageSize(20)
            .build();

        PageRequest pageable = PageRequest.of(0, 20);
        MapSqlParameterSource params = new MapSqlParameterSource();

        ShippingOrderSearchSqlBuilder sqlBuilder = ShippingOrderSearchSqlBuilder.builder()
            .query(query)
            .pageable(pageable)
            .params(params)
            .build();

        // When
        String selectSql = sqlBuilder.buildSelectSql();

        // Then
        assertNotNull(selectSql);
        assertTrue(selectSql.contains("SELECT"));
        assertTrue(selectSql.contains("FROM shipping_order so"));
        assertTrue(selectSql.contains("LEFT JOIN location l"));
        assertTrue(selectSql.contains("WHERE so.deleted_at IS NULL"));
        assertTrue(selectSql.contains("AND so.order_number = :orderNumber"));
        assertTrue(selectSql.contains("AND so.delivery_date = :deliveryDate"));
        assertTrue(selectSql.contains("AND l.name ILIKE :breakdownName"));
        assertTrue(selectSql.contains("AND so.status IN (:statuses)"));
        assertTrue(selectSql.contains("LIMIT :limit OFFSET :offset"));
    }

    @Test
    void buildCountSql_WithBasicQuery_ShouldGenerateValidSql() {
        // Given
        ShippingOrderQuery query = ShippingOrderQuery.builder()
            .orderNumber("SO-001")
            .build();

        MapSqlParameterSource params = new MapSqlParameterSource();

        ShippingOrderSearchSqlBuilder sqlBuilder = ShippingOrderSearchSqlBuilder.builder()
            .query(query)
            .params(params)
            .build();

        // When
        String countSql = sqlBuilder.buildCountSql();

        // Then
        assertNotNull(countSql);
        assertTrue(countSql.contains("SELECT COUNT(*)"));
        assertTrue(countSql.contains("FROM shipping_order so"));
        assertTrue(countSql.contains("LEFT JOIN location l"));
        assertTrue(countSql.contains("WHERE so.deleted_at IS NULL"));
        assertTrue(countSql.contains("AND so.order_number = :orderNumber"));
    }

    @Test
    void buildSelectSql_WithNullQuery_ShouldUseDefaultSort() {
        // Given
        ShippingOrderQuery query = ShippingOrderQuery.builder()
            .orderNumber("SO-001")
            .build(); // sort is null

        PageRequest pageable = PageRequest.of(0, 20);
        MapSqlParameterSource params = new MapSqlParameterSource();

        ShippingOrderSearchSqlBuilder sqlBuilder = ShippingOrderSearchSqlBuilder.builder()
            .query(query)
            .pageable(pageable)
            .params(params)
            .build();

        // When
        String selectSql = sqlBuilder.buildSelectSql();

        // Then
        assertNotNull(selectSql);
        assertTrue(selectSql.contains("ORDER BY so.created_at DESC"));
    }
} 