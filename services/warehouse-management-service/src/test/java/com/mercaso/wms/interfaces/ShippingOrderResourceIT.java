package com.mercaso.wms.interfaces;

import static com.mercaso.wms.utils.MockDataUtils.buildShopifyOrderDto;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

import com.mercaso.wms.AbstractIT;
import com.mercaso.wms.application.command.shippingorder.ShippingOrderHighValueItemValidateCommand;
import com.mercaso.wms.application.command.shippingorder.ShippingOrderValidateCommand;
import com.mercaso.wms.application.command.shippingorder.UpdateShippingOrderItemCommand;
import com.mercaso.wms.application.dto.shippingorder.ShippingOrderDto;
import com.mercaso.wms.application.dto.shippingorder.ShippingOrderItemDto;
import com.mercaso.wms.application.dto.shopify.ShopifyOrderDto;
import com.mercaso.wms.delivery.application.dto.deliveryorder.DeliveryOrderRouteInfoDto;
import com.mercaso.wms.domain.shippingorder.ShippingOrder;
import com.mercaso.wms.domain.shippingorder.enums.ShippingOrderStatus;
import com.mercaso.wms.domain.shippingorderitem.ShippingOrderItem;
import com.mercaso.wms.utils.ShippingOrderResourceApi;
import com.mercaso.wms.utils.ShopifyWebhookResourceApi;
import java.time.LocalDate;
import java.util.List;
import java.util.UUID;
import org.apache.commons.lang3.RandomStringUtils;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

class ShippingOrderResourceIT extends AbstractIT {

    @Autowired
    private ShippingOrderResourceApi shippingOrderResourceApi;
    @Autowired
    private ShopifyWebhookResourceApi shopifyWebhookResourceApi;

    @Test
    void pickedShippingOrdersExport_Success() {
        // Arrange
        String deliveryDate = "2024-03-20";

        // Act
        byte[] result = shippingOrderResourceApi.pickedShippingOrdersExport(deliveryDate);

        // Assert
        assertNotNull(result);
    }

    @Test
    void pickedShippingOrdersExport_WithFutureDate_Success() {
        // Arrange
        String deliveryDate = "2024-12-31";

        // Act
        byte[] result = shippingOrderResourceApi.pickedShippingOrdersExport(deliveryDate);

        // Assert
        assertNotNull(result);
    }

    @Test
    void pickedShippingOrdersExport_WithPastDate_Success() {
        // Arrange
        String deliveryDate = "2024-01-01";

        // Act
        byte[] result = shippingOrderResourceApi.pickedShippingOrdersExport(deliveryDate);

        // Assert
        assertNotNull(result);
    }

    @Test
    void pickedShippingOrdersExport_WithTodayDate_Success() {
        // Arrange
        String deliveryDate = java.time.LocalDate.now().toString();

        // Act
        byte[] result = shippingOrderResourceApi.pickedShippingOrdersExport(deliveryDate);

        // Assert
        assertNotNull(result);
    }

    @Test
    void when_shipping_order_validate_then_success() throws Exception {
        ShopifyOrderDto shopifyOrderDto = buildShopifyOrderDto();
        shopifyOrderDto.setTags(LocalDate.now().plusDays(20) + ", SELLER_Mercaso");

        shopifyWebhookResourceApi.webhook(shopifyOrderDto);

        ShippingOrder shippingOrder = shippingOrderRepository.findByNumber(shopifyOrderDto.getName());
        shippingOrder.setStatus(ShippingOrderStatus.PICKED);
        shippingOrderRepository.save(shippingOrder);

        ShippingOrderValidateCommand command = new ShippingOrderValidateCommand();
        command.setPalletCount(10);
        command.setUpdateShippingOrderItemCommands(
            shippingOrder.getShippingOrderItems().stream()
                .map(item -> new UpdateShippingOrderItemCommand(item.getId(), item.getShippingOrderId(), item.getQty(), null))
                .toList()
        );
        ShippingOrderDto shippingOrderDto = shippingOrderResourceApi.validateShippingOrder(
            shippingOrder.getId(),
            command
        );

        assertEquals(ShippingOrderStatus.VALIDATED, shippingOrderDto.getStatus());
        assertEquals(command.getPalletCount(), shippingOrderDto.getPalletCount());
        shippingOrderDto.getShippingOrderItems().forEach(
            item -> assertEquals(item.getQty(), item.getValidatedQty())
        );
    }

    @Test
    void when_shipping_order_validate_with_high_value_items_then_success() throws Exception {
        ShopifyOrderDto shopifyOrderDto = buildShopifyOrderDto();
        shopifyOrderDto.setTags(LocalDate.now().plusDays(20) + ", SELLER_Mercaso");

        shopifyWebhookResourceApi.webhook(shopifyOrderDto);

        ShippingOrder shippingOrder = shippingOrderRepository.findByNumber(shopifyOrderDto.getName());
        shippingOrder.setStatus(ShippingOrderStatus.PICKED);
        shippingOrder.getShippingOrderItems().getFirst().setHighValueItem(true);
        shippingOrderRepository.save(shippingOrder);

        ShippingOrderHighValueItemValidateCommand command = new ShippingOrderHighValueItemValidateCommand();
        command.setUpdateShippingOrderItemCommands(
            shippingOrder.getShippingOrderItems().stream()
                .filter(ShippingOrderItem::isHighValueItem)
                .map(item -> new UpdateShippingOrderItemCommand(item.getId(), item.getShippingOrderId(), item.getQty(), null))
                .toList()
        );
        List<ShippingOrderDto> shippingOrderDtos = shippingOrderResourceApi.validateHighValueItems(command);

        assertNotNull(shippingOrderDtos);
        assertEquals(1, shippingOrderDtos.size());
        shippingOrderDtos.getFirst().getShippingOrderItems().stream().filter(ShippingOrderItemDto::isHighValueItem)
            .forEach(item -> assertEquals(item.getQty(), item.getValidatedQty()));
    }

    @Test
    void syncRouteInfo_Success() throws Exception {
        LocalDate deliveryDate = LocalDate.now().plusDays(20);

        ShopifyOrderDto shopifyOrderDto = buildShopifyOrderDto();
        shopifyOrderDto.setTags(deliveryDate + ", SELLER_Mercaso");

        shopifyWebhookResourceApi.webhook(shopifyOrderDto);

        DeliveryOrderRouteInfoDto orderRouteInfoDto = DeliveryOrderRouteInfoDto.builder()
            .orderNumber("M-" + shopifyOrderDto.getName())
            .driverUserName(RandomStringUtils.randomAlphabetic(10))
            .driverUserId(
                UUID.randomUUID())
            .truckNumber(RandomStringUtils.randomAlphabetic(10))
            .build();
        when(deliveryAdaptor.buildDeliveryTask(any())).thenReturn(List.of(orderRouteInfoDto));

        // Act
        shippingOrderResourceApi.syncRouteInfo(deliveryDate);

        ShippingOrder shippingOrder = shippingOrderRepository.findByNumber(shopifyOrderDto.getName());

        assertNotNull(shippingOrder);
        assertEquals(deliveryDate, shippingOrder.getDeliveryDate());
        assertEquals(orderRouteInfoDto.getDriverUserId(), shippingOrder.getDriverUserId());
        assertEquals(orderRouteInfoDto.getTruckNumber(), shippingOrder.getTruckNumber());
        assertEquals(orderRouteInfoDto.getDriverUserName(), shippingOrder.getDriverUserName());

    }
} 